// // tools/tsx/modifyComponent.ts
// import { Project, SyntaxKind } from "ts-morph";
// import * as fs from "fs";

// type Args = {
//   filePath: string;
//   componentName: string;
//   newProp?: { name: string; value: string };
//   insertChildJsx?: string;
// };

// export async function modifyComponent({
//   filePath,
//   componentName,
//   newProp,
//   insertChildJsx,
//   dryRun = false,
// }: Args & { dryRun?: boolean }) {
//   const project = new Project({ tsConfigFilePath: "tsconfig.json" });
//   const sourceFile = project.addSourceFileAtPath(filePath);
//   const component = sourceFile.getFunctionOrThrow(componentName);

//   const returnStatement = component
//     .getDescendantsOfKind(SyntaxKind.ReturnStatement)
//     .at(0);

//   if (!returnStatement) {
//     throw new Error("Could not find return statement in component");
//   }

//   const jsx = returnStatement.getFirstDescendant((d) =>
//     d.getKindName().startsWith("Jsx")
//   );

//   if (!jsx) {
//     throw new Error("Could not find JSX element in return statement");
//   }

//   if (newProp) {
//     const openingElement =
//       jsx.getFirstChildByKind(SyntaxKind.JsxOpeningElement) ||
//       jsx.asKind(SyntaxKind.JsxSelfClosingElement);
//     if (!openingElement) throw new Error("Could not find JSX opening element");

//     openingElement.addAttribute({
//       name: newProp.name,
//       initializer: `"${newProp.value}"`,
//     });
//   }

//   if (insertChildJsx) {
//     if (jsx.getKind() === SyntaxKind.JsxElement) {
//       const closing = jsx.getFirstChildByKind(SyntaxKind.JsxClosingElement);
//       if (closing) {
//         jsx.insertText(closing.getStart(), `\n  ${insertChildJsx}\n`);
//       }
//     } else {
//       throw new Error("JSX element does not have children to insert into");
//     }
//   }

//   if (!dryRun) {
//     await sourceFile.save();
//     return `Component "${componentName}" in "${filePath}" was modified successfully.`;
//   } else {
//     return {
//       dryRun: true,
//       filePath,
//       preview: sourceFile.getFullText(),
//       message: `(dryRun) Component "${componentName}" in "${filePath}" would be modified.`,
//     };
//   }
// }

// export async function updateFunctionBody({
//   filePath,
//   functionName,
//   newBody,
//   dryRun = false,
// }: {
//   filePath: string;
//   functionName: string;
//   newBody: string;
//   dryRun?: boolean;
// }) {
//   const project = new Project({ tsConfigFilePath: "tsconfig.json" });
//   const sourceFile = project.addSourceFileAtPath(filePath);
//   const func = sourceFile.getFunction(functionName);

//   if (!func) throw new Error("Function not found");
//   func.setBodyText(newBody);

//   if (!dryRun) {
//     await sourceFile.save();
//     return `Function "${functionName}" body updated in "${filePath}"`;
//   } else {
//     return {
//       dryRun: true,
//       filePath,
//       preview: sourceFile.getFullText(),
//       message: `(dryRun) Function "${functionName}" body would be updated in "${filePath}"`,
//     };
//   }
// }

// export async function addFunctionToFile({
//   filePath,
//   functionCode,
//   dryRun = false,
// }: {
//   filePath: string;
//   functionCode: string;
//   dryRun?: boolean;
// }) {
//   const project = new Project({ tsConfigFilePath: "tsconfig.json" });
//   const sourceFile = project.addSourceFileAtPath(filePath);

//   sourceFile.addStatements(functionCode);

//   if (!dryRun) {
//     await sourceFile.save();
//     return `Function added to "${filePath}"`;
//   } else {
//     return {
//       dryRun: true,
//       filePath,
//       preview: sourceFile.getFullText(),
//       message: `(dryRun) Function would be added to "${filePath}"`,
//     };
//   }
// }

// export async function replaceJsxElement({
//   filePath,
//   oldTag,
//   newTag,
//   newProps,
//   replaceChildren = false,
//   dryRun = false,
// }: {
//   filePath: string;
//   oldTag: string;
//   newTag: string;
//   newProps?: Record<string, string>;
//   replaceChildren?: boolean;
//   dryRun?: boolean;
// }) {
//   const project = new Project({ tsConfigFilePath: "tsconfig.json" });
//   const sourceFile = project.addSourceFileAtPath(filePath);

//   const elements = sourceFile.getDescendantsOfKind(SyntaxKind.JsxElement)
//     .filter((el) => el.getOpeningElement().getTagNameNode().getText() === oldTag);

//   elements.forEach((el) => {
//     el.getOpeningElement().setTagName(newTag);
//     el.getClosingElement().setTagName(newTag);
//     if (newProps) {
//       Object.entries(newProps).forEach(([k, v]) => {
//         el.getOpeningElement().addAttribute({ name: k, initializer: `"${v}"` });
//       });
//     }
//     if (replaceChildren) {
//       el.getJsxChildren().forEach((child) => child.replaceWithText(""));
//     }
//   });

//   if (!dryRun) {
//     await sourceFile.save();
//     return `All <${oldTag}> elements in "${filePath}" replaced with <${newTag}>.`;
//   } else {
//     return {
//       dryRun: true,
//       filePath,
//       preview: sourceFile.getFullText(),
//       message: `(dryRun) <${oldTag}> elements would be replaced with <${newTag}> in "${filePath}"`,
//     };
//   }
// }

// export async function updateVariableInitializer({
//   filePath,
//   varName,
//   newValue,
//   dryRun = false,
// }: {
//   filePath: string;
//   varName: string;
//   newValue: string;
//   dryRun?: boolean;
// }) {
//   const project = new Project({ tsConfigFilePath: "tsconfig.json" });
//   const sourceFile = project.addSourceFileAtPath(filePath);

//   const variable = sourceFile.getVariableDeclaration(varName);
//   if (!variable) throw new Error("Variable not found");

//   variable.setInitializer(newValue);

//   if (!dryRun) {
//     await sourceFile.save();
//     return `Variable "${varName}" updated in "${filePath}".`;
//   } else {
//     return {
//       dryRun: true,
//       filePath,
//       preview: sourceFile.getFullText(),
//       message: `(dryRun) Variable "${varName}" in "${filePath}" would be updated.`,
//     };
//   }
// }