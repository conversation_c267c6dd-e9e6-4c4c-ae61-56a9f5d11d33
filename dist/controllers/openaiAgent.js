"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.simpleChat = simpleChat;
exports.prompt = prompt;
const path_1 = __importDefault(require("path"));
const promises_1 = require("node:fs/promises");
const fs_1 = require("fs");
const openai_1 = __importDefault(require("openai"));
const js_sha256_1 = require("js-sha256");
const cliconfig_1 = require("../database/cliconfig");
const compiler_1 = require("./compiler");
const messageparsing_1 = require("./messageparsing");
const toolsForAgents_1 = require("./toolsForAgents");
const claudeAgent_1 = require("./claudeAgent");
let openai = null;
// import {findAgent, createAgent, findThread, createThread as storeThread} from './yolodb';
function initialize() {
    if (!openai) {
        const rec = (0, cliconfig_1.getAPIKey)("openai");
        if (rec) {
            openai = new openai_1.default({ apiKey: rec.apikey });
        }
    }
    return openai;
}
// role: "user",
//         content: [
//           { 
//             type: "text",
//             text: modifiedPrompt
//           },
//           {
//             type: "image_file",
//             image_file: {
//               file_id: imageId,
//               detail: "low" // less tokens consumed so is cheaper presumably
//             }
//           }
//         ]
//
function simpleChat(messages) {
    return __awaiter(this, void 0, void 0, function* () {
        const openai = initialize();
        if (!openai) {
            console.error("Failed to initialize openai");
            return null;
        }
        else {
            if (messages[0].role !== "system") {
                messages.unshift({
                    role: "system",
                    content: `
  Assume the role of a programmer who helps with constructing graphql queries. You will be provided with some tools to interact with a graphql schema for a shopify store. You will use these tools to look for the appropriate fields that would satisfy the request and then construct a gql query. The user may ask for things that don't exist in the schema or database. If you don't find something just report that you couldn't find those specific things and create the query for the things you did find.

  # Description of how to use the tools
  The tools available to you are named 'search_schema', 'describe_schema_upto_level', 'get_schema_for_path'. 

  ## enumerate_fields_at_path
  This tool will enumerate all the subfields of the field whose path is supplied. It also supports fetching the descriptions and arguments of the fields. The returned value contains information about the target field as well as all the fields from the root to the target field. This is the cheapest and fastest tool available. As much as possible rely on this tool. The other tools can easily blow up the token budget and stop the chat midway.

  ## search_schema
  A fuzzy finder that will search the schema for text that you provide. Be careful when using this tool because if you make the search term too generic you will end up with too many matches and that will blow the token budget. 

  The tool can search the entire schema or a subtree of the schema if you give it a valid starting point. So you should try to start with searches that will create a small to medium amount of matches and then use the paths in that result to restrict your search to specific subtrees of the schema. 

  ## get_scheam_for_path
  This will retrieve the subtree for a give path in the schema. Once you have determined the type you want to query for or some close ancestor of it, you can use this function to get all the fields of that object. Again, if you do this for some very toplevel node in the schema then you will get a massive subtree that blows out the token budget. It is best to set maxDepth to 1 initially lest we exceed token limits.

  ## describe_schema_upto_level
  This will give you the entire schema tree upto a level. You can use this tool when starting your search. For example you could get the entire schema upto level 0 and immediately determine the first subtree you want to limit your fuzzy find to before you start. This will give you the first value you can pass in the 'startingPoint' search option for the 'search_schema' tool, to reduce the search space effectively.

  Once you have determined the answer the final reply should be a graphql schema that I can paste inside a gql\`\` tag followed with a typescript type that I can use to type the response that I will get by running the query.
  `
                });
            }
            const responseStream = openai.chat.completions.create({
                // model: "gpt-4o",
                model: "gpt-4.5-preview",
                messages,
                parallel_tool_calls: false,
                stream: true,
                tools: [
                    {
                        type: "function",
                        function: {
                            name: "search_schema",
                            description: "Fuzzy search in graphql schema. The return value contains the paths of the fields where the matches were found as well as descriptions, types and arguments of the matched fields.",
                            parameters: {
                                type: "object",
                                properties: {
                                    searchtext: {
                                        type: "string",
                                        description: "The text to search for using fuzzy search"
                                    },
                                    options: {
                                        type: "object",
                                        properties: {
                                            startingPoint: {
                                                type: "string",
                                                description: "A dot separated string that represents a path in the schema. For example 'product.title'. The fuzzy finder will limit its search to this subtree. Pass a blank string to search the entire tree"
                                            },
                                            includeArgs: {
                                                type: "boolean",
                                                description: "When false prevents the information about arguments to be passed when querying a field from being return in the response."
                                            },
                                            includeDescriptions: {
                                                type: "boolean",
                                                description: "Used to exclude the descriptions for fields in the search result. Useful when doing exploratory search, to reduce the response size and save tokens. Only set to true when you really want to read the descriptions since they can be large and blow the token budget."
                                            }
                                        },
                                        required: ["includeArgs", "includeDescriptions"]
                                    }
                                },
                                required: ["searchtext", "options"]
                            }
                        }
                    },
                    {
                        type: "function",
                        function: {
                            name: "describe_schema_upto_level",
                            description: "Get a json representation of the descriptions, fields, arguments and types in the graphql schema. The depth of the seach can be limited using the max_level argument.",
                            parameters: {
                                type: "object",
                                properties: {
                                    maxDepth: {
                                        type: "number",
                                        description: "The maximum depth level till which fields will be looked up in the schema"
                                    }
                                },
                                required: ["maxDepth"]
                            }
                        }
                    },
                    {
                        type: "function",
                        function: {
                            name: "get_schema_for_path",
                            description: "Get all the descriptions, fields, arguments, types etc from the schema along the provided path",
                            parameters: {
                                type: "object",
                                properties: {
                                    path: {
                                        type: "string",
                                        description: "A dot separated string reprenting the path to a field inside the graphql schema. For example `product.selectedOrFirstAvailableVariant.metafields`"
                                    },
                                    maxDepth: {
                                        type: "number",
                                        description: "Used to limit the levels that will be fetched beyond the specified path from the tree. Going till the lefs might include too much info and blow the token budget. A value of 5 seems to do the trick most of the time."
                                    }
                                },
                                required: ["path", "maxDepth"]
                            }
                        }
                    },
                    // {
                    //   type: "function",
                    //   function: {
                    //     name: "execute_query",
                    //     description: "Execute a query to check if it was successful or not",
                    //     parameters: {
                    //       type: "object",
                    //       properties: {
                    //         query: {
                    //           type: "string",
                    //           description: "The graphql query to run. This will be passed to a gql tag by the function so the value should be just the query in SDL as a string that can be embedded into a gql tag as gql`${query}`. The result will be 'successful' or the 'error: <error message if any>'"
                    //         },
                    //         variables: {
                    //           type: "object",
                    //           description: "An object containing the variables to be passed to the query"
                    //         }
                    //       },
                    //       required: ["query", "variables"]
                    //     }
                    //   }
                    // },
                    {
                        type: "function",
                        function: {
                            name: "enumerate_fields_at_path",
                            description: "Enumerate the fields for an object in the graphql schema. Think of it as a tree explorer. You can incrementally update the path argument and the function will return the fields of the object reachable via that path. The `includeDescriptions` and `includeArgumentsInfo` parameters determine if the function will also return the descriptions and arguments for the objects reached by the specified path in the schema. The `includeArgumentsInfo` parameter is more expensive compared to the `includeDescriptions` argument in terms of how many tokens are used to send the response. So use the `includeArgumentsInfo` parameter when you are sure you want to use the arguments for the obejct to build the query. Use the `includeDescriptions` argument to get more information about the fields you are looking at in a cheap way.",
                            parameters: {
                                type: "object",
                                properties: {
                                    path: {
                                        type: "string",
                                        description: "A dot separated string specifying the field in the schema whose fields are to be enumerated. If a blank string is passed the top level of the schema will be enumerated."
                                    },
                                    includeDescriptions: {
                                        type: "boolean",
                                        description: "Include the descriptions for the fields in the result"
                                    },
                                    includeArgumentsInfo: {
                                        type: "boolean",
                                        description: "Include all available information about the arguments applicable to the fields being returned. Set this to true sparingly to save tokens."
                                    }
                                },
                                required: ["path", "includeDescriptions", "includeArguments"]
                            }
                        }
                    }
                ],
            });
            // console.log("Waiting for claude");
            return (0, messageparsing_1.ingestOpenaiMessages)(responseStream);
        }
    });
}
function prompt(type, appId, assetName, userPrompt, res, model) {
    return __awaiter(this, void 0, void 0, function* () {
        console.log("[OPENAI AGENT] starting");
        const imgTagStart = userPrompt.indexOf('<img');
        let finalizedPrompt = [];
        if (imgTagStart >= 0) {
            console.log("[OPENAI AGENT] matching on image tag successful");
            const format = userPrompt.match(/<img\ssrc="data:(.+);base64,/);
            if (format && format[1]) {
                const originalMessage = userPrompt;
                const dataStart = userPrompt.indexOf(";base64,") + ";base64,".length;
                const dataEnd = userPrompt.indexOf("\"", dataStart);
                userPrompt = userPrompt.slice(0, imgTagStart) +
                    " removed_image_to_save_tokens " +
                    userPrompt.slice(userPrompt.indexOf(">", dataEnd));
                finalizedPrompt.push({
                    type: 'image_url',
                    image_url: {
                        url: `data:${format[1]};base64,${originalMessage.slice(dataStart, dataEnd)}`,
                        detail: 'high'
                    }
                });
                finalizedPrompt.push({
                    type: 'text',
                    text: userPrompt
                });
            }
            else {
                finalizedPrompt.push({
                    type: 'text',
                    text: userPrompt
                });
                console.log("[ANTHROPIC AGENT] Finalized text only prompt", JSON.stringify(finalizedPrompt, claudeAgent_1.logReplacer, 2));
            }
        }
        else {
            finalizedPrompt.push({
                type: 'text',
                text: userPrompt
            });
            console.log("[ANTHROPIC AGENT] Finalized text only prompt", JSON.stringify(finalizedPrompt, claudeAgent_1.logReplacer, 2));
        }
        const openai = initialize();
        let result = '';
        if (!openai) {
            result = 'No openai client found!';
        }
        else {
            const openedApp = (0, cliconfig_1.getOpenApp)(appId);
            if (openedApp) {
                const filePath = path_1.default.resolve(openedApp.repoPath, 'remoteCode/plugins', assetName, 'source/component.jsx');
                const fileStream = (0, fs_1.createWriteStream)(filePath, { flags: 'w' });
                res.write(`\nresult will be written to ${filePath}\n`);
                let chat = (0, cliconfig_1.getChat)(filePath, "openai", model);
                if (!chat) {
                    chat = (0, cliconfig_1.createChat)(filePath, type, "openai", model);
                }
                if (!chat) {
                    res.write(`\nfailed to create chat for: ${filePath}`);
                }
                else {
                    (0, cliconfig_1.createChatMessageWithContentAndRole)(chat.id, "user", JSON.stringify(finalizedPrompt));
                }
                const initialPrompt = yield (0, promises_1.readFile)(path_1.default.resolve(__dirname, '../systemPrompts/latest.md'), { encoding: 'utf8' });
                const messages = [
                    {
                        role: 'system', content: initialPrompt
                    },
                ];
                if (chat) {
                    const history = (0, cliconfig_1.getChatMessages)(chat.id, 'ASC');
                    for (let i = 0; i < history.length; ++i) {
                        const msg = history[i];
                        if (msg.role === 'assistant') {
                            let systemMessageContent;
                            try {
                                systemMessageContent = JSON.parse(msg.content);
                                if (Array.isArray(systemMessageContent)) {
                                    systemMessageContent = systemMessageContent.filter(content => content.type !== "tool_use");
                                }
                                if (systemMessageContent.length === 0) {
                                    continue;
                                }
                            }
                            catch (err) {
                                systemMessageContent = msg.content;
                            }
                            messages.push({
                                role: 'assistant',
                                content: systemMessageContent
                            });
                        }
                        else if (msg.role === 'user') {
                            let userMessageContent;
                            try {
                                userMessageContent = JSON.parse(msg.content);
                                if (userMessageContent.role === "tool") {
                                    continue;
                                }
                            }
                            catch (err) {
                                console.error("Failed to parse contents of user message");
                                userMessageContent = msg.content;
                            }
                            messages.push({
                                role: 'user',
                                content: userMessageContent
                            });
                        }
                        else {
                            console.warn("Skipping message from chat history because role is not handled: ", msg);
                        }
                    }
                }
                // include the latest prompt
                // messages.push({
                //   role: 'user',
                //   content: finalizedPrompt
                // });
                // if (typeof userPrompt === "string") {
                //   messages.push({role: 'user', content: userPrompt})
                // } else if (Array.isArray(userPrompt)){
                //   for (let i = 0; i < userPrompt.length; ++i) {
                //     messages.push(userPrompt[i]);
                //   }
                // } else {
                //   throw new Error("Invalid prompt");
                // }
                try {
                    let toolRun = false;
                    do {
                        const completion = openai.chat.completions.create({
                            model,
                            messages,
                            stream: true,
                            tools: [
                                {
                                    type: "function",
                                    function: {
                                        name: "enumerate_fields_at_path",
                                        description: "Enumerate the fields for an object in the graphql schema. Think of it as a tree explorer. You can incrementally update the path argument and the function will return the fields of the object reachable via that path. The `includeDescriptions` and `includeArgumentsInfo` parameters determine if the function will also return the descriptions and arguments for the objects reached by the specified path in the schema. The `includeArgumentsInfo` parameter is more expensive compared to the `includeDescriptions` argument in terms of how many tokens are used to send the response. So use the `includeArgumentsInfo` parameter when you are sure you want to use the arguments for the obejct to build the query. Use the `includeDescriptions` argument to get more information about the fields you are looking at in a cheap way.",
                                        parameters: {
                                            type: "object",
                                            properties: {
                                                path: {
                                                    type: "string",
                                                    description: "A dot separated string specifying the field in the schema whose fields are to be enumerated. If a blank string is passed the top level of the schema will be enumerated."
                                                },
                                                includeDescriptions: {
                                                    type: "boolean",
                                                    description: "Include the descriptions for the fields in the result"
                                                },
                                                includeArgumentsInfo: {
                                                    type: "boolean",
                                                    description: "Include all available information about the arguments applicable to the fields being returned. Set this to true sparingly to save tokens."
                                                }
                                            },
                                            required: ["path", "includeDescriptions", "includeArguments"]
                                        }
                                    }
                                }
                            ]
                        });
                        const ingestedResponse = yield (0, messageparsing_1.ingestOpenaiMessageStream)(completion, res, fileStream);
                        const assistantResponse = {
                            role: "assistant",
                        };
                        if (ingestedResponse === null || ingestedResponse === void 0 ? void 0 : ingestedResponse.messageText) {
                            assistantResponse.content = ingestedResponse.messageText;
                        }
                        toolRun = !!(ingestedResponse === null || ingestedResponse === void 0 ? void 0 : ingestedResponse.toolInput);
                        if (toolRun && ingestedResponse.toolInput) {
                            console.log("[OPENAI_AGENT] Starting tool run: ", ingestedResponse.toolInput.tool);
                            const inputVars = JSON.parse(ingestedResponse.toolInput.partialInput);
                            assistantResponse.tool_calls = [{
                                    id: ingestedResponse.toolInput.id,
                                    type: "function",
                                    function: {
                                        name: ingestedResponse.toolInput.tool,
                                        arguments: ingestedResponse.toolInput.partialInput
                                    }
                                }];
                            messages.push(assistantResponse);
                            if (chat) {
                                (0, cliconfig_1.createChatMessageWithContentAndRole)(chat.id, "assistant", JSON.stringify([{
                                        id: ingestedResponse.toolInput.id,
                                        input: inputVars,
                                        name: ingestedResponse.toolInput.tool,
                                        type: "tool_use"
                                    }]));
                            }
                            let result;
                            switch (ingestedResponse.toolInput.tool) {
                                case "search_schema":
                                    result = yield (0, toolsForAgents_1.searchSchema)(inputVars.searchtext, {
                                        startingPoint: inputVars.options.startingPoint,
                                        maxDepth: 1,
                                        includeArgs: false, // inputVars.options.includeArgs, 
                                        includeDescriptions: inputVars.options.includeDescriptions
                                    });
                                    break;
                                case "describe_schema_upto_level":
                                    result = yield (0, toolsForAgents_1.getQueriesUptoLevel)(inputVars.maxDepth);
                                    break;
                                case "get_schema_for_path":
                                    result = yield (0, toolsForAgents_1.getSchemaForPath)(inputVars.path, inputVars.maxDepth);
                                    break;
                                case "enumerate_fields_at_path":
                                    result = yield (0, toolsForAgents_1.enumerateFields)(inputVars.path, inputVars.includeDescriptions, inputVars.includeArgumentsInfo);
                                    break;
                                case "execute_query":
                                    result = yield (0, toolsForAgents_1.executeQuery)(inputVars.query, inputVars.variables);
                                    break;
                                default:
                                    console.log("Unknown tool!");
                                    return;
                            }
                            let stringifiedResult = JSON.stringify(result);
                            if (stringifiedResult.length > 40000) {
                                stringifiedResult = stringifiedResult.slice(0, 40000) + "--------rest of the response was truncated due to token limit--------";
                            }
                            messages.push({
                                role: "tool",
                                tool_call_id: ingestedResponse.toolInput.id,
                                content: stringifiedResult
                            });
                            if (chat) {
                                (0, cliconfig_1.createChatMessageWithContentAndRole)(chat.id, "user", JSON.stringify({
                                    role: "tool",
                                    tool_call_id: ingestedResponse.toolInput.id,
                                    content: stringifiedResult
                                }));
                            }
                        }
                    } while (toolRun);
                }
                catch (err) {
                    console.error("Expected failure at the end of the chunks from prompt: ", err);
                }
                fileStream.end();
                res.write(`\npreparing to compile generated code\n`);
                let newCode = yield (0, promises_1.readFile)(filePath, { encoding: 'utf8' });
                if (chat) {
                    (0, cliconfig_1.createChatMessageWithContentAndRole)(chat.id, 'assistant', newCode);
                }
                else {
                    console.error("Chat session is active but no chat found!");
                }
                let startOfCodeMarker = newCode.indexOf('```javascript');
                if (startOfCodeMarker >= 0) {
                    startOfCodeMarker += '```javascript'.length;
                }
                else if (newCode.indexOf('```jsx') >= 0) {
                    startOfCodeMarker = newCode.indexOf('```jsx') + '```jsx'.length;
                }
                else {
                    startOfCodeMarker = 0;
                }
                let endOfCodeBlock = newCode.indexOf('```', startOfCodeMarker);
                if (endOfCodeBlock < 0) {
                    endOfCodeBlock = newCode.length;
                }
                newCode = newCode.slice(startOfCodeMarker, endOfCodeBlock);
                yield (0, promises_1.writeFile)(filePath, newCode);
                // compile it
                const entries = yield (0, promises_1.readdir)(path_1.default.resolve(openedApp.repoPath, 'remoteCode/plugins'), { withFileTypes: true });
                const plugins = entries.filter(it => it.isDirectory()).map(it => it.name);
                const remoteCode = path_1.default.resolve(openedApp.repoPath, 'remoteCode');
                res.write('compiling...\n');
                const compiledDirectoryName = (0, js_sha256_1.sha256)(plugins.join('_')).toString();
                yield (0, compiler_1.compileMultiplePlugins)(remoteCode, plugins, compiledDirectoryName);
                res.write('compilation finished\n');
                res.end();
                // recompile
                // finish the call
            }
            else {
                result = 'App not found!';
            }
        }
        return result;
    });
}
