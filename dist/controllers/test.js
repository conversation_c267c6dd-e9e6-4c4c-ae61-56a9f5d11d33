"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const claudeAgent_1 = require("./claudeAgent");
const openaiAgent_1 = require("./openaiAgent");
const chalk_1 = __importDefault(require("chalk"));
const blessed_1 = __importDefault(require("blessed"));
const toolsForAgents_1 = require("./toolsForAgents");
const INITIAL_MESSAGE = "Generate a query to fetch products from a collection. I want to get image, title, price and tags (if any) for the products. I also want to paginate my queries.";
function retrieveSubtree() {
    return __awaiter(this, void 0, void 0, function* () {
        const queryType = yield (0, toolsForAgents_1.loadQueryType)();
        if (queryType) {
            const tree = yield (0, toolsForAgents_1.getQueryTreeForPath)(queryType, "product.selectedOrFirstAvailableVariant.metafields", 5, true, true, false);
            console.log(JSON.stringify(tree, null, 2));
        }
        else {
            console.log("Failed to extract schema");
        }
    });
}
/*
executeQuery(
`
query NewQuery($first: Int!, $identifiers: [HasMetafieldsIdentifier!]!) {
  products(first: $first) {
    nodes {
      handle
      featuredImage {
        url
      }
      metafields(identifiers: $identifiers) {
        key
        description
        createdAt
        type
        value
      }
    }
  }
}`,
{
  "first": 100,
  "first3": 0,
  "identifiers": [
    {
      "key": "rating",
      "namespace": "reviews"
    },
    {
      "key": "rating_count",
      "namespace": "reviews"
    },
    {
      "key": "group_data",
      "namespace": "vsk"
    }
  ],
  "key": "reviews"
});
*/
// getQueriesUptoLevel(2);
// console.log(searchSchema("product metafield"))
// retrieveSubtree();
function serializeChat(messages) {
    var _a, _b, _c, _d;
    let result = [];
    for (let msg of messages) {
        let serializedMessage = "";
        if (msg.role === "user") {
            for (let contentBlock of msg.content) {
                if (contentBlock.type === "text") {
                    serializedMessage += chalk_1.default.green("User: ") + contentBlock.text;
                }
                else if (contentBlock.type === "tool_result") {
                    // Only a single string is sent by all tools for now
                    serializedMessage += chalk_1.default.blue("Tool: ") + chalk_1.default.red(`(${(_a = contentBlock.content) === null || _a === void 0 ? void 0 : _a.length})`) + ((_b = contentBlock.content) === null || _b === void 0 ? void 0 : _b.slice(0, 150)) || "no content found";
                }
                else {
                    serializedMessage += chalk_1.default.green("User: ") + "unserializable user content";
                }
            }
        }
        else if (msg.role === "assistant") {
            if (Array.isArray(msg.content)) { // anthropic
                for (let contentBlock of msg.content) {
                    if (contentBlock.type === "text") {
                        serializedMessage += chalk_1.default.yellow("Assistant: ") + contentBlock.text;
                    }
                    else if (contentBlock.type === "tool_use") {
                        serializedMessage += chalk_1.default.yellow("Assistant: ") + "execute " + chalk_1.default.blue(contentBlock.name + JSON.stringify(contentBlock.input));
                    }
                    else {
                        serializedMessage += chalk_1.default.yellow("Assistant: ") + "unserializable assistant content";
                    }
                }
            }
            else if (msg.tool_calls || msg.content) { // openai
                if (msg.tool_calls) {
                    for (let toolcall of msg.tool_calls) {
                        serializedMessage += chalk_1.default.yellow("Assistant: ") + "execute " + chalk_1.default.blue(toolcall.function.name + toolcall.function.arguments);
                    }
                }
                if (msg.content) {
                    serializedMessage += chalk_1.default.yellow("Assistant: ") + msg.content;
                }
            }
        }
        else if (msg.role === "tool") {
            serializedMessage += chalk_1.default.blue("Tool: ") + chalk_1.default.red(`(${(_c = msg.content) === null || _c === void 0 ? void 0 : _c.length})`) + ((_d = msg.content) === null || _d === void 0 ? void 0 : _d.slice(0, 150)) || "no content found";
        }
        else {
            serializedMessage += chalk_1.default.gray(msg.role) + " <unrecognized role>";
        }
        result.push(serializedMessage);
    }
    return result;
}
class ChatApp {
    constructor() {
        this.focusedPane = "chathistory";
        this.chatHistory = [];
        this.chatHistoryOpenAI = [];
        this.finalAnswer = {
            query: "no query yet",
            variables: {}
        };
        this.provider = "anthropic";
        this.screen = blessed_1.default.screen({
            smartCSR: true,
            title: "devtools",
            dockBorders: true,
            autoPadding: true,
        });
        this.panes = this.addPanes();
        this.setupKeyBindings();
    }
    start(provider) {
        this.provider = provider;
        if (provider === "anthropic") {
            this.panes.chathistory.setContent(serializeChat(this.chatHistory).join("\n"));
        }
        else {
            this.panes.chathistory.setContent(serializeChat(this.chatHistoryOpenAI).join("\n"));
        }
        this.panes.chatinput.setValue(INITIAL_MESSAGE);
        this.screen.render();
    }
    addPanes() {
        const chathistory = blessed_1.default.box({
            top: 0,
            left: 0,
            width: "100%",
            height: "70%",
            scrollable: true,
            alwaysScroll: true,
            tags: true,
            border: { type: "line" },
            label: "Chat History",
            padding: { left: 1, right: 1 },
            scrollbar: {
                ch: " ",
                track: {
                    bg: "blue",
                },
                style: {
                    inverse: true,
                },
            },
            style: {
                focus: {
                    border: { fg: "#0000ff" },
                },
            },
        });
        const chatinput = blessed_1.default.textarea({
            bottom: 0,
            left: 0,
            width: "70%",
            height: "30%",
            keys: true,
            inputOnFocus: false,
            vi: true,
            mouse: true,
            border: { type: "line" },
            label: " Message Input (Ctrl+S to send) ",
            padding: 1,
            style: {
                focus: {
                    border: { fg: "#0000ff" },
                },
            },
        });
        const commandPane = blessed_1.default.list({
            bottom: 0,
            left: "70%",
            width: "30%",
            height: "30%",
            keys: true,
            vi: true,
            mouse: true,
            border: { type: "line" },
            label: " Commands ",
            padding: { left: 1 },
            items: ["Continue Chat", "Quit",],
            style: {
                focus: {
                    border: { fg: "#ff0000" }, // Red when focused
                },
                selected: {
                    bg: "blue",
                    fg: "white",
                },
            },
        });
        this.screen.append(chathistory);
        this.screen.append(chatinput);
        this.screen.append(commandPane);
        return { chathistory, chatinput, commandPane };
    }
    setupKeyBindings() {
        this.screen.key(["q", "C-c"], () => {
            this.screen.destroy();
            process.exit(0);
        });
        this.screen.key(["C-w"], () => {
            switch (this.focusedPane) {
                case "chathistory":
                    this.panes.chatinput.focus();
                    this.focusedPane = "chatinput";
                    break;
                case "chatinput":
                    this.panes.commandPane.focus();
                    this.focusedPane = "commandPane";
                    break;
                case "commandPane":
                    this.panes.chathistory.focus();
                    this.focusedPane = "chathistory";
                    break;
                default:
                    this.panes.chatinput.focus();
                    this.focusedPane = "chatinput";
            }
        });
        this.screen.key(["k"], () => {
            if (this.focusedPane === "chathistory") {
                this.panes.chathistory.scroll(-1);
                this.screen.render();
            }
        });
        this.screen.key(["j"], () => {
            if (this.focusedPane === "chathistory") {
                this.panes.chathistory.scroll(1);
                this.screen.render();
            }
        });
        this.screen.key(["C-d"], () => {
            if (this.focusedPane === "chathistory") {
                this.panes.chathistory.scroll(10);
                this.screen.render();
            }
        });
        this.screen.key(["C-u"], () => {
            if (this.focusedPane === "chathistory") {
                this.panes.chathistory.scroll(-10);
                this.screen.render();
            }
        });
        // Handle command selection
        this.panes.commandPane.on("select", (_, index) => __awaiter(this, void 0, void 0, function* () {
            const command = this.panes.commandPane.getItem(index).getText();
            if (command === "Quit") {
                this.screen.destroy();
                process.exit(0);
            }
            else if (command === "Continue Chat") {
                try {
                    if (this.provider === "anthropic") {
                        yield this.iterateChat();
                    }
                    else {
                        yield this.iterateChatForOpenAI();
                    }
                }
                catch (err) {
                    console.error("Failed when iterating chat", err);
                }
            }
            this.screen.render();
        }));
    }
    iterateChatForOpenAI() {
        return __awaiter(this, void 0, void 0, function* () {
            if (!this.panes.chatinput.getText()) {
                this.panes.chatinput.setValue(INITIAL_MESSAGE);
            }
            if (this.chatHistoryOpenAI.length === 0) {
                this.chatHistoryOpenAI[0] = {
                    role: "user",
                    content: [
                        {
                            type: "text",
                            text: this.panes.chatinput.getValue() || INITIAL_MESSAGE
                        }
                    ]
                };
            }
            this.panes.chathistory.setContent(serializeChat(this.chatHistoryOpenAI).join("\n") + chalk_1.default.blue("\n Waiting for chat response..."));
            this.screen.render();
            const response = yield (0, openaiAgent_1.simpleChat)(this.chatHistoryOpenAI);
            const assistantResponse = {
                role: "assistant",
            };
            this.chatHistoryOpenAI.push(assistantResponse);
            if (response === null || response === void 0 ? void 0 : response.messageText) {
                assistantResponse.content = response.messageText;
            }
            if (response === null || response === void 0 ? void 0 : response.toolInput) {
                const inputVars = JSON.parse(response.toolInput.partialInput);
                assistantResponse.tool_calls = [{
                        id: response.toolInput.id,
                        type: "function",
                        function: {
                            name: response.toolInput.tool,
                            arguments: response.toolInput.partialInput
                        }
                    }];
                let result;
                switch (response.toolInput.tool) {
                    case "search_schema":
                        result = yield (0, toolsForAgents_1.searchSchema)(inputVars.searchtext, {
                            startingPoint: inputVars.options.startingPoint,
                            maxDepth: 1,
                            includeArgs: false, // inputVars.options.includeArgs, 
                            includeDescriptions: inputVars.options.includeDescriptions
                        });
                        break;
                    case "describe_schema_upto_level":
                        result = yield (0, toolsForAgents_1.getQueriesUptoLevel)(inputVars.maxDepth);
                        break;
                    case "get_schema_for_path":
                        result = yield (0, toolsForAgents_1.getSchemaForPath)(inputVars.path, inputVars.maxDepth);
                        break;
                    case "enumerate_fields_at_path":
                        result = yield (0, toolsForAgents_1.enumerateFields)(inputVars.path, inputVars.includeDescriptions, inputVars.includeArgumentsInfo);
                        break;
                    case "execute_query":
                        result = yield (0, toolsForAgents_1.executeQuery)(inputVars.query, inputVars.variables);
                        this.finalAnswer.query = inputVars.query;
                        this.finalAnswer.variables = inputVars.variables;
                        break;
                    default:
                        console.log("Unknown tool!");
                        return;
                }
                let stringifiedResult = JSON.stringify(result);
                if (stringifiedResult.length > 20000) {
                    stringifiedResult = stringifiedResult.slice(0, 20000) + "--------rest of the response was truncated due to token limit--------";
                }
                this.chatHistoryOpenAI.push({
                    role: "tool",
                    tool_call_id: response.toolInput.id,
                    content: stringifiedResult
                });
            }
            // Update chat history pane
            this.panes.chathistory.setContent(serializeChat(this.chatHistoryOpenAI).join("\n"));
            this.screen.render();
        });
    }
    iterateChat() {
        return __awaiter(this, void 0, void 0, function* () {
            if (!this.panes.chatinput.getText()) {
                this.panes.chatinput.setValue(INITIAL_MESSAGE);
            }
            this.chatHistory[0] = {
                role: "user",
                content: [
                    {
                        type: "text",
                        text: INITIAL_MESSAGE
                    }
                ]
            };
            this.panes.chathistory.setContent(serializeChat(this.chatHistory).join("\n") + chalk_1.default.blue("\n Waiting for chat response..."));
            this.screen.render();
            const response = yield (0, claudeAgent_1.simpleChat)(this.chatHistory);
            if (response === null || response === void 0 ? void 0 : response.messageText) {
                this.chatHistory.push({
                    role: "assistant",
                    content: [{ type: "text", text: response.messageText }]
                });
            }
            if (response === null || response === void 0 ? void 0 : response.toolInput) {
                const inputVars = JSON.parse(response.toolInput.partialInput);
                this.chatHistory.push({
                    role: "assistant",
                    content: [
                        {
                            id: response.toolInput.id,
                            input: inputVars,
                            name: response.toolInput.tool,
                            type: "tool_use"
                        }
                    ]
                });
                let result;
                switch (response.toolInput.tool) {
                    case "search_schema":
                        result = yield (0, toolsForAgents_1.searchSchema)(inputVars.searchtext, {
                            startingPoint: inputVars.options.startingPoint,
                            maxDepth: 1,
                            includeArgs: false, // inputVars.options.includeArgs, 
                            includeDescriptions: inputVars.options.includeDescriptions
                        });
                        break;
                    case "describe_schema_upto_level":
                        result = yield (0, toolsForAgents_1.getQueriesUptoLevel)(inputVars.maxDepth);
                        break;
                    case "get_schema_for_path":
                        result = yield (0, toolsForAgents_1.getSchemaForPath)(inputVars.path, inputVars.maxDepth);
                        break;
                    case "enumerate_fields_at_path":
                        result = yield (0, toolsForAgents_1.enumerateFields)(inputVars.path, inputVars.includeDescriptions, inputVars.includeArgumentsInfo);
                        break;
                    case "execute_query":
                        result = yield (0, toolsForAgents_1.executeQuery)(inputVars.query, inputVars.variables);
                        this.finalAnswer.query = inputVars.query;
                        this.finalAnswer.variables = inputVars.variables;
                        break;
                    default:
                        console.log("Unknown tool!");
                        return;
                }
                let stringifiedResult = JSON.stringify(result);
                if (stringifiedResult.length > 20000) {
                    stringifiedResult = stringifiedResult.slice(0, 20000) + "--------rest of the response was truncated due to token limit--------";
                }
                this.chatHistory.push({
                    role: "user",
                    content: [
                        {
                            type: "tool_result",
                            tool_use_id: response.toolInput.id,
                            content: stringifiedResult
                        }
                    ]
                });
            }
            // Update chat history pane
            this.panes.chathistory.setContent(serializeChat(this.chatHistory).join("\n"));
            this.screen.render();
        });
    }
}
const app = new ChatApp();
// app.start("openai");
app.start("anthropic");
function main() {
    return __awaiter(this, void 0, void 0, function* () {
        //const result = await enumerateFields("products.edges.node.adjacentVariants.unitPrice", false, false);
        const result = yield (0, toolsForAgents_1.enumerateFields)("products.edges.node.adjacentVariants.product.title", false, false);
        // const result = await searchSchema("productHandle", {
        //   startingPoint: "", 
        //   maxDepth: 1, 
        //   includeArgs: true, 
        //   includeDescriptions: true,
        // });
        // const result = await getSchemaForPath("product.priceRange", 5);
        // const result = await getQueriesUptoLevel(1);
        if (result) {
            const serializedResult = JSON.stringify(result, null, 2);
            console.log(serializedResult);
            console.log(chalk_1.default.red("Size: " + serializedResult.length));
        }
    });
}
// main();
