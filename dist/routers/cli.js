"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.jsonParser = void 0;
const express_1 = __importDefault(require("express"));
const workspace_1 = require("../controllers/workspace");
const body_parser_1 = __importDefault(require("body-parser"));
const cliconfig_1 = require("../database/cliconfig");
const projectSetup_1 = require("../controllers/projectSetup");
const websocket_1 = require("../websocket");
const axios_1 = __importDefault(require("axios"));
const chalk_1 = __importDefault(require("chalk"));
exports.jsonParser = body_parser_1.default.json();
const router = express_1.default.Router();
// import workspaceMiddleware from '../middlewares/workspace';
// router.get('/workspaces', workspaceMiddleware, async (req, res) => {
//   
// });
router.delete('/workspaces/:workspaceId/closeApp', exports.jsonParser, (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    const fullPath = req.body.fullPath;
    (0, cliconfig_1.closeAppByLocation)(fullPath);
    res.status(200);
    res.end();
}));
router.post('/operation/opencode/:location', (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const location = decodeURIComponent(req.params.location);
        yield (0, workspace_1.openWithCode)(location);
        res.status(200);
        res.end();
    }
    catch (err) {
        console.error("Failed when trying to open location with finder");
        next(err);
    }
}));
router.post('/operation/openfinder/:location', (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const location = decodeURIComponent(req.params.location);
        yield (0, workspace_1.openWithFinder)(location);
        res.status(200);
        res.end();
    }
    catch (err) {
        console.error("Failed when trying to open location with finder");
        next(err);
    }
}));
router.get('/workspaces/list', (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const response = yield (0, workspace_1.listWorkspaces)();
        res.json(response);
    }
    catch (err) {
        console.error("Failure in GET /workspace");
        next(err);
    }
}));
router.post('/workspaces', (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    // add entry in workspace table to link a filesystem path and name
    // link workspace to apptile-server url
    // clone sdk repository and checkout specified branch
}));
router.post('/workspaces/:workspaceId/cloneApp', exports.jsonParser, (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const workspace = parseInt(req.params.workspaceId);
        // TODO(add types to manifest and appIntegrations)
        const manifest = req.body.manifest;
        const appIntegrations = req.body.appIntegrations;
        const apptileConfig = req.body.apptileConfig;
        if (process.env.NODE_ENV === "development") {
            (0, workspace_1.cloneApp)(workspace, manifest, appIntegrations, apptileConfig);
        }
        else {
            (0, workspace_1.cloneAppEc2)(workspace, manifest, appIntegrations, apptileConfig);
        }
        res.status(200);
        res.end();
    }
    catch (err) {
        console.error("Failed to clone app");
        next(err);
    }
}));
router.get('/workspaces/:workspaceId/app/:appId', (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    const workspaceId = req.params.workspaceId;
    const appId = req.params.appId;
    try {
        const appLocation = yield (0, workspace_1.getAppLocation)(parseInt(workspaceId), appId);
        const localBundles = yield (0, workspace_1.getLocalBundles)(appLocation);
        res.json({
            appId,
            appRoot: appLocation,
            codePushBundles: localBundles
        });
    }
    catch (err) {
        next(err);
    }
}));
router.post('/workspaces/:workspaceId/openApp/:appId', (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    // find the folder for appid in the workspace
    const appId = req.params.appId;
    const workspaceId = req.params.workspaceId;
    try {
        const appRoot = yield (0, workspace_1.getAppLocation)(parseInt(workspaceId), appId);
        (0, cliconfig_1.insertOrUpdateOpenapp)(workspaceId, appId, appRoot);
        res.status(201);
        res.end();
    }
    catch (err) {
        console.error("Failed to open app");
        next(err);
    }
}));
router.post('/app/:appId/regenerateAppConfig', (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const appId = req.params.appId;
        const existingConfig = yield (0, workspace_1.getAppConfigsFromFS)(appId);
        yield (0, workspace_1.generateApptileConfig)(existingConfig);
        res.status(200);
        res.end();
    }
    catch (error) {
        console.error("Failed to regenerate app config", error);
        res.status(400).json({ error: error.message });
    }
}));
router.post('/app/:appId/npmInstall', (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    (0, websocket_1.sendLog)("Starting npm install");
    const appId = req.params.appId;
    try {
        const app = (0, cliconfig_1.getOpenApp)(appId);
        if (app) {
            yield (0, projectSetup_1.npmInstall)(app.repoPath);
        }
        else {
            throw new Error("Unable to run npm install for an app that is not open " + appId);
        }
        res.status(201);
        res.end();
    }
    catch (err) {
        console.error("failed to do npm install", err);
        next(err);
    }
}));
router.post('/app/:appId/podInstall', (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    (0, websocket_1.sendLog)("Starting pod install");
    const appId = req.params.appId;
    try {
        const app = (0, cliconfig_1.getOpenApp)(appId);
        if (app) {
            yield (0, projectSetup_1.podInstall)(app.repoPath);
        }
        else {
            throw new Error("Unable to run npm install for an app that is not open " + appId);
        }
        res.status(201);
        res.end();
    }
    catch (err) {
        console.error("failed to do npm install", err);
        next(err);
    }
}));
router.get('/shoplazza-temp-proxy/*', (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    // const query = req.query;
    let config = {
        method: 'get',
        maxBodyLength: Infinity,
        url: 'https://shoplazza-test-store1.myshoplaza.com/api/collections/9f36baea-e6b3-4f1e-8bb7-18c0b44a3e2a/cps?page=0&limit=8',
        headers: {}
    };
    try {
        const { data } = yield axios_1.default.request(config);
        res.json(data);
    }
    catch (err) {
        next(err);
    }
}));
router.get('/createLog', (req, res, next) => __awaiter(void 0, void 0, void 0, function* () {
    console.log(chalk_1.default.yellow('LOG: ' + req.headers['x-telemetry-log']));
    res.status(200);
    res.end();
}));
exports.default = router;
