import React, {FC, useContext, useState} from 'react';
import {StoreContext} from '../store';
import {FileSystem} from '../types';
import {handlePathOpen} from '../fsOperations';
import DevActions from './DevActions';
import Publishing from './Publishing';
import CommitManager from './CommitManager';

const TabButton: FC<{active: boolean; onClick: () => void; label: string;}> = function TabButton({active, onClick, label}) {
  const classes = active ?  "border border-t-blue-500 border-b-none text-sm text-stone-800" : "text-sm";
  return (
    <button 
      className={classes}
      onClick={onClick}
    >
      {label}
    </button>
  );
}


const CurrentApp: FC = function CurrentApp() {
  const [tab, setTab] = useState<"publishing"|"devactions"|"commits">("devactions");
  const store = useContext(StoreContext);
  let repoData: FileSystem.Repo|null = null;
  if (store && store.currentAppRepoOffset > -1) {
    const workspace = store.workspaceData.payload[0];
    repoData = workspace?.repos[store.currentAppRepoOffset]
  }

  let renderedTab = null;
  if (store) {
    if (store.currentAppRepoOffset === -1) {
      renderedTab = <div>repoOffset is -1. This probably means your apptile.config.json has the wrong "APP_ID"</div>;
    } else if (repoData && tab === "devactions") {
      renderedTab = <DevActions 
        repoData={repoData} 
        appData={store.appData} 
        devicesData={store.devicesData}
      />;
    } else if (!repoData && tab === "devactions") {
      renderedTab = <div>There is no current app open. Try opening one in the workspace!</div>
    } else if (repoData && tab === "publishing") {
      renderedTab = <Publishing repoData={repoData} appData={store.appData} />
    } else if (!repoData && tab === "publishing") {
      renderedTab = <div>There is no current app open. Try opening one in the workspace!</div>
    } else if (tab === "commits") {
      if (store.appData.loadingInfo.status === "success") {
        renderedTab = <CommitManager appId={store.appData.payload.manifest.uuid} />;
      } else {
        renderedTab = <div className="border">Waiting for appData to load</div>
      }
    }  else {
      renderedTab = <div>This is unreachable codepath</div>
    }
  } else {
    renderedTab = <div>Store is not initialized! Retry</div>
  }

  let header = null;
  if (repoData) {
    header = (<div className="flex flex-row justify-between items-center w-full p-1">
      <div className="text-orange-500">{repoData.name}</div>
      <button onClick={() => repoData && handlePathOpen(repoData.fullPath, 'code')}>
        <img 
          alt="open in vscode"
          src={require('../assets/code.png')}
          className="w-8"
        />
      </button>
    </div>);
  }

  return (
    <div className="w-80">
      {header}
      <div className="flex flex-row gap-2">
        <TabButton 
          label="Dev Actions"
          active={tab === "devactions"}
          onClick={() => setTab("devactions")}
        />
        <TabButton 
          label="Publishing"
          active={tab === "publishing"}
          onClick={() => setTab("publishing")}
        />
        <TabButton 
          label="Commits"
          active={tab === "commits"}
          onClick={() => setTab("commits")}
        />
      </div>
      {renderedTab}
    </div>
  );
}

export default CurrentApp;
