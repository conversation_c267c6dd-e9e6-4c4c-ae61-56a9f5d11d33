#!/bin/sh
"use strict";
':'; //; exec "$(command -v nodejs || command -v node)" "$0" "$@"
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const path_1 = __importDefault(require("path"));
const cors_1 = __importDefault(require("cors"));
const http_1 = __importDefault(require("http"));
const morgan_1 = __importDefault(require("morgan"));
// import bodyParser from 'body-parser'
require("dotenv/config");
const cli_1 = __importDefault(require("./routers/cli"));
const home_1 = __importDefault(require("./routers/home"));
const plugins_1 = __importDefault(require("./routers/plugins"));
const webSDK_1 = __importDefault(require("./routers/webSDK"));
const navigators_1 = __importDefault(require("./routers/navigators"));
const websocket_1 = require("./websocket");
const workspace_1 = require("./controllers/workspace");
const app = (0, express_1.default)();
app.use((0, morgan_1.default)(':method :url :status'));
app.set('view engine', 'pug');
const server = http_1.default.createServer(app);
app.use((0, cors_1.default)());
// export const jsonParser = bodyParser.json({limit: '50mb'});
(0, websocket_1.createSocketServer)(server);
app.use(`/plugin-server/cli`, cli_1.default);
app.use(`/plugin-server/home`, home_1.default);
app.use(`/plugin-server/plugins`, plugins_1.default);
app.use(`/plugin-server/navigators`, navigators_1.default);
app.use(`/plugin-server/webSDK`, webSDK_1.default);
// const staticOptions = {
//   maxAge: '1000d', 
//   etag: true,   
//   lastModified: true, 
//   immutable: true 
// };
app.use('/plugin-server/public', express_1.default.static(path_1.default.resolve(__dirname, 'public'))); //, staticOptions));
// For handling spa
app.use('/plugin-server/public/ui/*', (req, res) => {
    res.sendFile(path_1.default.resolve(__dirname, 'public/ui/index.html'));
});
app.get('/plugin-server/healthcheck', (req, res) => {
    res.send('ok');
});
app.use((err, req, res, next) => {
    if (res.headersSent) {
        return next(err);
    }
    res.status(500);
    console.error("GLOBAL_ERROR", err);
    res.end();
});
(0, workspace_1.ensureDefaultWorkspace)().then(startupMessage => {
    server.listen(3100, '0.0.0.0', () => {
        console.log(`visit http://${process.env.NODE_ENV === "production"
            ? "cli.apptile.io"
            : "localhost:3100"}/plugin-server/cli/workspaces/list`);
        console.log(startupMessage);
    });
});
