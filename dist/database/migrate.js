"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.runMigrations = runMigrations;
const fs_1 = require("fs");
const path_1 = __importDefault(require("path"));
function runMigrations(db) {
    db.exec(`
    CREATE TABLE IF NOT EXISTS migrations (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL UNIQUE,
      apptiled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
  `);
    const appliedMigrations = new Set(db.prepare("SELECT name FROM migrations").all()
        .map((m) => m.name));
    const migrationFiles = (0, fs_1.readdirSync)(path_1.default.resolve(__dirname, "migrations")).sort();
    for (const file of migrationFiles) {
        if (!appliedMigrations.has(file)) {
            console.log(`Applying migration: ${file}`);
            const sql = (0, fs_1.readFileSync)(path_1.default.join(__dirname, "migrations", file), "utf8");
            db.exec(sql);
            db.prepare("INSERT INTO migrations (name) VALUES (?)").run(file);
        }
    }
    console.log("Migrations complete.");
}
