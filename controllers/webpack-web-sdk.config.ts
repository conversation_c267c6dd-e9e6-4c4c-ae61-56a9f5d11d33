import { Configuration } from "webpack";

const path = require("path");

const webpackConfig: Configuration = {
  entry: "__ADDED_AT_RUNTIME__",
  output: {
    filename: "web-sdk-bundle.js",
    path: "__ADDED_AT_RUNTIME__",
    libraryTarget: "umd", // Universal Module Definition
    globalObject: "apptileWebSDK", // Necessary for UMD to work in Node and browser environments
  },
  stats: {
    colors: false,
  },
  mode: "development",
  externals: {
    react: {
      commonjs: "react",
      commonjs2: "react",
      amd: "react",
      root: "React", // Global variable in non-module environments
    },
    "react-redux": {
      commonjs: "react-redux",
      commonjs2: "react-redux",
      amd: "react-redux",
      root: "ReactRedux",
    },
    "apptile-web-core": {
      commonjs: "apptile-web-core",
      commonjs2: "apptile-web-core",
      amd: "apptile-web-core",
      root: "ApptileWebCore",
    },
    "react-router-dom": {
      commonjs: "react-router-dom",
      commonjs2: "react-router-dom",
      amd: "react-router-dom",
      root: "ReactRouterDom",
    },
    "react-router": {
      commonjs: "react-router",
      commonjs2: "react-router",
      amd: "react-router",
      root: "ReactRouter",
    },
  },
  module: {
    rules: [
      {
        test: /\.[jt]sx?$/, // Matches both .ts/.tsx and .js/.jsx files
        exclude: /node_modules/,
        use: [
          {
            loader: "babel-loader",
            options: {
              cacheDirectory: true,
              presets: [
                "@babel/preset-env", // Transpile modern JavaScript
                "@babel/preset-react", // Transpile JSX
                "@babel/preset-typescript", // Transpile TypeScript (without type-checking)
              ],
            },
          },
        ],
      },
    ],
  },
  resolve: {
    extensions: [
      ".web.tsx",
      ".web.ts",
      ".tsx",
      ".ts",
      ".web.jsx",
      ".web.js",
      ".jsx",
      ".js",
      ".css",
    ],
    modules: ["__ADDED_AT_RUNTIME__"],
  },
};
// export default webpackConfig
export default webpackConfig;
