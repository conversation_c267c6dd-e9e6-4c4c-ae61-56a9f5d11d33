"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
const follow_redirects_1 = require("follow-redirects");
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const cliconfig_1 = require("../database/cliconfig");
const systemPrompt = fs_1.default.readFileSync(path_1.default.resolve(__dirname, "latest.html"), { encoding: "utf8" });
const apikey = (_a = (0, cliconfig_1.getAPIKey)('claude')) === null || _a === void 0 ? void 0 : _a.apikey;
if (!apikey) {
    console.error("Failed to retrieve claude's apikey");
    process.exit(1);
}
var options = {
    'method': 'POST',
    'hostname': 'api.anthropic.com',
    'path': '/v1/messages/count_tokens',
    'headers': {
        'x-api-key': apikey,
        'content-type': 'application/json',
        'anthropic-version': '2023-06-01'
    },
    'maxRedirects': 20
};
var req = follow_redirects_1.https.request(options, function (res) {
    var chunks = [];
    res.on("data", function (chunk) {
        chunks.push(chunk);
    });
    res.on("end", function () {
        var body = Buffer.concat(chunks);
        console.log(body.toString());
    });
    res.on("error", function (error) {
        console.error(error);
    });
});
var postData = JSON.stringify({
    "model": "claude-3-7-sonnet-20250219",
    "system": systemPrompt,
    "messages": [
        {
            "role": "user",
            "content": "Hello, Claude"
        }
    ]
});
req.write(postData);
req.end();
