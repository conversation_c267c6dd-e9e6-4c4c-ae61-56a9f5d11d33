import React, {FC} from 'react';

type ButtonProps = {
  onClick: (() => void);
  label: string;
  showLoader?: boolean;
  disabled?: boolean;
  className?: string;
};
const Button: FC<ButtonProps> = function ({onClick, label, showLoader, disabled, className}) {
  let loader = null;
  if (showLoader) {
    loader = <img alt="loading" src={require('../assets/loading.gif')} style={{height: 15, marginRight: 4}} />
  }
  return (
    <button
      disabled={disabled}
      className={`flex flex-row p-1 rounded text-blue-500 border border-blue-500 m-1 disabled:opacity-50 ${className}`}
      onClick={onClick}
    >
      {label}
      {loader}
    </button>
  );
}

export default Button;
