// import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
// import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
// import { z } from "zod";
// import { prompt } from "./supabaseAgent";
// import {Logger} from '../../../../utils/logger';
// import { apptileContextZodSchema } from "../../../../utils";


// const logger = new Logger({filePath: __dirname + "/logs/supabase-client-mcp-server-logger.txt"});
// const server = new McpServer(
//   {
//     name: "supabase-client-mcp-server",
//     version: "1.0.0",
//   },
//   {
//     capabilities: {
//       tools: {},
//     },
//   }
// );

// const formatReturn = (result: any): any => {
//   if (Array.isArray(result)) {
//     return {
//       content: result.map((item) => ({
//         type: "text",
//         text: JSON.stringify(item),
//       })),
//     };
//   }

//   return {
//     content: [
//       {
//         type: "text",
//         text: typeof result === 'string' ? result : JSON.stringify(result),
//       },
//     ],
//   }
// };

// const supabaseActionInput = {
//   action: z
//     .enum([
//       "fetch",
//       "insert",
//       "update",
//       "delete",
//       "get_schemas",
//       "get_tables",
//       "get_table_schema",
//       "authentication_api_generator",
//       "get_supabase_rest_api_key_and_base_url",
//       "create_table",
//       "enable_rls",
//       "disable_rls",
//       "create_rls_policy",
//     ])
//     .describe(
//       "The type of database operation to perform. In case of fetch, it will return the fetch apis to get the data directly from supabase."
//     ),
//   custom_ask_description: z
//     .string()
//     .optional()
//     .describe("Description of the custom ask operation."),
//   schema_name: z
//     .string()
//     .optional()
//     .describe("The name of the schema to perform the operation on."),
//   table_name: z
//     .string()
//     .optional()
//     .describe("The name of the table to perform the operation on, Or the table name for creating a table"),
//   filters: z
//     .record(z.string(), z.any())
//     .optional()
//     .describe("Filters for selecting records (used in fetch, update, delete) or policy details for RLS operations (policy_name, policy_action, policy_definition, policy_role)."),
//   policy_details: z
//     .object({
//       policy_name: z.string().describe("A unique name for the RLS policy"),
//       policy_action: z.enum(["ALL", "SELECT", "INSERT", "UPDATE", "DELETE"]).optional().describe("The operation the policy applies to (defaults to ALL)"),
//       policy_definition: z.string().describe("The SQL condition that defines when the policy applies"),
//       policy_role: z.string().optional().describe("The database role the policy applies to (defaults to 'authenticated')")
//     })
//     .optional()
//     .describe("Details for creating Row Level Security policies"),
//   apptile_context: apptileContextZodSchema,
//   schema: z
//       .array(
//         z.object({
//           column: z.string(),
//           type: z.string(),
//           constraints: z.string().optional(),
//           primary_key: z.boolean().optional(),
//           auto_increment: z.boolean().optional(),
//           unique: z.boolean().optional(),
//         })
//       )
//       .optional()
//       .describe("Table schema definition for table creation."),
//   auth_credentials: z
//     .object({
//       email: z.string().email(),
//       password: z.string(),
//     })
//     .optional()
//     .describe(
//       "Credentials for authentication actions (auth_sign_in, auth_sign_up)."
//     ),
//   auth_action: z.enum(["signup", "signin", "signout", "get_user"]).optional(),
//   data: z
//     .array(z.record(z.string(), z.any()))
//     .optional()
//     .describe("Data to insert or update records."),
// };

// const supabaseActionSchema = z.object(supabaseActionInput);
// export type SupabaseActionType = z.infer<typeof supabaseActionSchema>;

// server.tool(
//   "supabase_action",
//   `This tool is only used by the backend. Any react-native code cannot use this tool directly. It can be used to interact 
//     with supabase to either prepare the database for use in an app or to retrieve information about how to call the REST endpoints
//     exposed by supabsed. The type of database operation this tool will perform: 
//     'fetch': Returns Supabase query methods to retrieve data with filtering, sorting, and pagination capabilities. 
//     'insert': Creates new records in the specified table. 
//     'update': Modifies existing records that match the specified conditions. 
//     'delete': Removes records from the database that match the specified conditions. 
//     'get_schemas': Retrieves all available schemas in the current database. 
//     'create_table': Creates a table. But provide the table name and schema. Its mandatory for table creation
//     'get_tables': Lists all tables available in the specified schema. 
//     'get_table_schema': Returns the column definitions and constraints for a specific table. 
//     'authentication_api_generator': Receive authentication related api endpoints for signup, signin, signout, and get user. You have to provide auth creds for sign in, sign out and get user 
//     'auth_action: Give the auth action to perform. It is required for any kind of authentication_api_generator
//     'get_supabase_api_key_and_api_endpoint': Returns the API key and endpoint URL for direct Supabase access.
//     'enable_rls': Enables Row Level Security for the specified table.
//     'disable_rls': Disables Row Level Security for the specified table.
//     'create_rls_policy': Creates a Row Level Security policy for the specified table. Requires policy_name, policy_action, policy_definition, and policy_role in the filters parameter.`,
//   supabaseActionInput,
//   async (inputVars: SupabaseActionType) => {
//     logger.info("Input vars: ", inputVars);
//     try {
//       {
//         const result = await prompt(inputVars);
//         if (!result) {
//           return formatReturn("No response from supabase agent!");
//         } else {
//           return formatReturn(result);
//         }
//       }
//     } catch (error: any) {
//       logger.error("Error from tool call supabse_action: ", error);
//       return formatReturn(error.message);
//     }
//   }
// );

// async function main() {
//   const transport = new StdioServerTransport();
//   await server.connect(transport);
//   logger.info("Connected to supabase client mcp server");
//   // console.error("Supabase MCP Server running on stdio");
// }

// main().catch((error) => {
  
//   // console.error("Fatal error in main():", error);
//   process.exit(1);
// });
