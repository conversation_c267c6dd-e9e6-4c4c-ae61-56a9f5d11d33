export {}
// import React, {useContext, createElement, FC, useEffect} from 'react';
// import {StoreContext, StoreDispatchContext} from '../App';
// import {useMatch} from 'react-router-dom';
// 
// function recursiveCreate(root: any) {
//   if (root.type === 'directory') {
//     let children = [];
//     for (let i = 0; i < root.contents?.length ?? 0; ++i) {
//       const child = root.contents[i];
//       if (child.type === 'directory') {
//         children.push(recursiveCreate(child));
//       } else {
//         children.push(<div className="file" key={child.fullPath}>{child.name}</div>);
//       }
//     }
//     return (<div className="directory" key={root.fullPath}>
//         <div className="directory-name">{root.name}</div>
//         <div className="directory-contents">
//           {children}
//         </div>
//       </div>);
//   } else {
//     return <div className="file" key={root.fullPath}>{root.name}</div>
//   }
// }
// 
// const Explorer: FC = function () {
//   const store = useContext(StoreContext);
//   const dispatch = useContext(StoreDispatchContext);
//   const match = useMatch('explore/:path/:depth');
// 
//   useEffect(() => {
//     if (match && match.params.path && match.params.depth) {
//       const path = match.params.path;
//       const depth = match.params.depth;
//       const encodedPath = encodeURIComponent(path);
//       dispatch({
//         type: 'SET_ROOT',
//         payload: path
//       });
//       fetch(`${process.env.REACT_APP_SERVER_URL}/plugin-server/home/<USER>/${encodedPath}/${depth}`)
//         .then(res => res.json())
//         .then(contents => {
//           dispatch({
//             type: 'SET_TREE',
//             payload: contents
//           });
//         });
//     }
//   }, [match, dispatch]);
//   let renderedTree: ReturnType<typeof createElement> = <div>here</div>;
//   if (store && store.tree.fullPath) {
//     renderedTree = recursiveCreate(store.tree);
//   } else {
//     renderedTree = <div>Loading ...</div>;
//   }
// 
//   return renderedTree;
// }
// 
// export default Explorer
