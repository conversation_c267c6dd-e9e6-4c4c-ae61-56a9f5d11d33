"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const mcp_js_1 = require("@modelcontextprotocol/sdk/server/mcp.js");
const stdio_js_1 = require("@modelcontextprotocol/sdk/server/stdio.js");
const zod_1 = require("zod");
const supabaseAgent_1 = require("./supabaseAgent");
const server = new mcp_js_1.McpServer({
    name: "supabase-mcp-server",
    version: "1.0.0",
}, {
    capabilities: {
        tools: {},
    },
});
server.tool("supabase_action", "Perform database operations like fetch, insert, update, or delete on a Supabase database.", {
    action: zod_1.z
        .enum(["fetch", "insert", "update", "delete"])
        .describe("The type of database operation to perform."),
    table: zod_1.z
        .string()
        .describe("The name of the table to perform the operation on."),
    filters: zod_1.z
        .record(zod_1.z.string(), zod_1.z.any())
        .optional()
        .describe("Filters for selecting records (used in fetch, update, delete)."),
    data: zod_1.z
        .record(zod_1.z.string(), zod_1.z.any())
        .optional()
        .describe("Data to insert or update records."),
}, (inputVars) => __awaiter(void 0, void 0, void 0, function* () {
    try {
        const result = yield (0, supabaseAgent_1.prompt)(JSON.stringify(inputVars));
        return {
            content: [
                {
                    type: "text",
                    text: JSON.stringify(result),
                },
            ],
        };
    }
    catch (error) {
        return {
            content: [
                {
                    type: "text",
                    text: JSON.stringify({ status: "error", message: error.message }),
                },
            ],
        };
    }
}));
function main() {
    return __awaiter(this, void 0, void 0, function* () {
        const transport = new stdio_js_1.StdioServerTransport();
        yield server.connect(transport);
        console.error("Supabase MCP Server running on stdio");
    });
}
main().catch((error) => {
    console.error("Fatal error in main():", error);
    process.exit(1);
});
