import React, {FC, useEffect, useCallback, useRef} from 'react';
import {subscribe} from '../websocket';

const SocketLogs: FC = function SocketLogs() {
  const logRoot = useRef<HTMLPreElement>(null);
  const onLog = useCallback((logs: string) => {
    if (logRoot.current) {
      logRoot.current.innerText = logs;
      logRoot.current.scrollTop = logRoot.current.scrollHeight;
    }
  }, [logRoot]);

  useEffect(() => {
    return subscribe("onLog", onLog)
  }, [onLog]);

  return (<div>
      <pre 
        ref={logRoot} 
        style={{fontSize: 10, height: 'calc(100vh - 28px)', overflow: 'auto'}}
      />
    </div>
  );
}

export default SocketLogs;
