"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.logReplacer = logReplacer;
exports.prompt = prompt;
const path_1 = __importDefault(require("path"));
const promises_1 = require("node:fs/promises");
const cliconfig_1 = require("../database/cliconfig");
const messageparsing_1 = require("./messageparsing");
const sdk_1 = __importDefault(require("@anthropic-ai/sdk"));
const stdio_js_1 = require("@modelcontextprotocol/sdk/client/stdio.js");
const chalk_1 = __importDefault(require("chalk"));
let claude = null;
let mcp = {
    graphql: null,
    supabase: null,
};
function initialize() {
    return __awaiter(this, void 0, void 0, function* () {
        if (!claude) {
            const rec = (0, cliconfig_1.getAPIKey)("claude");
            if (rec) {
                claude = new sdk_1.default({ apiKey: rec.apikey });
            }
        }
        return claude;
    });
}
const transports = new Map();
const tools = [];
function connectToMCPServer(serverScriptPath, mcp, env) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const isJS = serverScriptPath.endsWith(".js");
            const isPy = serverScriptPath.endsWith(".py");
            const isTS = serverScriptPath.endsWith(".ts");
            // if (!isJS && !isPy && !isTS) {
            //   throw new Error("Server script must be .js, .py or .ts file");
            // }
            let command;
            if (isJS) {
                command = process.execPath;
            }
            else if (isPy) {
                command = "python3";
            }
            else if (isTS) {
                command = path_1.default.resolve(__dirname, "..", "node_modules", ".bin", "ts-node");
            }
            else {
                command = serverScriptPath;
            }
            let transport;
            if (isJS || isPy || isTS) {
                transport = new stdio_js_1.StdioClientTransport({
                    command,
                    args: [serverScriptPath],
                    env,
                });
            }
            else {
                transport = new stdio_js_1.StdioClientTransport({
                    command,
                    env,
                });
            }
            transports.set(serverScriptPath, transport);
            yield mcp.connect(transport);
            // TODO: Hack because supabase mcp takes time to start. @yashwanth take a look
            // await new Promise((resolve) => {
            //   setTimeout(() => {
            //     resolve({});
            //   }, 25000);
            // });
            const toolResult = yield mcp.listTools();
            for (let i = 0; i < toolResult.tools.length; ++i) {
                const tool = toolResult.tools[i];
                tools.push({
                    name: tool.name,
                    description: tool.description,
                    input_schema: tool.inputSchema,
                });
            }
            console.log(chalk_1.default.green("Connected to mcp server with tools: " +
                tools.map((it) => it.name).join(",")));
        }
        catch (err) {
            console.error(chalk_1.default.red("Failed to connect to mcp server: " + serverScriptPath), err);
        }
    });
}
function narrowFormat(format) {
    return ["image/jpeg", "image/png", "image/gif", "image/webp"].includes(format);
}
function logReplacer(key, value) {
    if (key === "data" && typeof value === "string" && value.length > 20) {
        return value.slice(0, 20) + "...[truncated]";
    }
    return value;
}
function prompt(type, appId, userPrompt, res, model) {
    return __awaiter(this, void 0, void 0, function* () {
        var _a;
        console.log("[PLANNER AGENT] starting");
        let finalizedPrompt = [];
        if (typeof userPrompt === "string") {
            const imgTagStart = userPrompt.indexOf("<img");
            if (imgTagStart >= 0) {
                console.log("[PLANNER AGENT] found image tag in prompt");
                const format = userPrompt.match(/<img\ssrc="data:(.+);base64,/);
                if (format && format[1]) {
                    console.log("[PLANNER AGENT] matching on image tag successful");
                    const originalMessage = userPrompt;
                    const dataStart = userPrompt.indexOf(";base64,") + ";base64,".length;
                    const dataEnd = userPrompt.indexOf("\"", dataStart);
                    userPrompt = userPrompt.slice(0, imgTagStart) +
                        " removed_image_to_save_tokens " +
                        userPrompt.slice(userPrompt.indexOf(">", dataEnd));
                    if (narrowFormat(format[1])) {
                        finalizedPrompt.push({
                            type: 'image',
                            source: {
                                type: 'base64',
                                media_type: format[1],
                                data: originalMessage.slice(dataStart, dataEnd)
                            }
                        });
                    }
                    else {
                        console.error("Unacceptable image format detected in prompt. Dropping image: ", format[1]);
                    }
                    finalizedPrompt.push({
                        type: 'text',
                        text: userPrompt
                    });
                    console.log("[PLANNER AGENT] Finalized prompt: ", JSON.stringify(finalizedPrompt, logReplacer, 2));
                }
                else {
                    finalizedPrompt.push({
                        type: 'text',
                        text: userPrompt
                    });
                    console.log("[PLANNER AGENT] Finalized prompt is text only because image format was weird: ", format, JSON.stringify(finalizedPrompt, logReplacer, 2));
                }
            }
            else {
                finalizedPrompt.push({
                    type: 'text',
                    text: userPrompt
                });
                console.log("[PLANNER AGENT] Finalized text only prompt", JSON.stringify(finalizedPrompt, logReplacer, 2));
            }
        }
        else if (userPrompt.isToolResponse) {
            finalizedPrompt.push({
                type: "tool_result",
                tool_use_id: userPrompt.toolId,
                content: userPrompt.response
            });
        }
        else {
            console.error("Invalid prompt was found!");
            res.status(400);
            res.end();
            return;
        }
        const claude = yield initialize();
        let result = "";
        if (!claude || !mcp) {
            result = "No anthropic client found!";
            console.log("[PLANNER AGENT] Failed because no agent was found");
            if (!mcp) {
                console.log("[PLANNER AGENT] Failed to connect to mcp client");
            }
        }
        else {
            const openedApp = (0, cliconfig_1.getOpenApp)(appId);
            if (openedApp) {
                // const filePath = path.resolve(
                //   openedApp.repoPath,
                //   "remoteCode",
                //   "plugins",
                //   assetName,
                //   "source",
                //   "component.jsx"
                // );
                // const fileStream = createWriteStream(filePath, { flags: "w" });
                // res.write(`\nresult will be written to ${filePath}\n`);
                let chat = (0, cliconfig_1.getChat)(appId, "claude", model);
                if (!chat) {
                    chat = (0, cliconfig_1.createChat)(appId, type, "claude", model);
                }
                if (!chat) {
                    res.write(`\nfailed to create chat for: ${appId}`);
                }
                else {
                    (0, cliconfig_1.createChatMessageWithContentAndRole)(chat.id, "user", JSON.stringify(finalizedPrompt));
                }
                const initialPrompt = yield (0, promises_1.readFile)(path_1.default.resolve(__dirname, "../systemPrompts/appPlanner.html"), { encoding: "utf8" });
                const messages = [
                // {role: 'user', content: [{type: 'text', text: initialPrompt}]}
                ];
                if (chat) {
                    // We remove all tool calls from history for claude
                    const history = (0, cliconfig_1.getChatMessages)(chat.id, "ASC");
                    for (let i = 0; i < history.length; ++i) {
                        const msg = history[i];
                        let parsedMessage;
                        try {
                            parsedMessage = JSON.parse(msg.content) || "<lost_data/>";
                            if (msg.role === "user" || msg.role === "assistant") {
                                messages.push({
                                    role: msg.role,
                                    content: parsedMessage,
                                });
                            }
                            else {
                                console.error("[PLANNER AGENT] unrecognized role in chat history", msg);
                            }
                        }
                        catch (err) {
                            parsedMessage = msg.content || "<lost_data/>";
                            if (msg.role === "user" || msg.role === "assistant") {
                                messages.push({
                                    role: msg.role,
                                    content: parsedMessage,
                                });
                            }
                        }
                    }
                }
                let ingestedResponse = { messageText: "", status: "", toolInput: { tool: "", partialInput: "", id: "" } };
                try {
                    // TODO(gaurav): if input token count is larger than limit remove history messages
                    let prevTokens;
                    do {
                        const tokenCount = yield claude.messages.countTokens({
                            model,
                            system: initialPrompt,
                            messages,
                        });
                        prevTokens = tokenCount.input_tokens;
                        if (prevTokens > 40000) {
                            const droppedMessage = messages.shift();
                            console.log("[PLANNER AGENT] Dropping message because of token limit: ", JSON.stringify(droppedMessage, logReplacer, 2));
                        }
                    } while (prevTokens > 40000 && messages.length > 0);
                    console.log("[PLANNER AGENT] Triggering completion");
                    const responseStream = claude.messages.stream({
                        system: `You are a planner agent that creates a project plan for a react-native app in the apptile nocode platform. You will create an actionable list of tasks that can be performed to create an app according to the user's request. you can use the tools available to you to perform these tasks. You will not be writing any react-native code directly. The tools you have access to will create screens, navigators and tiles (react-native components with special wrapper HOC). There is another agent that can generate the code for the tiles you create. A tile can be thought of a react-native component whose props can be controlled using a storybook like interface through the apptile nocode platform.`,
                        model,
                        messages,
                        max_tokens: 16000,
                        tool_choice: {
                            type: "auto",
                            disable_parallel_tool_use: true,
                        },
                        tools: [
                            {
                                name: "list_tiles_in_screen",
                                description: "List the tiles that are in the screen currently",
                                input_schema: {
                                    type: "object",
                                    required: ["name", "screenname"],
                                    properties: {
                                        name: {
                                            type: "string",
                                            description: "name of the tile to drop",
                                        },
                                        screenname: {
                                            type: "string",
                                            description: "name of the screen to drop the tile in",
                                        },
                                    },
                                },
                            },
                            {
                                name: "add_tile_to_screen",
                                description: "Drop a tile inside a screen",
                                input_schema: {
                                    type: "object",
                                    required: ["name", "screenname"],
                                    properties: {
                                        name: {
                                            type: "string",
                                            description: "name of the tile to drop",
                                        },
                                        screenname: {
                                            type: "string",
                                            description: "name of the screen to drop the tile in",
                                        },
                                    },
                                },
                            },
                            {
                                name: "create_tile",
                                description: "Create a react-native component that can be dropped into a screen",
                                input_schema: {
                                    type: "object",
                                    required: ["name", "prompt"],
                                    properties: {
                                        name: {
                                            type: "string",
                                            description: "name of the tile to create",
                                        },
                                        prompt: {
                                            type: "string",
                                            description: "the prompt to pass on to the agent that can write the code for the plugin. This agent can fetch data from shopify store attached to the project.",
                                        },
                                    },
                                },
                            },
                            {
                                name: "list_screens",
                                description: "Get the list of existing screens. Returns a tree hierarchy of @react-navigation navigators with the screens attached in them",
                                input_schema: {
                                    type: "object",
                                    properties: {},
                                },
                            },
                            {
                                name: "create_screen",
                                description: "Create a @react-navigation screen in the apptile nocode platform",
                                input_schema: {
                                    type: "object",
                                    required: ["name"],
                                    properties: {
                                        name: {
                                            type: "string",
                                            description: "name of the screen to create",
                                        },
                                    },
                                },
                            },
                        ],
                    });
                    console.log("[PLANNER AGENT] starting ingestion of response stream");
                    ingestedResponse = yield (0, messageparsing_1.ingestClaudeResponseStream)(responseStream, res);
                }
                catch (err) {
                    console.error("[PLANNER AGENT] Failed when communicating with anthropic", err);
                    res.write("\nerror happened\n");
                    res.write(((_a = err === null || err === void 0 ? void 0 : err.error) === null || _a === void 0 ? void 0 : _a.message) || "\nerror has no description\n");
                }
                if (chat) {
                    if (ingestedResponse.messageText) {
                        (0, cliconfig_1.createChatMessageWithContentAndRole)(chat.id, "assistant", ingestedResponse.messageText);
                    }
                    if (ingestedResponse.status === "ToolCallRequested") {
                        let inputVars = {};
                        if (ingestedResponse.toolInput) {
                            try {
                                inputVars = JSON.parse(ingestedResponse.toolInput.partialInput);
                            }
                            catch (err) {
                                console.error("[PLANNER AGENT] could not parse toolInput ", ingestedResponse.toolInput.partialInput);
                            }
                            (0, cliconfig_1.createChatMessageWithContentAndRole)(chat.id, "assistant", JSON.stringify([{
                                    id: ingestedResponse.toolInput.id,
                                    input: inputVars,
                                    name: ingestedResponse.toolInput.tool,
                                    type: "tool_use"
                                }]));
                        }
                    }
                }
                else {
                    console.error("[PLANNER AGENT] Chat session is active but no chat found!");
                }
                res.write("compilation finished\n");
                res.end();
            }
            else {
                result = "App not found!";
                console.log("[PLANNER AGENT] Failed because no app was found");
            }
        }
        return result;
    });
}
