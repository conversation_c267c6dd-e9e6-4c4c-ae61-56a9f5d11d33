"use strict";
// Check if ~/.apptile-cli exists. If it doesn't create it and add workspaces table.
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.db = void 0;
exports.createChat = createChat;
exports.getChat = getChat;
exports.getChatMessages = getChatMessages;
exports.createChatMessageWithContentAndRole = createChatMessageWithContentAndRole;
exports.createToolChatMessage = createToolChatMessage;
exports.getAPIKey = getAPIKey;
exports.deleteMetroProc = deleteMetroProc;
exports.getCurrentMetroProc = getCurrentMetroProc;
exports.createMetroProc = createMetroProc;
exports.createWorkspace = createWorkspace;
exports.deleteLink = deleteLink;
exports.getWorkspaces = getWorkspaces;
exports.getWorkspace = getWorkspace;
exports.getWorkspaceByLocation = getWorkspaceByLocation;
exports.insertOrUpdateOpenapp = insertOrUpdateOpenapp;
exports.getOpenApp = getOpenApp;
exports.closeAppByLocation = closeAppByLocation;
const better_sqlite3_1 = __importDefault(require("better-sqlite3"));
const os_1 = __importDefault(require("os"));
const path_1 = __importDefault(require("path"));
const fs_1 = require("fs");
const types_1 = require("../ui/src/types");
const migrate_1 = require("./migrate");
const homeDir = os_1.default.homedir();
try {
    (0, fs_1.mkdirSync)(path_1.default.resolve(homeDir, '.apptile-cli'));
}
catch (err) {
    if ((err === null || err === void 0 ? void 0 : err.code) === 'EEXIST') {
        console.log("config folder already exists");
    }
    else {
        throw err;
    }
}
const dbFilePath = path_1.default.resolve(homeDir, '.apptile-cli/db.sqlite');
exports.db = new better_sqlite3_1.default(dbFilePath); // , { verbose: console.log });
(0, migrate_1.runMigrations)(exports.db);
function narrowChat(raw) {
    return (raw &&
        (typeof raw.id === 'number') &&
        (typeof raw.outputFile === 'string') &&
        (['widget', 'datasource', 'app'].includes(raw.type)) &&
        (typeof raw.createdAt === 'string') &&
        (["openai", "claude"].includes(raw.llm_provider)) &&
        (typeof raw.llm_model === "string"));
}
const selectChat = exports.db.prepare(`SELECT * FROM chat WHERE id = ?`);
const insertChat = exports.db.prepare(`INSERT INTO chat (outputFile, type, llm_provider, llm_model) VALUES (?,?,?,?)`);
function createChat(filePath, type, provider, model) {
    const { lastInsertRowid } = insertChat.run(filePath, type, provider, model);
    const chat = selectChat.get(lastInsertRowid);
    if (narrowChat(chat)) {
        return chat;
    }
    else {
        return null;
    }
}
const selectChatForArtefact = exports.db.prepare(`SELECT * FROM chat WHERE outputFile = ? AND llm_provider = ? AND llm_model = ?`);
function getChat(filePath, provider, model) {
    if (!model) {
        console.warn("setting model to gpt-4o");
        model = 'gpt-4o';
    }
    let chat = selectChatForArtefact.get(filePath, provider, model);
    if (narrowChat(chat)) {
        return chat;
    }
    else {
        return null;
    }
}
function narrowChatMessage(raw) {
    try {
        return (raw &&
            (typeof raw.id === 'number') &&
            (typeof raw.chatId === 'number') &&
            (['developer', 'system', 'user', 'tool', 'assistant'].includes(raw.role)) &&
            (!raw.name || typeof raw.name === 'string') &&
            (typeof raw.content === 'string') &&
            (!raw.tool_call_id || typeof raw.tool_call_id === 'string') &&
            (typeof raw.createdAt === 'string'));
    }
    catch (err) {
        console.error("Failed to validate chat message: ", raw);
        return false;
    }
}
function narrowChatMessageArray(raw) {
    let result = false;
    try {
        result = Array.isArray(raw);
        for (let i = 0; i < raw.length; ++i) {
            result = result && narrowChatMessage(raw[i]);
        }
    }
    catch (err) {
        console.error("Failed to validate chat message array", err);
    }
    return result;
}
const selectMessagesByChatDesc = exports.db.prepare(`SELECT * FROM chat_message WHERE chatId = ? ORDER BY createdAt DESC`);
const selectMessagesByChatAsc = exports.db.prepare(`SELECT * FROM chat_message WHERE chatId = ? ORDER BY createdAt ASC`);
function getChatMessages(chatId, dateOrder) {
    let messages;
    if (dateOrder === 'DESC') {
        messages = selectMessagesByChatDesc.all(chatId);
    }
    else {
        messages = selectMessagesByChatAsc.all(chatId);
    }
    if (narrowChatMessageArray(messages)) {
        return messages;
    }
    else {
        return [];
    }
}
const insertChatWithContentAndRole = exports.db.prepare(`INSERT INTO chat_message (chatId, role, content) VALUES (?,?,?)`);
function createChatMessageWithContentAndRole(chatId, role, content) {
    insertChatWithContentAndRole.run(chatId, role, content);
}
const insertToolCall = exports.db.prepare(`INSERT INTO chat_message (chatId, role, content, tool_call_id) VALUES (?,?,?,?)`);
function createToolChatMessage(chatId, role, content, tool_call_id) {
    insertToolCall.run(chatId, role, content, tool_call_id);
}
function narrowAPIKey(raw) {
    try {
        if (raw &&
            (typeof raw.id === 'number') &&
            (["openai", "claude"].includes(raw.type)) &&
            (typeof raw.apikey === 'string')) {
            return true;
        }
    }
    catch (err) {
        console.error("Failed to validate APIKey", err);
    }
    return false;
}
const selectAPIKey = exports.db.prepare(`SELECT * FROM apikey where type=?`);
function getAPIKey(type) {
    let key = selectAPIKey.get(type);
    if (narrowAPIKey(key)) {
        return key;
    }
    else {
        return null;
    }
}
function narrowMetroProc(raw) {
    try {
        return (typeof raw.pid === "number") &&
            (typeof raw.repoPath === "string");
    }
    catch (err) {
        console.error("Failed to narrow metro proc");
        return false;
    }
}
function narrowMetroProcArray(raw) {
    try {
        let result = true;
        for (let i = 0; i < raw.length; ++i) {
            result = result && narrowMetroProc(raw[i]);
        }
        return result;
    }
    catch (err) {
        console.error("Failed to validate metroProc array");
        return false;
    }
}
const delMetroProc = exports.db.prepare(`DELETE FROM metro WHERE repoPath=?`);
function deleteMetroProc(repoPath) {
    delMetroProc.run(repoPath);
}
const selectMetroProc = exports.db.prepare(`SELECT * FROM metro`);
function getCurrentMetroProc() {
    let procs = selectMetroProc.all();
    if (narrowMetroProcArray(procs)) {
        return procs;
    }
    else {
        return null;
    }
}
const insertMetroProc = exports.db.prepare(`INSERT INTO metro (pid, repoPath) VALUES (?,?)`);
function createMetroProc(pid, fullPath) {
    insertMetroProc.run(pid.toFixed(0), fullPath);
}
const insertWorkspace = exports.db.prepare(`INSERT INTO workspaces (location)
               VALUES (?)`);
function createWorkspace(location) {
    try {
        insertWorkspace.run(location);
    }
    catch (err) {
        console.error("Failed to create link", err);
    }
}
const removeWorkspace = exports.db.prepare(`DELETE FROM workspaces WHERE id = ?`);
function deleteLink(id) {
    removeWorkspace.run(id);
}
const getAllWorkspaces = exports.db.prepare(`SELECT * FROM workspaces`);
function getWorkspaces() {
    const workspaces = getAllWorkspaces.all();
    if (types_1.DataBase.narrowWorkspaceArray(workspaces)) {
        return workspaces;
    }
    else {
        console.error("Invalid link found in table", workspaces);
        return null;
    }
}
const findWorkspace = exports.db.prepare(`SELECT * FROM workspaces WHERE id = ?`);
function getWorkspace(id) {
    const workspace = findWorkspace.get(id.toFixed(0));
    if (types_1.DataBase.narrowWorkspace(workspace)) {
        return workspace;
    }
    else {
        console.error("Invalid workspace found in table", workspace);
        return null;
    }
}
const findWorkspaceByLocation = exports.db.prepare(`SELECT * FROM workspaces WHERE location = ?`);
function getWorkspaceByLocation(location) {
    const workspace = findWorkspaceByLocation.get(location);
    if (types_1.DataBase.narrowWorkspace(workspace)) {
        return workspace;
    }
    else {
        console.error("Invalid workspace found in table", workspace);
        return null;
    }
}
function narrowOpenedApp(result) {
    return ((result) &&
        (typeof (result.appId) === "string") &&
        (typeof (result.workspaceId === "string")) &&
        (typeof (result.repoPath) === "string") &&
        (!!result.repoPath) &&
        ((result.apptilecookie === null) || (typeof result.apptilecookie === "string")));
}
const insertOrUpdateOpenapps = exports.db.prepare(`INSERT INTO openapps (workspaceId, appId, repoPath) 
  VALUES (?, ?, ?)
  ON CONFLICT(appId)
  DO UPDATE SET 
    workspaceId = excluded.workspaceId,
    repoPath = excluded.repoPath
  `);
function insertOrUpdateOpenapp(workspaceId, appId, repoPath) {
    insertOrUpdateOpenapps.run(workspaceId, appId, repoPath);
}
const findOpenApp = exports.db.prepare(`SELECT * FROM openapps WHERE appId = ?`);
function getOpenApp(appId) {
    const openedAppEntry = findOpenApp.get(appId);
    if (narrowOpenedApp(openedAppEntry)) {
        return openedAppEntry;
    }
    else {
        return null;
    }
}
const deleteAppByLocationQuery = exports.db.prepare(`DELETE FROM openapps WHERE repoPath = ?`);
function closeAppByLocation(repoPath) {
    return deleteAppByLocationQuery.run(repoPath);
}
exports.default = exports.db;
