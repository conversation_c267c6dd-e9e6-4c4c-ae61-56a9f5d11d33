import React, {FC, useState, useEffect} from 'react';
import AsyncButton from '../components/AsyncButton';
import Button from '../components/Button';
import {FileSystem} from '../types';
import {StoreState} from '../store';
import {handlePathOpen} from '../fsOperations';

const formatter = Intl.DateTimeFormat('en-US', {
  dateStyle: 'long',
  timeStyle: 'short',
});

type PublishingProps = {
  repoData: FileSystem.Repo; 
  appData: StoreState['appData'];
}
const Publishing: FC<PublishingProps> = function Publishing({repoData, appData}) {
  const [s3BundleFilter, setS3BundleFilter] = useState('plugins');
  const [pushLog, setPushLog] = useState({
    iosBundleId: "-1",
    androidBundleId: "-1",
    pluginsBundleId: "-1",
    navigatorsBundleId: "-1",
    publishedCommitId: "-1",
    comment: '',
    show: false
  });

  const appId = repoData.appId;
  useEffect(() => {
    if (appData.loadingInfo.status === "success" && appData.payload.manifest.forks.length > 0) {
      setPushLog(prev => {
        return {
          ...prev,
          publishedCommitId: appData.payload.manifest.forks[0].publishedCommitId.toString()
        };
      });
    }
  }, [appData, setPushLog]);

  const logBundle = (id: string|number) => {
  // ["appId", "androidBundleId", "iosBundleId", "pluginsBundleId", "navigatorsBundleId", "publishedCommitId", "comment"]
    if (appData.loadingInfo.status === "success") {
      const bundle = appData.payload.bundles.find(it => it.id === id);
      if (bundle && bundle.type === 'ios-jsbundle') {
        setPushLog(prev => {
          return {
            ...prev,
            show: true,
            iosBundleId: bundle.id.toFixed()
          };
        })
      } else if (bundle && bundle.type === 'android-jsbundle') {
        setPushLog(prev => {
          return {
            ...prev,
            show: true,
            androidBundleId: bundle.id.toFixed()
          };
        })
      } else if (bundle && bundle.type === 'plugins') {
        setPushLog(prev => {
          return {
            ...prev,
            show: true,
            pluginsBundleId: bundle.id.toFixed()
          };
        })
      }  else if (bundle && bundle.type === 'navigators') {
        setPushLog(prev => {
          return {
            ...prev,
            show: true,
            navigatorsBundleId: bundle.id.toFixed()
          };
        })
      } 
    }
  }

  const publishBundle = async (id: string|number) => {
    await fetch(`${process.env.REACT_APP_SERVER_URL}/plugin-server/home/<USER>/bundles/${id}`, {method: 'POST'});
    window.parent.postMessage({type: "requestAppData"}, "*");
  }

  const uploadWebBundles = async () => {
    await fetch(`${process.env.REACT_APP_SERVER_URL}/plugin-server/home/<USER>/uploadWebBundle`, {method: 'POST'});
    window.parent.postMessage({type: "requestAppData"}, "*");
  }

  const uploadMobileBundle = async (bundleTimestamp: number, os: 'android'|'ios') => {
    await fetch(`${process.env.REACT_APP_SERVER_URL}/plugin-server/home/<USER>/uploadMobileBundle/${bundleTimestamp}/${os}`, {method: 'POST'})

    window.parent.postMessage({type: "requestAppData"}, "*");
  } 

    let publishedBundles = null;
    let s3Bundles = null;
    if (['inprogress', 'notstarted'].includes(appData.loadingInfo.status)) {
      publishedBundles = <div>Loading...</div>;
      s3Bundles = <div>Loading...</div>;
    } else if (appData.loadingInfo.status === 'error') {
      publishedBundles = <div>Error happened: {appData.loadingInfo.errorMessage}</div>
      s3Bundles = <div>Error happened: {appData.loadingInfo.errorMessage}</div>
    } else if (appData.loadingInfo.status === 'success') {
      let artefacts = appData.payload.manifest.codeArtefacts;
      const bundleTypes = ['plugins', 'navigators', 'ios-jsbundle', 'android-jsbundle'];
      const publishedIds: Array<number|'NULL'> = [];
      for (let bundleType of bundleTypes) {
        let artefact = artefacts.find(it => it.type === bundleType);
        if (artefact) {
          publishedIds.push(artefact.id);
        } else {
          publishedIds.push('NULL');
        }
      }
      publishedBundles = bundleTypes
        .map((bundleType, i) => {
          let activeClass = ""
          if (bundleType === s3BundleFilter) {
            activeClass = "border-orange-500";
          }

          return (
            <div key={bundleType} 
              className={`flex flex-col flex-1 border border-1 m-1 rounded justify-between ${activeClass} cursor-pointer`}
              onClick={() => setS3BundleFilter(bundleType)}
            >
              <div className="w-full flex justify-center text-center" style={{fontSize: 8}}>
                {bundleType}
              </div>
              <div className="w-full flex justify-center">{publishedIds[i]}</div>
            </div>
          );
      });
      publishedBundles = (<div className="flex flex-row">
        {publishedBundles}
      </div>)

      s3Bundles = appData.payload.bundles.filter(it => it.type === s3BundleFilter).map((it, i) => {
        return (
          <div key={i} className="flex flex-row justify-between w-full items-center px-2">
            <span>{it.id}</span>
            <div className="flex flex-row gap-1">
              <Button label="Log" onClick={() => logBundle(it.id)}/>
              <AsyncButton label="Publish" onClick={() => publishBundle(it.id)}/>
            </div>
          </div>
        );
      })
    }

  const renderLocalBundles = (bundles: FileSystem.LocalMobileBundle[], bundleType: 'ios'|'android', label: string) => {
    let rows = [<div className="p-2">no bundles yet</div>];
    if (bundles.length > 0) {
      rows = bundles.map(bundle => {
        return (<div key={bundle.fullPath} className="flex flex-row items-center justify-between px-2">
          <span>{formatter.format(bundle.timestamp)}</span>
          <AsyncButton label="Upload" onClick={() => uploadMobileBundle(bundle.timestamp, bundleType)}/>
        </div>);
      })
    }

    let finderButton = null;
    if (bundles.length > 0) {
      finderButton = (<button 
          className="w-5"
          onClick={() => handlePathOpen(repoData.fullPath + `/remoteCode/generated/bundles/${bundleType}`, 'finder')}
        >
          <img 
            alt="open in finder"
            src={require('../assets/finder.png')}
            className="w-full"
          />
        </button>
      );
    }

    return (<>
      <div className="flex flex-row items-center justify-between w-full pr-1">
        {label}
        {finderButton}
      </div>
      <div className="max-h-24 overflow-y-auto border text-xs mb-2">
        {rows}
      </div>
    </>);
  };

  const createPushLog = async () => {
    return fetch(`${process.env.REACT_APP_SERVER_URL}/plugin-server/home/<USER>/pushLogs`, {
      method: 'POST',
      headers: {
        'Content-type': 'application/json'
      },
      body: JSON.stringify(pushLog)
    })
    .then(res => res.text());
  };

  const discardPushLog = () => {
    setPushLog(prev => {
      return {
        ...prev,
        show: false
      };
    });
  };

  let renderedPushLog = null;
  if (pushLog.show) {
    const setLogProperty = (property: "publishedCommitId"|"comment"|"navigatorsBundleId"|"pluginsBundleId"|"androidBundleId"|"iosBundleId", value: string) => {
      setPushLog(prev => {
        return {
          ...prev,
          [property]: value
        };
      });
    };

    renderedPushLog = (
      <div className="flex flex-col">
        <div className="flex flex-row flex-wrap gap-1">
          <div className="flex flex-col border justify-between items-center">
            <span className="w-full flex justify-center text-center" style={{fontSize: 8}}>ios</span> 
            <input className="w-8" value={pushLog.iosBundleId}
              onChange={ev => setLogProperty('iosBundleId', ev.target.value)}
            />
          </div>
          <div className="flex flex-col border justify-between items-center">
            <span className="w-full flex justify-center text-center" style={{fontSize: 8}}>android</span> 
            <input className="w-8" value={pushLog.androidBundleId}
              onChange={ev => setLogProperty('androidBundleId', ev.target.value)}
            />
          </div>
          <div className="flex flex-col border justify-between items-center">
            <span className="w-full flex justify-center text-center" style={{fontSize: 8}}>plugins</span> 
            <input className="w-8" value={pushLog.pluginsBundleId}
              onChange={ev => setLogProperty('pluginsBundleId', ev.target.value)}
            />
          </div>
          <div className="flex flex-col border justify-between items-center">
            <span className="w-full flex justify-center text-center" style={{fontSize: 8}}>navigators</span> 
            <input className="w-8" value={pushLog.navigatorsBundleId}
              onChange={ev => setLogProperty('navigatorsBundleId', ev.target.value)}
            />
          </div>
          <div className="flex flex-col border justify-between items-center">
            <span className="w-full flex justify-center text-center" style={{fontSize: 8}}>ota</span> 
            <input className="w-8" value={pushLog.publishedCommitId} 
              onChange={ev => setLogProperty('publishedCommitId', ev.target.value)}
            />
          </div>
        </div>
        <div className="flex flex-row gap-1">
          <div className="flex flex-1 flex-col border justify-between items-start">
            <span className="w-full flex justify-center text-center" style={{fontSize: 8}}>comment</span> 
            <input className="w-full" value={pushLog.comment}
              onChange={ev => setLogProperty('comment', ev.target.value)}
            />
          </div>
          <AsyncButton label="Send" onClick={createPushLog} />
          <Button label="Discard" onClick={discardPushLog} />
        </div>
      </div>
    );
  }

  return (
    <div className="border p-1 rounded">
      {renderedPushLog}
      <div>Published</div>
      {publishedBundles}
      <div className="max-h-24 overflow-y-auto border text-xs mb-2">
        {s3Bundles}
      </div>

      {renderLocalBundles(repoData.codePushBundles.ios, 'ios', 'Local ios-jsbundles')}
      {renderLocalBundles(repoData.codePushBundles.android, 'android', 'Local android-jsbundles')}
      <div className="flex flex-row items-center justify-between w-full pr-1">
        Local web bundles
      </div>
      <div className="max-h-24 overflow-y-auto text-xs mb-2">
        <div className="max-h-24 overflow-y-auto border text-xs mb-2 p-2">
          <AsyncButton label="Upload Current WebBundles" onClick={uploadWebBundles}/>
        </div>
      </div>
    </div>
  );
}

export default Publishing;
