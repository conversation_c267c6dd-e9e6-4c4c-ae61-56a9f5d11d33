import path from "path";
import {stat, mkdir} from "node:fs/promises";
import {Request, Response, NextFunction} from "express";
export default async function (req: Request, res: Response, next: NextFunction) {
  try {
    // ensure that workspaces table has an entry linking path to ~/apptile-cli-home
    // ensure that ~/apptile-cli-home directory exists and has ReactNativeTSProjeect
    // ensure that the ReactNativeTSProjeect has a git repo and the remote is pointed to correct github
    next();
  } catch (err: any) {
    next(err);
  }
}

export async function ensurePlugins(path: string) {
  try {
    await stat(path);
  } catch (err: any) {
    if (err?.code === "ENOENT") {
      await mkdir(path, { recursive: true });
    }
  }
}