import {https} from 'follow-redirects';
import fs from 'fs';
import path from 'path';
import {getAP<PERSON><PERSON><PERSON>} from '../database/cliconfig';

const systemPrompt = fs.readFileSync(path.resolve(__dirname, "latest.html"), {encoding: "utf8"});
const apikey = getAPIKey('claude')?.apikey;

if (!apikey) {
  console.error("Failed to retrieve claude's apikey");
  process.exit(1);
}

var options: any = {
  'method': 'POST',
  'hostname': 'api.anthropic.com',
  'path': '/v1/messages/count_tokens',
  'headers': {
    'x-api-key': apikey,
    'content-type': 'application/json',
    'anthropic-version': '2023-06-01'
  },
  'maxRedirects': 20
};

var req = https.request(options, function (res) {
  var chunks: any[] = [];

  res.on("data", function (chunk) {
    chunks.push(chunk);
  });

  res.on("end", function () {
    var body = Buffer.concat(chunks);
    console.log(body.toString());
  });

  res.on("error", function (error) {
    console.error(error);
  });
});

var postData = JSON.stringify({
  "model": "claude-3-7-sonnet-20250219",
  "system": systemPrompt,
  "messages": [
    {
      "role": "user",
      "content": "Hello, Claude"
    }
  ]
});

req.write(postData);

req.end();
