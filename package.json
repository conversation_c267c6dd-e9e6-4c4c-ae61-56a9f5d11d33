{"name": "@apptile/cli", "version": "1.0.30", "main": "dist/index.js", "bin": {"apptile-cli": "./dist/index.js"}, "scripts": {"predev": "sequelize-cli db:migrate", "watch": "nodemon", "debug": "node --inspect-brk=127.0.0.1:9233 ./node_modules/.bin/ts-node index.ts", "dev": "ts-node index.ts", "build": "rm -rf dist && rm -rf public/ui && tsc -p tsconfig.json && cp -r public ./dist/ && cp -r templates ./dist/ && cp -r systemPrompts ./dist/ && cp -r database ./dist/ && cp ./controllers/agents/mcp-servers/toolImplementations/shopifyschema.graphqls ./dist/controllers/agents/mcp-servers/toolImplementations/shopifyschema.graphqls && cp -r config ./dist && cd ui && npm run build && cd ..", "build-local": "rm -rf dist && rm -rf public/ui && tsc -p tsconfig.json && cp -r public ./dist/ && cp -r templates ./dist/ && cp -r systemPrompts ./dist/ && cp ./controllers/agents/mcp-servers/toolImplementations/shopifyschema.graphqls ./dist/controllers/agents/mcp-servers/toolImplementations/shopifyschema.graphqls  && cd ui && npm run build-local && cd ..", "start": "node dist/index.js", "migrate": "sequelize-cli db:migrate", "clean": "rm -rf dist && rm -rf public/ui"}, "author": "", "license": "ISC", "description": "", "dependencies": {"@anthropic-ai/sdk": "^0.39.0", "@apollo/client": "^3.13.2", "@aws-sdk/client-bedrock-runtime": "^3.828.0", "@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.4", "@babel/preset-react": "^7.24.7", "@babel/runtime": "^7.27.0", "@modelcontextprotocol/sdk": "^1.7.0", "@react-native/babel-preset": "^0.75.4", "@sentry/node": "^7.73.0", "@sentry/profiling-node": "^1.2.1", "@types/archiver": "^6.0.2", "@types/axios": "^0.14.4", "@types/better-sqlite3": "^7.6.11", "@types/blessed": "^0.1.25", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.17", "@types/diff": "^7.0.2", "@types/express": "^5.0.0", "@types/follow-redirects": "^1.14.4", "@types/markdown-it": "^14.1.2", "@types/node": "^22.13.10", "@types/unzipper": "^0.10.10", "@types/ws": "^8.5.12", "apptile-backend-utils": "github:clearsight-dev/apptile-backend-utils#v0.1.5", "archiver": "^7.0.1", "axios": "^1.7.7", "babel-loader": "^9.2.1", "better-sqlite3": "^11.5.0", "blessed": "^0.1.81", "body-parser": "^1.20.3", "chalk": "4.1.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "diff": "^7.0.0", "dotenv": "^16.4.5", "express": "^4.21.0", "express-winston": "^4.2.0", "form-data": "^4.0.1", "fuse.js": "^7.1.0", "graphql": "^16.10.0", "js-sha256": "^0.11.0", "markdown-it": "^14.1.0", "openai": "^4.78.1", "pg": "^8.16.0", "plist": "^3.1.0", "pug": "^3.0.3", "react": "^18.3.1", "react-native-web": "^0.19.12", "sequelize": "^6.37.6", "tiktoken": "^1.0.21", "ts-morph": "^25.0.1", "ts-node": "^10.9.2", "typescript": "^5.6.2", "unzipper": "^0.12.3", "uuid": "^11.1.0", "webpack": "^5.94.0", "webpack-cli": "^5.1.4", "webpack-dev-middleware": "^7.4.2", "winston": "^3.17.0", "ws": "^8.18.0", "zod": "^3.24.2", "zod-to-json-schema": "^3.24.5"}, "devDependencies": {"@types/plist": "^3.0.5", "@types/sequelize": "^4.28.11", "babel-plugin-react-native-web": "^0.19.12", "nodemon": "^3.1.10", "sequelize-cli": "^6.4.1", "supabase": "^2.19.7"}}