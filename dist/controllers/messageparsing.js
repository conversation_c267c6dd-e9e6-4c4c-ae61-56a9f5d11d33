"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __asyncValues = (this && this.__asyncValues) || function (o) {
    if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");
    var m = o[Symbol.asyncIterator], i;
    return m ? m.call(o) : (o = typeof __values === "function" ? __values(o) : o[Symbol.iterator](), i = {}, verb("next"), verb("throw"), verb("return"), i[Symbol.asyncIterator] = function () { return this; }, i);
    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }
    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ingestOpenaiMessages = ingestOpenaiMessages;
exports.ingestClaudeResponseStream = ingestClaudeResponseStream;
exports.ingestSupabaseClientResponseStream = ingestSupabaseClientResponseStream;
exports.ingestOpenaiMessageStream = ingestOpenaiMessageStream;
exports.ingestClaudeMessages = ingestClaudeMessages;
function ingestOpenaiMessages(responseStreamP) {
    return __awaiter(this, void 0, void 0, function* () {
        var _a, e_1, _b, _c;
        var _d;
        const responseStream = yield responseStreamP;
        let status = "Waiting";
        let messageText = "";
        let toolArgs = "";
        let toolInput = null;
        let toolName = "";
        try {
            for (var _e = true, responseStream_1 = __asyncValues(responseStream), responseStream_1_1; responseStream_1_1 = yield responseStream_1.next(), _a = responseStream_1_1.done, !_a; _e = true) {
                _c = responseStream_1_1.value;
                _e = false;
                const streamItem = _c;
                if (streamItem.choices.length === 0) {
                    console.warn("No choices obtained");
                }
                else if (streamItem.choices.length > 1) {
                    console.warn("Received multiple choices. Will continue with first");
                }
                // console.log("delta: ", streamItem.choices[0]?.delta);
                const choice = (_d = streamItem === null || streamItem === void 0 ? void 0 : streamItem.choices) === null || _d === void 0 ? void 0 : _d[0];
                const delta = choice === null || choice === void 0 ? void 0 : choice.delta;
                if (delta.tool_calls && delta.tool_calls[0].function) {
                    if (delta.tool_calls[0].id) {
                        toolInput = {
                            id: delta.tool_calls[0].id,
                            tool: "",
                            partialInput: ""
                        };
                    }
                    if (delta.tool_calls[0].function.arguments) {
                        toolArgs += delta.tool_calls[0].function.arguments;
                    }
                    else if (delta.tool_calls[0].function.name) {
                        toolName += delta.tool_calls[0].function.name;
                    }
                    else {
                        console.error("Failed to recognize chunk describing tool input");
                    }
                }
                else if (typeof delta.content === "string") {
                    messageText += delta.content;
                }
                else if (choice.finish_reason === "tool_calls") {
                    status = "EndTurnReached";
                    // console.log("Openai is finished because " + choice.finish_reason);
                }
                else {
                    console.error("Unrecognized delta received");
                }
            }
        }
        catch (e_1_1) { e_1 = { error: e_1_1 }; }
        finally {
            try {
                if (!_e && !_a && (_b = responseStream_1.return)) yield _b.call(responseStream_1);
            }
            finally { if (e_1) throw e_1.error; }
        }
        if (toolInput) {
            toolInput.tool = toolName;
            toolInput.partialInput = toolArgs;
            return { messageText, toolInput, end_turn: status === "EndTurnReached" };
        }
        else {
            return { messageText, toolInput: null, end_turn: status === "EndTurnReached" };
        }
    });
}
function ingestClaudeResponseStream(responseStream, res, fileStream) {
    return __awaiter(this, void 0, void 0, function* () {
        var _a, responseStream_2, responseStream_2_1;
        var _b, e_2, _c, _d;
        let status = "Waiting";
        let messageText = "";
        let toolInput = null;
        try {
            for (_a = true, responseStream_2 = __asyncValues(responseStream); responseStream_2_1 = yield responseStream_2.next(), _b = responseStream_2_1.done, !_b; _a = true) {
                _d = responseStream_2_1.value;
                _a = false;
                const streamItem = _d;
                res.write("streamItemStart:" + JSON.stringify(streamItem) + ":streamItemEnd");
                switch (streamItem.type) {
                    case "message_start":
                        {
                            // res.write('\nClaude has started responding\n')
                        }
                        break;
                    case "message_delta":
                        {
                            // console.log("Claude is done with the chat.", streamItem.delta);
                            if (streamItem.delta.stop_reason === "end_turn") {
                                console.log("Claude is done with the chat.", streamItem);
                                status = "EndTurnReached";
                            }
                            else if (streamItem.delta.stop_reason === "stop_sequence") {
                                console.log("Claude is stopping because it encountered a stop sequence");
                            }
                            else if (streamItem.delta.stop_reason === "max_tokens") {
                                console.log("Claude is stopping because max tokens were exhausted");
                                status = "MaxTokensUsed";
                            }
                            else if (streamItem.delta.stop_reason === "tool_use") {
                                console.log("Claude is stopping because it needs a tool response");
                                status = "ToolCallRequested";
                            }
                        }
                        break;
                    case "message_stop":
                        {
                            console.log("stopping: ", streamItem);
                        }
                        break;
                    case "content_block_start":
                        {
                            switch (streamItem.content_block.type) {
                                case "text":
                                    {
                                        // res.write(streamItem.content_block.text)
                                        if (fileStream) {
                                            fileStream.write(streamItem.content_block.text);
                                        }
                                        messageText = streamItem.content_block.text;
                                    }
                                    break;
                                case "tool_use":
                                    {
                                        // res.write(streamItem.content_block.name)
                                        if (fileStream) {
                                            fileStream.write(streamItem.content_block.name);
                                        }
                                        toolInput = {
                                            tool: streamItem.content_block.name,
                                            id: streamItem.content_block.id,
                                            partialInput: ''
                                        };
                                    }
                                    break;
                                default:
                                    console.error("unknown delta in content_block");
                            }
                        }
                        break;
                    case "content_block_delta":
                        {
                            switch (streamItem.delta.type) {
                                case "input_json_delta":
                                    {
                                        // res.write(streamItem.delta.partial_json)
                                        if (fileStream) {
                                            fileStream.write(streamItem.delta.partial_json);
                                        }
                                        toolInput.partialInput += streamItem.delta.partial_json;
                                    }
                                    break;
                                case "text_delta":
                                    {
                                        // res.write(streamItem.delta.text);
                                        if (fileStream) {
                                            fileStream.write(streamItem.delta.text);
                                        }
                                        messageText += streamItem.delta.text;
                                    }
                                    break;
                            }
                        }
                        break;
                    case "content_block_stop":
                        {
                            // res.write(`\n Claude has finished responding \n`);
                            if (fileStream) {
                                fileStream.write(`\n Claude has finished responding \n`);
                            }
                            // console.log("content block ended: ", streamItem);
                        }
                        break;
                    default:
                        console.error("unknown stream item: ", streamItem);
                }
            }
        }
        catch (e_2_1) { e_2 = { error: e_2_1 }; }
        finally {
            try {
                if (!_a && !_b && (_c = responseStream_2.return)) yield _c.call(responseStream_2);
            }
            finally { if (e_2) throw e_2.error; }
        }
        return { messageText, toolInput, status };
    });
}
function ingestSupabaseClientResponseStream(responseStream) {
    return __awaiter(this, void 0, void 0, function* () {
        var _a, responseStream_3, responseStream_3_1;
        var _b, e_3, _c, _d;
        let status = "Waiting";
        let messageText = "";
        let toolInput = null;
        try {
            for (_a = true, responseStream_3 = __asyncValues(responseStream); responseStream_3_1 = yield responseStream_3.next(), _b = responseStream_3_1.done, !_b; _a = true) {
                _d = responseStream_3_1.value;
                _a = false;
                const streamItem = _d;
                switch (streamItem.type) {
                    case "message_start":
                        {
                            // res.write('\nClaude has started responding\n')
                        }
                        break;
                    case "message_delta":
                        {
                            // console.log("Claude is done with the chat.", streamItem.delta);
                            if (streamItem.delta.stop_reason === "end_turn") {
                                console.log("[SUPABASE AGENT] Claude is done with the chat.", streamItem);
                                status = "EndTurnReached";
                            }
                            else if (streamItem.delta.stop_reason === "stop_sequence") {
                                console.log("[SUPABASE AGENT] Claude is stopping because it encountered a stop sequence");
                            }
                            else if (streamItem.delta.stop_reason === "max_tokens") {
                                console.log("[SUPABASE AGENT] Claude is stopping because max tokens were exhausted");
                                status = "MaxTokensUsed";
                            }
                            else if (streamItem.delta.stop_reason === "tool_use") {
                                console.log("[SUPABASE AGENT] Claude is stopping because it needs a tool response");
                                status = "ToolCallRequested";
                            }
                        }
                        break;
                    case "message_stop":
                        {
                            console.log("stopping: ", streamItem);
                        }
                        break;
                    case "content_block_start":
                        {
                            switch (streamItem.content_block.type) {
                                case "text":
                                    {
                                        // res.write(streamItem.content_block.text)
                                        messageText = streamItem.content_block.text;
                                    }
                                    break;
                                case "tool_use":
                                    {
                                        // res.write(streamItem.content_block.name)
                                        toolInput = {
                                            tool: streamItem.content_block.name,
                                            id: streamItem.content_block.id,
                                            partialInput: ''
                                        };
                                    }
                                    break;
                                default:
                                    console.error("unknown delta in content_block");
                            }
                        }
                        break;
                    case "content_block_delta":
                        {
                            switch (streamItem.delta.type) {
                                case "input_json_delta":
                                    {
                                        // res.write(streamItem.delta.partial_json)
                                        toolInput.partialInput += streamItem.delta.partial_json;
                                    }
                                    break;
                                case "text_delta":
                                    {
                                        // res.write(streamItem.delta.text);
                                        messageText += streamItem.delta.text;
                                    }
                                    break;
                            }
                        }
                        break;
                    case "content_block_stop":
                        {
                            // res.write(`\n Claude has finished responding \n`);
                            console.log("[SUPABASE CLIENT]content block ended: ", streamItem);
                        }
                        break;
                    default:
                        console.error("unknown stream item: ", streamItem);
                }
            }
        }
        catch (e_3_1) { e_3 = { error: e_3_1 }; }
        finally {
            try {
                if (!_a && !_b && (_c = responseStream_3.return)) yield _c.call(responseStream_3);
            }
            finally { if (e_3) throw e_3.error; }
        }
        return { messageText, toolInput, status };
    });
}
function ingestOpenaiMessageStream(responseStreamP, res, fileStream) {
    return __awaiter(this, void 0, void 0, function* () {
        var _a, e_4, _b, _c;
        var _d;
        const responseStream = yield responseStreamP;
        let status = "Waiting";
        let messageText = "";
        let toolArgs = "";
        let toolInput = null;
        let toolName = "";
        try {
            for (var _e = true, responseStream_4 = __asyncValues(responseStream), responseStream_4_1; responseStream_4_1 = yield responseStream_4.next(), _a = responseStream_4_1.done, !_a; _e = true) {
                _c = responseStream_4_1.value;
                _e = false;
                const streamItem = _c;
                if (streamItem.choices.length === 0) {
                    console.warn("No choices obtained");
                    res.write(`\nNo choices obtained\n`);
                }
                else if (streamItem.choices.length > 1) {
                    console.warn("Received multiple choices. Will continue with first");
                    res.write(`\nReceived multiple choces. Will continue with first\n`);
                }
                // console.log("delta: ", streamItem.choices[0]?.delta);
                const choice = (_d = streamItem === null || streamItem === void 0 ? void 0 : streamItem.choices) === null || _d === void 0 ? void 0 : _d[0];
                const delta = choice === null || choice === void 0 ? void 0 : choice.delta;
                if (delta.tool_calls && delta.tool_calls[0].function) {
                    if (delta.tool_calls[0].id) {
                        toolInput = {
                            id: delta.tool_calls[0].id,
                            tool: "",
                            partialInput: ""
                        };
                    }
                    if (delta.tool_calls[0].function.arguments) {
                        toolArgs += delta.tool_calls[0].function.arguments;
                    }
                    else if (delta.tool_calls[0].function.name) {
                        toolName += delta.tool_calls[0].function.name;
                        res.write(`\nrunning tool: ${toolName}\n`);
                    }
                    else {
                        console.error("Failed to recognize chunk describing tool input");
                    }
                }
                else if (typeof delta.content === "string") {
                    messageText += delta.content;
                    res.write(delta.content);
                    fileStream.write(delta.content);
                }
                else if (choice.finish_reason === "tool_calls") {
                    status = "EndTurnReached";
                    console.log("[OPENAI_AGENT] Openai is finished because " + choice.finish_reason);
                }
                else {
                    console.error("[OPENAI_AGENT] Unrecognized delta received");
                }
            }
        }
        catch (e_4_1) { e_4 = { error: e_4_1 }; }
        finally {
            try {
                if (!_e && !_a && (_b = responseStream_4.return)) yield _b.call(responseStream_4);
            }
            finally { if (e_4) throw e_4.error; }
        }
        if (toolInput) {
            toolInput.tool = toolName;
            toolInput.partialInput = toolArgs;
            return { messageText, toolInput, end_turn: status === "EndTurnReached" };
        }
        else {
            return { messageText, toolInput: null, end_turn: status === "EndTurnReached" };
        }
    });
}
function ingestClaudeMessages(responseStream) {
    return __awaiter(this, void 0, void 0, function* () {
        var _a, responseStream_5, responseStream_5_1;
        var _b, e_5, _c, _d;
        let status = "Waiting";
        let messageText = "";
        let toolInput = null;
        try {
            for (_a = true, responseStream_5 = __asyncValues(responseStream); responseStream_5_1 = yield responseStream_5.next(), _b = responseStream_5_1.done, !_b; _a = true) {
                _d = responseStream_5_1.value;
                _a = false;
                const streamItem = _d;
                switch (streamItem.type) {
                    case "message_start":
                        {
                            // console.log("Message started");
                        }
                        break;
                    case "message_delta":
                        {
                            // console.log("Claude is done with the chat.", streamItem.delta);
                            if (streamItem.delta.stop_reason === "end_turn") {
                                console.log("Claude is done with the chat.");
                                status = "EndTurnReached";
                            }
                        }
                        break;
                    case "message_stop":
                        {
                            // console.log("stopping: ", streamItem);
                        }
                        break;
                    case "content_block_start":
                        {
                            switch (streamItem.content_block.type) {
                                case "text":
                                    {
                                        messageText = streamItem.content_block.text;
                                    }
                                    break;
                                case "tool_use":
                                    {
                                        toolInput = {
                                            tool: streamItem.content_block.name,
                                            id: streamItem.content_block.id,
                                            partialInput: ''
                                        };
                                    }
                                    break;
                                default:
                                    console.error("unknown delta in content_block");
                            }
                        }
                        break;
                    case "content_block_delta":
                        {
                            switch (streamItem.delta.type) {
                                case "input_json_delta":
                                    {
                                        toolInput.partialInput += streamItem.delta.partial_json;
                                    }
                                    break;
                                case "text_delta":
                                    {
                                        messageText += streamItem.delta.text;
                                    }
                                    break;
                            }
                        }
                        break;
                    case "content_block_stop":
                        {
                            console.log("content block ended: ", streamItem);
                        }
                        break;
                    default:
                        console.error("unknown stream item: ", streamItem);
                }
            }
        }
        catch (e_5_1) { e_5 = { error: e_5_1 }; }
        finally {
            try {
                if (!_a && !_b && (_c = responseStream_5.return)) yield _c.call(responseStream_5);
            }
            finally { if (e_5) throw e_5.error; }
        }
        return { messageText, toolInput, end_turn: status === "EndTurnReached" };
    });
}
