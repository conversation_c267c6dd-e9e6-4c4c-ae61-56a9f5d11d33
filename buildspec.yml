version: 0.2

env:
  variables:
    ECR_REPO_NAME: "apptile-cli-server"
    AWS_REGION: "us-east-1"

phases:
  pre_build:
    commands:
      - echo Logging in to Amazon ECR...
      - aws --version
      - aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin 723464704088.dkr.ecr.${AWS_REGION}.amazonaws.com
      - REPOSITORY_URI=723464704088.dkr.ecr.${AWS_REGION}.amazonaws.com/${ECR_REPO_NAME}
      - echo $CODEBUILD_WEBHOOK_HEAD_REF
      - echo WEBHOOK_REF
      - |
        if echo "$CODEBUILD_WEBHOOK_HEAD_REF" | grep -q "^refs/heads/scion/"; then
          BRANCH_NAME=$(echo $CODEBUILD_WEBHOOK_HEAD_REF | cut -d'/' -f4-)
          IMAGE_TAG="${BRANCH_NAME}-${CODEBUILD_BUILD_NUMBER}"
        else
          IMAGE_TAG="${CODEBUILD_BUILD_NUMBER}"
        fi

  build:
    commands:
      - echo Building the Docker image...
      - echo $IMAGE_TAG
      - docker build -t ${REPOSITORY_URI}:${IMAGE_TAG} .
      - docker tag ${REPOSITORY_URI}:${IMAGE_TAG} ${REPOSITORY_URI}:latest

  post_build:
    commands:
      - echo Pushing the Docker images to ECR...
      - docker push ${REPOSITORY_URI}:${IMAGE_TAG}
      - docker push ${REPOSITORY_URI}:latest
      - echo Writing image definitions...
      - printf '[{"name":"my-app","imageUri":"%s"}]' ${REPOSITORY_URI}:${IMAGE_TAG} > imagedefinitions.json
      - |
        if [ "$CODEBUILD_BUILD_SUCCEEDING" -eq 1 ]; then
          echo "curl -L -X POST -H \"Accept: application/vnd.github+json\" -H \"Authorization: Bearer $GITHUB_ACCESS_TOKEN\" -H \"X-GitHub-Api-Version: 2022-11-28\" https://api.github.com/repos/clearsight-dev/$GITHUB_REPO_NAME/statuses/$CODEBUILD_RESOLVED_SOURCE_VERSION --data '{\"state\":\"success\",\"description\":\"BN: '$REPO_TAG'\",\"context\":\"ci/codebuild\"}'"
          curl -L \
          -X POST \
          -H "Accept: application/vnd.github+json" \
          -H "Authorization: Bearer $GITHUB_ACCESS_TOKEN" \
          -H "X-GitHub-Api-Version: 2022-11-28" \
          https://api.github.com/repos/clearsight-dev/$GITHUB_REPO_NAME/statuses/$CODEBUILD_RESOLVED_SOURCE_VERSION \
          --data '{"state":"success","description":"BN: '$IMAGE_TAG'","context":"ci/codebuild"}'
        else
          curl -L \
          -X POST \
          -H "Accept: application/vnd.github+json" \
          -H "Authorization: Bearer $GITHUB_ACCESS_TOKEN" \
          -H "X-GitHub-Api-Version: 2022-11-28" \
          https://api.github.com/repos/clearsight-dev/$GITHUB_REPO_NAME/statuses/$CODEBUILD_RESOLVED_SOURCE_VERSION \
          --data '{"state":"error","description":"failed with BN: '$IMAGE_TAG'","context":"ci/codebuild"}'
        fi

artifacts:
  files: imagedefinitions.json