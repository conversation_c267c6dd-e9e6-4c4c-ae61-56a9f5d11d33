"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.prompt = prompt;
const path_1 = __importDefault(require("path"));
const promises_1 = require("node:fs/promises");
const cliconfig_1 = require("../database/cliconfig");
const messageparsing_1 = require("./messageparsing");
const sdk_1 = __importDefault(require("@anthropic-ai/sdk"));
const index_js_1 = require("@modelcontextprotocol/sdk/client/index.js");
const stdio_js_1 = require("@modelcontextprotocol/sdk/client/stdio.js");
const chalk_1 = __importDefault(require("chalk"));
let claude = null;
let mcp = {
    supabase: null,
};
function initialize() {
    return __awaiter(this, void 0, void 0, function* () {
        if (!claude) {
            const rec = (0, cliconfig_1.getAPIKey)("claude");
            if (rec) {
                claude = new sdk_1.default({ apiKey: rec.apikey });
            }
        }
        if (!mcp.supabase) {
            mcp.supabase = new index_js_1.Client({
                name: "supabase-mcp-client",
                version: "1.0.0",
            });
            debugger;
            yield connectToMCPServer("/Users/<USER>/.local/bin/supabase-mcp-server", mcp.supabase, {
                SUPABASE_PROJECT_REF: "hlpgpqdivzmocsbgfnqj",
                SUPABASE_DB_PASSWORD: "supabasePostgresDB",
                SUPABASE_REGION: "ap-southeast-1",
            });
        }
        return claude;
    });
}
const transports = new Map();
const tools = [];
const toolsMCPMapping = {};
function connectToMCPServer(serverScriptPath, mcp, env) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const isJS = serverScriptPath.endsWith(".js");
            const isPy = serverScriptPath.endsWith(".py");
            const isTS = serverScriptPath.endsWith(".ts");
            // if (!isJS && !isPy && !isTS) {
            //   throw new Error("Server script must be .js, .py or .ts file");
            // }
            let command;
            if (isJS) {
                command = process.execPath;
            }
            else if (isPy) {
                command = "python3";
            }
            else if (isTS) {
                command = path_1.default.resolve(__dirname, "..", "node_modules", ".bin", "ts-node");
            }
            else {
                command = serverScriptPath;
            }
            let transport;
            if (isJS || isPy || isTS) {
                transport = new stdio_js_1.StdioClientTransport({
                    command,
                    args: [serverScriptPath],
                    env,
                });
            }
            else {
                transport = new stdio_js_1.StdioClientTransport({
                    command,
                    env,
                });
            }
            transports.set(serverScriptPath, transport);
            yield mcp.connect(transport);
            // TODO: Hack because supabase mcp takes time to start. @yashwanth take a look
            // await new Promise((resolve) => {
            //   setTimeout(() => {
            //     resolve({});
            //   }, 25000);
            // });
            const toolResult = yield mcp.listTools();
            for (let i = 0; i < toolResult.tools.length; ++i) {
                const tool = toolResult.tools[i];
                tools.push({
                    name: tool.name,
                    description: tool.description,
                    input_schema: tool.inputSchema,
                });
                toolsMCPMapping[tool.name] = mcp;
            }
            console.log(chalk_1.default.green("Connected to mcp server with tools: " +
                tools.map((it) => it.name).join(",")));
        }
        catch (err) {
            console.error(chalk_1.default.red("Failed to connect to mcp server: " + serverScriptPath), err);
        }
    });
}
function prompt(userPrompt) {
    return __awaiter(this, void 0, void 0, function* () {
        console.log("[SUPABASE AGENT] starting");
        const claude = yield initialize();
        let result = "";
        if (!claude || !mcp) {
            result = "No anthropic client found!";
            console.log("[SUPABASE AGENT] Failed because no agent was found");
            if (!mcp) {
                console.log("[SUPABASE AGENT] Failed to connect to mcp client");
            }
        }
        else {
            let toolRun = false;
            const initialPrompt = yield (0, promises_1.readFile)(path_1.default.resolve(__dirname, "../systemPrompts/supabase.md"), { encoding: "utf8" });
            const messages = [
                { role: "user", content: [{ type: "text", text: userPrompt }] },
            ];
            try {
                do {
                    console.log("[SUPABASE AGENT] Triggering completion");
                    const responseStream = claude.messages.stream({
                        system: initialPrompt,
                        model: "claude-3-7-sonnet-20250219",
                        messages,
                        max_tokens: 16000,
                        tool_choice: {
                            type: "auto",
                            disable_parallel_tool_use: true,
                        },
                        tools: tools,
                    });
                    console.log("[SUPABASE AGENT] starting ingestion of response stream");
                    const ingestedResponse = yield (0, messageparsing_1.ingestSupabaseClientResponseStream)(responseStream);
                    if (ingestedResponse === null || ingestedResponse === void 0 ? void 0 : ingestedResponse.messageText) {
                        messages.push({
                            role: "assistant",
                            content: [{ type: "text", text: ingestedResponse.messageText }],
                        });
                    }
                    if (ingestedResponse.status === "MaxTokensUsed") {
                        console.log("Claude stopped in the middle of generation. Waiting a few seconds and asking to continue");
                        yield new Promise((resolve) => {
                            setTimeout(() => {
                                resolve({});
                            }, 10000);
                        });
                        messages.push({
                            role: "user",
                            content: [
                                {
                                    type: "text",
                                    text: "you stopped in the middle of generation. Please continue.",
                                },
                            ],
                        });
                        continue;
                    }
                    toolRun = !!(ingestedResponse === null || ingestedResponse === void 0 ? void 0 : ingestedResponse.toolInput);
                    if (toolRun) {
                        let inputVars = undefined;
                        if (ingestedResponse.toolInput.partialInput) {
                            inputVars = JSON.parse(ingestedResponse.toolInput.partialInput);
                            console.log("[SUPABASE AGENT] Starting tool run ", ingestedResponse.toolInput.tool, inputVars);
                        }
                        else {
                            inputVars = {};
                            console.log("[SUPABASE AGENT] Starting tool run with no arguments for: ", ingestedResponse.toolInput.tool);
                        }
                        messages.push({
                            role: "assistant",
                            content: [
                                {
                                    id: ingestedResponse.toolInput.id,
                                    input: inputVars,
                                    name: ingestedResponse.toolInput.tool,
                                    type: "tool_use",
                                },
                            ],
                        });
                        debugger;
                        let result;
                        if (toolsMCPMapping[ingestedResponse.toolInput.tool]) {
                            result = yield toolsMCPMapping[ingestedResponse.toolInput.tool].callTool({
                                name: ingestedResponse.toolInput.tool,
                                arguments: inputVars,
                            });
                        }
                        else {
                            console.error(chalk_1.default.red("Could not run tool!"));
                            result = { content: ["Tool unavailable! Try again later!"] };
                        }
                        debugger;
                        let typedResult;
                        if (typeof result.content === "string") {
                            typedResult = result.content;
                        }
                        else if (Array.isArray(result.content)) {
                            typedResult = result.content;
                        }
                        else {
                            console.log(chalk_1.default.red("tool returned unexpected response: "), result);
                            typedResult = "tool failed!";
                        }
                        // if (stringifiedResult.length > 40000) {
                        //   stringifiedResult = stringifiedResult.slice(0, 40000) + "--------rest of the response was truncated due to token limit--------";
                        // }
                        messages.push({
                            role: "user",
                            content: [
                                {
                                    type: "tool_result",
                                    tool_use_id: ingestedResponse.toolInput.id,
                                    content: typedResult,
                                },
                            ],
                        });
                    }
                } while (toolRun);
            }
            catch (err) {
                console.error("[SUPABASE AGENT] Failed when communicating with anthropic", err);
            }
        }
        return result;
    });
}
