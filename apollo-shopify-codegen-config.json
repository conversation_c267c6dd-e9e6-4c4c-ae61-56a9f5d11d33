{"schemaNamespace": "shopify", "input": {"operationSearchPaths": ["**/shopifyqueries/*.graphql"], "schemaSearchPaths": ["**/shopifyqueries/*.graphqls"]}, "schemaDownloadConfiguration": {"downloadMethod": {"introspection": {"endpointURL": "https://<shopname>.myshopify.com/api/2024-10/graphql.json", "httpMethod": {"POST": {}}, "includeDeprecatedInputValues": false, "outputFormat": "SDL"}}, "downloadTimeout": 60, "headers": {"X-Shopify-Storefront-Access-Token": "<token>"}, "outputPath": "./shopifyschema.graphqls"}, "output": {"testMocks": {"none": {}}, "schemaTypes": {"path": "./shopify", "moduleType": {"swiftPackageManager": {}}}, "operations": {"inSchemaModule": {}}}}