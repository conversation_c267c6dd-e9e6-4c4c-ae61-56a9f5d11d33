'use strict';
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('chat', {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        allowNull: false,
        primaryKey: true
      },
      outputFile: {
        type: Sequelize.STRING,
        allowNull: true
      },
      type: {
        type: Sequelize.STRING,
        allowNull: true
      },
      llm_provider: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      llm_model: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      appId: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE
      },
      deletedAt: {
        allowNull: true,
        type: Sequelize.DATE
      }
    });
    await queryInterface.sequelize.query("UPDATE chat SET llm_provider = 'openai', llm_model = 'gpt-4o'");
  },
  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('chat');
  }
};
