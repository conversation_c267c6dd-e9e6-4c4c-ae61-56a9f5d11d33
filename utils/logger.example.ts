import { Logger, LogLevel, createDefaultLogger } from './index';
import path from 'path';

/**
 * Example 1: Using the default logger
 * This creates a log file in the utils directory
 */
function exampleDefaultLogger() {
  // Create a default logger that writes to 'app-logs.txt' in the utils directory
  const logger = createDefaultLogger('app-logs.txt');
  
  // Log different types of messages
  logger.info('Application started');
  logger.debug('Debug information', { config: { port: 3000, env: 'development' } });
  logger.warn('Warning: resource usage high');
  logger.error('Failed to connect to database', new Error('Connection timeout'));
}

/**
 * Example 2: Creating a custom logger
 * This demonstrates how to create a logger with custom configuration
 */
function exampleCustomLogger() {
  // Create a custom logger that writes to a specific path
  const logPath = path.join(__dirname, '..', 'logs', 'custom-logs.txt');
  const logger = new Logger({
    filePath: logPath,
    logToConsole: true,
    minLevel: LogLevel.WARN // Only log warnings and errors
  });
  
  // These will be logged (WARN level or higher)
  logger.warn('This is a warning');
  logger.error('This is an error');
  
  // These will be skipped (below WARN level)
  logger.info('This info message will not be logged');
  logger.debug('This debug message will not be logged');
}

/**
 * Example 3: Creating a module-specific logger
 * This demonstrates how to create loggers for different modules
 */
function exampleModuleLogger(moduleName: string) {
  // Create a logger for a specific module
  const logPath = path.join(__dirname, '..', 'logs', `${moduleName}.log`);
  const logger = new Logger({
    filePath: logPath,
    logToConsole: false // Don't log to console, only to file
  });
  
  return logger;
}

// Usage example for module-specific logger
function exampleModuleUsage() {
  const authLogger = exampleModuleLogger('auth');
  const apiLogger = exampleModuleLogger('api');
  
  authLogger.info('User logged in', { userId: '123', timestamp: new Date() });
  apiLogger.error('API request failed', { endpoint: '/users', statusCode: 500 });
}

/**
 * How to use the logger in your application:
 * 
 * 1. Import the logger from utils:
 *    import { createDefaultLogger } from '../utils';
 * 
 * 2. Create a logger instance:
 *    const logger = createDefaultLogger('my-service-logs.txt');
 *    
 * 3. Use the logger throughout your code:
 *    logger.info('Operation completed', { result });
 *    logger.error('Operation failed', error);
 */

// Note: This file is for demonstration purposes only.
// You don't need to run these examples directly.
