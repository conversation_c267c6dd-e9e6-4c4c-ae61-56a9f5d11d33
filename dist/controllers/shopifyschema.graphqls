schema {
  query: QueryRoot
  mutation: Mutation
}

"""Marks an element of a GraphQL schema as having restricted access."""
directive @accessRestricted(
  """Explains the reason around this restriction"""
  reason: String = null
) on FIELD_DEFINITION | OBJECT

"""
Contextualizes data based on the additional information provided by the directive. For example, you can use the `@inContext(country: CA)` directive to [query a product's price](https://shopify.dev/custom-storefronts/internationalization/international-pricing) in a storefront within the context of Canada.
"""
directive @inContext(
  """The country code for context. For example, `CA`."""
  country: CountryCode

  """The language code for context. For example, `EN`."""
  language: LanguageCode

  """The identifier of the customer's preferred location."""
  preferredLocationId: ID

  """The buyer's identity."""
  buyer: BuyerInput
) on QUERY | MUTATION

"""
Informs the server to delay the execution of the current fragment, potentially resulting in multiple responses from the server. Non-deferred data is delivered in the initial response and data deferred is delivered in subsequent responses. Only available on development stores with the Defer Directive developer preview enabled.
"""
directive @defer(
  "When `true`, fragment should be deferred. When `false`, fragment will not be\ndeferred and data will be included in the initial response. Defaults to `true`\nwhen omitted.\n"
  if: Boolean = true

  "May be used to identify the data from responses and associate it with the\ncorresponding defer directive. `label` must be unique label across all `@defer` and\n`@stream` directives in a document. `label` must not be provided as a variable.\n"
  label: String
) on FRAGMENT_SPREAD | INLINE_FRAGMENT

"""
A directive used by the Apollo iOS client to annotate operations or fragments that should be used exclusively for generating local cache mutations instead of as standard operations.
"""
directive @apollo_client_ios_localCacheMutation on QUERY | MUTATION | SUBSCRIPTION | FRAGMENT_DEFINITION

"""
A directive used by the Apollo iOS code generation engine to generate custom import statements in operation or fragment definition files. An import statement to import a module with the name provided in the `module` argument will be added to the generated definition file.
"""
directive @import(
  """The name of the module to import."""
  module: String!
) repeatable on QUERY | MUTATION | SUBSCRIPTION | FRAGMENT_DEFINITION

"A version of the API, as defined by [Shopify API versioning](https://shopify.dev/api/usage/versioning).\nVersions are commonly referred to by their handle (for example, `2021-10`).\n"
type ApiVersion {
  """The human-readable name of the version."""
  displayName: String!

  """
  The unique identifier of an ApiVersion. All supported API versions have a date-based (YYYY-MM) or `unstable` handle.
  """
  handle: String!

  """
  Whether the version is actively supported by Shopify. Supported API versions are guaranteed to be stable. Unsupported API versions include unstable, release candidate, and end-of-life versions that are marked as unsupported. For more information, refer to [Versioning](https://shopify.dev/api/usage/versioning).
  """
  supported: Boolean!
}

"The input fields for submitting Apple Pay payment method information for checkout.\n"
input ApplePayWalletContentInput {
  """The customer's billing address."""
  billingAddress: MailingAddressInput!

  """The data for the Apple Pay wallet."""
  data: String!

  """The header data for the Apple Pay wallet."""
  header: ApplePayWalletHeaderInput!

  """The last digits of the card used to create the payment."""
  lastDigits: String

  """The signature for the Apple Pay wallet."""
  signature: String!

  """The version for the Apple Pay wallet."""
  version: String!
}

"The input fields for submitting wallet payment method information for checkout.\n"
input ApplePayWalletHeaderInput {
  """The application data for the Apple Pay wallet."""
  applicationData: String

  """The ephemeral public key for the Apple Pay wallet."""
  ephemeralPublicKey: String!

  """The public key hash for the Apple Pay wallet."""
  publicKeyHash: String!

  """The transaction ID for the Apple Pay wallet."""
  transactionId: String!
}

"""Details about the gift card used on the checkout."""
type AppliedGiftCard implements Node {
  """The amount that was taken from the gift card by applying it."""
  amountUsed: MoneyV2!

  """The amount that was taken from the gift card by applying it."""
  amountUsedV2: MoneyV2! @deprecated(reason: "Use `amountUsed` instead.")

  """The amount left on the gift card."""
  balance: MoneyV2!

  """The amount left on the gift card."""
  balanceV2: MoneyV2! @deprecated(reason: "Use `balance` instead.")

  """A globally-unique ID."""
  id: ID!

  """The last characters of the gift card."""
  lastCharacters: String!

  """The amount that was applied to the checkout in its currency."""
  presentmentAmountUsed: MoneyV2!
}

"""An article in an online store blog."""
type Article implements HasMetafields & Node & OnlineStorePublishable & Trackable {
  """The article's author."""
  author: ArticleAuthor! @deprecated(reason: "Use `authorV2` instead.")

  """The article's author."""
  authorV2: ArticleAuthor

  """The blog that the article belongs to."""
  blog: Blog!

  """List of comments posted on the article."""
  comments(
    """Returns up to the first `n` elements from the list."""
    first: Int

    """Returns the elements that come after the specified cursor."""
    after: String

    """Returns up to the last `n` elements from the list."""
    last: Int

    """Returns the elements that come before the specified cursor."""
    before: String

    """Reverse the order of the underlying list."""
    reverse: Boolean = false
  ): CommentConnection!

  """Stripped content of the article, single line with HTML tags removed."""
  content(
    """Truncates a string after the given length."""
    truncateAt: Int
  ): String!

  """The content of the article, complete with HTML formatting."""
  contentHtml: HTML!

  """Stripped excerpt of the article, single line with HTML tags removed."""
  excerpt(
    """Truncates a string after the given length."""
    truncateAt: Int
  ): String

  """The excerpt of the article, complete with HTML formatting."""
  excerptHtml: HTML

  """
  A human-friendly unique string for the Article automatically generated from its title.
  """
  handle: String!

  """A globally-unique ID."""
  id: ID!

  """The image associated with the article."""
  image: Image

  """
  A [custom field](https://shopify.dev/docs/apps/build/custom-data), including its `namespace` and `key`, that's associated with a Shopify resource for the purposes of adding and storing additional information.
  """
  metafield(
    """
    The container the metafield belongs to. If omitted, the app-reserved namespace will be used.
    """
    namespace: String

    """The identifier for the metafield."""
    key: String!
  ): Metafield

  """
  A list of [custom fields](/docs/apps/build/custom-data) that a merchant associates with a Shopify resource.
  """
  metafields(
    """
    The list of metafields to retrieve by namespace and key.
    
    The input must not contain more than `250` values.
    """
    identifiers: [HasMetafieldsIdentifier!]!
  ): [Metafield]!

  """
  The URL used for viewing the resource on the shop's Online Store. Returns `null` if the resource is currently not published to the Online Store sales channel.
  """
  onlineStoreUrl: URL

  """The date and time when the article was published."""
  publishedAt: DateTime!

  """The article’s SEO information."""
  seo: SEO

  "A categorization that a article can be tagged with.\n"
  tags: [String!]!

  """The article’s name."""
  title: String!

  """
  URL parameters to be added to a page URL to track the origin of on-site search traffic for [analytics reporting](https://help.shopify.com/manual/reports-and-analytics/shopify-reports/report-types/default-reports/behaviour-reports). Returns a result when accessed through the [search](https://shopify.dev/docs/api/storefront/current/queries/search) or [predictiveSearch](https://shopify.dev/docs/api/storefront/current/queries/predictiveSearch) queries, otherwise returns null.
  """
  trackingParameters: String
}

"""The author of an article."""
type ArticleAuthor {
  """The author's bio."""
  bio: String

  """The author’s email."""
  email: String!

  """The author's first name."""
  firstName: String!

  """The author's last name."""
  lastName: String!

  """The author's full name."""
  name: String!
}

"An auto-generated type for paginating through multiple Articles.\n"
type ArticleConnection {
  """A list of edges."""
  edges: [ArticleEdge!]!

  """A list of the nodes contained in ArticleEdge."""
  nodes: [Article!]!

  """Information to aid in pagination."""
  pageInfo: PageInfo!
}

"An auto-generated type which holds one Article and a cursor during pagination.\n"
type ArticleEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of ArticleEdge."""
  node: Article!
}

"""The set of valid sort keys for the Article query."""
enum ArticleSortKeys {
  """Sort by the `title` value."""
  TITLE

  """Sort by the `blog_title` value."""
  BLOG_TITLE

  """Sort by the `author` value."""
  AUTHOR

  """Sort by the `updated_at` value."""
  UPDATED_AT

  """Sort by the `published_at` value."""
  PUBLISHED_AT

  """Sort by the `id` value."""
  ID

  "Sort by relevance to the search terms when the `query` parameter is specified on the connection.\nDon't use this sort key when no search query is specified.\n"
  RELEVANCE
}

"""
Represents a generic custom attribute, such as whether an order is a customer's first.
"""
type Attribute {
  "The key or name of the attribute. For example, `\"customersFirstOrder\"`.\n"
  key: String!

  "The value of the attribute. For example, `\"true\"`.\n"
  value: String
}

"""The input fields for an attribute."""
input AttributeInput {
  """Key or name of the attribute."""
  key: String!

  """Value of the attribute."""
  value: String!
}

"Automatic discount applications capture the intentions of a discount that was automatically applied.\n"
type AutomaticDiscountApplication implements DiscountApplication {
  """
  The method by which the discount's value is allocated to its entitled items.
  """
  allocationMethod: DiscountApplicationAllocationMethod!

  """Which lines of targetType that the discount is allocated over."""
  targetSelection: DiscountApplicationTargetSelection!

  """The type of line that the discount is applicable towards."""
  targetType: DiscountApplicationTargetType!

  """The title of the application."""
  title: String!

  """The value of the discount application."""
  value: PricingValue!
}

"""Represents a cart line common fields."""
interface BaseCartLine implements Node {
  """An attribute associated with the cart line."""
  attribute(
    """The key of the attribute."""
    key: String!
  ): Attribute

  """
  The attributes associated with the cart line. Attributes are represented as key-value pairs.
  """
  attributes: [Attribute!]!

  """
  The cost of the merchandise that the buyer will pay for at checkout. The costs are subject to change and changes will be reflected at checkout.
  """
  cost: CartLineCost!

  """The discounts that have been applied to the cart line."""
  discountAllocations: [CartDiscountAllocation!]!

  """
  The estimated cost of the merchandise that the buyer will pay for at checkout. The estimated costs are subject to change and changes will be reflected at checkout.
  """
  estimatedCost: CartLineEstimatedCost! @deprecated(reason: "Use `cost` instead.")

  """A globally-unique ID."""
  id: ID!

  """The merchandise that the buyer intends to purchase."""
  merchandise: Merchandise!

  """The quantity of the merchandise that the customer intends to purchase."""
  quantity: Int!

  """
  The selling plan associated with the cart line and the effect that each selling plan has on variants when they're purchased.
  """
  sellingPlanAllocation: SellingPlanAllocation
}

"An auto-generated type for paginating through multiple BaseCartLines.\n"
type BaseCartLineConnection {
  """A list of edges."""
  edges: [BaseCartLineEdge!]!

  """A list of the nodes contained in BaseCartLineEdge."""
  nodes: [BaseCartLine!]!

  """Information to aid in pagination."""
  pageInfo: PageInfo!
}

"An auto-generated type which holds one BaseCartLine and a cursor during pagination.\n"
type BaseCartLineEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of BaseCartLineEdge."""
  node: BaseCartLine!
}

"""An online store blog."""
type Blog implements HasMetafields & Node & OnlineStorePublishable {
  """Find an article by its handle."""
  articleByHandle(
    """The handle of the article."""
    handle: String!
  ): Article

  """List of the blog's articles."""
  articles(
    """Returns up to the first `n` elements from the list."""
    first: Int

    """Returns the elements that come after the specified cursor."""
    after: String

    """Returns up to the last `n` elements from the list."""
    last: Int

    """Returns the elements that come before the specified cursor."""
    before: String

    """Reverse the order of the underlying list."""
    reverse: Boolean = false

    """Sort the underlying list by the given key."""
    sortKey: ArticleSortKeys = ID

    "Apply one or multiple filters to the query.\n| name | description | acceptable_values | default_value | example_use |\n| ---- | ---- | ---- | ---- | ---- |\n| author |\n| blog_title |\n| created_at |\n| tag |\n| tag_not |\n| updated_at |\nRefer to the detailed [search syntax](https://shopify.dev/api/usage/search-syntax) for more information about using filters.\n"
    query: String
  ): ArticleConnection!

  """The authors who have contributed to the blog."""
  authors: [ArticleAuthor!]!

  "A human-friendly unique string for the Blog automatically generated from its title.\n"
  handle: String!

  """A globally-unique ID."""
  id: ID!

  """
  A [custom field](https://shopify.dev/docs/apps/build/custom-data), including its `namespace` and `key`, that's associated with a Shopify resource for the purposes of adding and storing additional information.
  """
  metafield(
    """
    The container the metafield belongs to. If omitted, the app-reserved namespace will be used.
    """
    namespace: String

    """The identifier for the metafield."""
    key: String!
  ): Metafield

  """
  A list of [custom fields](/docs/apps/build/custom-data) that a merchant associates with a Shopify resource.
  """
  metafields(
    """
    The list of metafields to retrieve by namespace and key.
    
    The input must not contain more than `250` values.
    """
    identifiers: [HasMetafieldsIdentifier!]!
  ): [Metafield]!

  """
  The URL used for viewing the resource on the shop's Online Store. Returns `null` if the resource is currently not published to the Online Store sales channel.
  """
  onlineStoreUrl: URL

  """The blog's SEO information."""
  seo: SEO

  """The blogs’s title."""
  title: String!
}

"An auto-generated type for paginating through multiple Blogs.\n"
type BlogConnection {
  """A list of edges."""
  edges: [BlogEdge!]!

  """A list of the nodes contained in BlogEdge."""
  nodes: [Blog!]!

  """Information to aid in pagination."""
  pageInfo: PageInfo!
}

"An auto-generated type which holds one Blog and a cursor during pagination.\n"
type BlogEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of BlogEdge."""
  node: Blog!
}

"""The set of valid sort keys for the Blog query."""
enum BlogSortKeys {
  """Sort by the `handle` value."""
  HANDLE

  """Sort by the `title` value."""
  TITLE

  """Sort by the `id` value."""
  ID

  "Sort by relevance to the search terms when the `query` parameter is specified on the connection.\nDon't use this sort key when no search query is specified.\n"
  RELEVANCE
}

"The store's [branding configuration](https://help.shopify.com/en/manual/promoting-marketing/managing-brand-assets).\n"
type Brand {
  """The colors of the store's brand."""
  colors: BrandColors!

  """The store's cover image."""
  coverImage: MediaImage

  """The store's default logo."""
  logo: MediaImage

  """The store's short description."""
  shortDescription: String

  """The store's slogan."""
  slogan: String

  """The store's preferred logo for square UI elements."""
  squareLogo: MediaImage
}

"A group of related colors for the shop's brand.\n"
type BrandColorGroup {
  """The background color."""
  background: Color

  """The foreground color."""
  foreground: Color
}

"The colors of the shop's brand.\n"
type BrandColors {
  """The shop's primary brand colors."""
  primary: [BrandColorGroup!]!

  """The shop's secondary brand colors."""
  secondary: [BrandColorGroup!]!
}

"The input fields for obtaining the buyer's identity.\n"
input BuyerInput {
  """
  The customer access token retrieved from the [Customer Accounts API](https://shopify.dev/docs/api/customer#step-obtain-access-token).
  """
  customerAccessToken: String!

  """The identifier of the company location."""
  companyLocationId: ID
}

"""
Card brand, such as Visa or Mastercard, which can be used for payments.
"""
enum CardBrand {
  """Visa."""
  VISA

  """Mastercard."""
  MASTERCARD

  """Discover."""
  DISCOVER

  """American Express."""
  AMERICAN_EXPRESS

  """Diners Club."""
  DINERS_CLUB

  """JCB."""
  JCB
}

"A cart represents the merchandise that a buyer intends to purchase,\nand the estimated cost associated with the cart. Learn how to\n[interact with a cart](https://shopify.dev/custom-storefronts/internationalization/international-pricing)\nduring a customer's session.\n"
type Cart implements HasMetafields & Node {
  """The gift cards that have been applied to the cart."""
  appliedGiftCards: [AppliedGiftCard!]!

  """An attribute associated with the cart."""
  attribute(
    """The key of the attribute."""
    key: String!
  ): Attribute

  """
  The attributes associated with the cart. Attributes are represented as key-value pairs.
  """
  attributes: [Attribute!]!

  """Information about the buyer that's interacting with the cart."""
  buyerIdentity: CartBuyerIdentity!

  """The URL of the checkout for the cart."""
  checkoutUrl: URL!

  """
  The estimated costs that the buyer will pay at checkout. The costs are subject to change and changes will be reflected at checkout. The `cost` field uses the `buyerIdentity` field to determine [international pricing](https://shopify.dev/custom-storefronts/internationalization/international-pricing).
  """
  cost: CartCost!

  """The date and time when the cart was created."""
  createdAt: DateTime!

  "The delivery groups available for the cart, based on the buyer identity default\ndelivery address preference or the default address of the logged-in customer.\n"
  deliveryGroups(
    """Returns up to the first `n` elements from the list."""
    first: Int

    """Returns the elements that come after the specified cursor."""
    after: String

    """Returns up to the last `n` elements from the list."""
    last: Int

    """Returns the elements that come before the specified cursor."""
    before: String

    """Reverse the order of the underlying list."""
    reverse: Boolean = false

    "Whether to include [carrier-calculated delivery rates](https://help.shopify.com/en/manual/shipping/setting-up-and-managing-your-shipping/enabling-shipping-carriers) in the response.\n\nBy default, only static shipping rates are returned. This argument requires mandatory usage of the [`@defer` directive](https://shopify.dev/docs/api/storefront#directives).\n\nFor more information, refer to [fetching carrier-calculated rates for the cart using `@defer`](https://shopify.dev/docs/storefronts/headless/building-with-the-storefront-api/defer#fetching-carrier-calculated-rates-for-the-cart-using-defer).\n"
    withCarrierRates: Boolean = false
  ): CartDeliveryGroupConnection!

  """The discounts that have been applied to the entire cart."""
  discountAllocations: [CartDiscountAllocation!]!

  """
  The case-insensitive discount codes that the customer added at checkout.
  """
  discountCodes: [CartDiscountCode!]!

  """
  The estimated costs that the buyer will pay at checkout. The estimated costs are subject to change and changes will be reflected at checkout. The `estimatedCost` field uses the `buyerIdentity` field to determine [international pricing](https://shopify.dev/custom-storefronts/internationalization/international-pricing).
  """
  estimatedCost: CartEstimatedCost! @deprecated(reason: "Use `cost` instead.")

  """A globally-unique ID."""
  id: ID!

  """
  A list of lines containing information about the items the customer intends to purchase.
  """
  lines(
    """Returns up to the first `n` elements from the list."""
    first: Int

    """Returns the elements that come after the specified cursor."""
    after: String

    """Returns up to the last `n` elements from the list."""
    last: Int

    """Returns the elements that come before the specified cursor."""
    before: String

    """Reverse the order of the underlying list."""
    reverse: Boolean = false
  ): BaseCartLineConnection!

  """
  A [custom field](https://shopify.dev/docs/apps/build/custom-data), including its `namespace` and `key`, that's associated with a Shopify resource for the purposes of adding and storing additional information.
  """
  metafield(
    """
    The container the metafield belongs to. If omitted, the app-reserved namespace will be used.
    """
    namespace: String

    """The identifier for the metafield."""
    key: String!
  ): Metafield

  """
  A list of [custom fields](/docs/apps/build/custom-data) that a merchant associates with a Shopify resource.
  """
  metafields(
    """
    The list of metafields to retrieve by namespace and key.
    
    The input must not contain more than `250` values.
    """
    identifiers: [HasMetafieldsIdentifier!]!
  ): [Metafield]!

  """
  A note that's associated with the cart. For example, the note can be a personalized message to the buyer.
  """
  note: String

  """The total number of items in the cart."""
  totalQuantity: Int!

  """The date and time when the cart was updated."""
  updatedAt: DateTime!
}

"""Return type for `cartAttributesUpdate` mutation."""
type CartAttributesUpdatePayload {
  """The updated cart."""
  cart: Cart

  """The list of errors that occurred from executing the mutation."""
  userErrors: [CartUserError!]!

  """A list of warnings that occurred during the mutation."""
  warnings: [CartWarning!]!
}

"""
The discounts automatically applied to the cart line based on prerequisites that have been met.
"""
type CartAutomaticDiscountAllocation implements CartDiscountAllocation {
  """The discounted amount that has been applied to the cart line."""
  discountedAmount: MoneyV2!

  """The type of line that the discount is applicable towards."""
  targetType: DiscountApplicationTargetType!

  """The title of the allocated discount."""
  title: String!
}

"""Return type for `cartBillingAddressUpdate` mutation."""
type CartBillingAddressUpdatePayload {
  """The updated cart."""
  cart: Cart

  """The list of errors that occurred from executing the mutation."""
  userErrors: [CartUserError!]!

  """A list of warnings that occurred during the mutation."""
  warnings: [CartWarning!]!
}

"""
Represents information about the buyer that is interacting with the cart.
"""
type CartBuyerIdentity {
  """The country where the buyer is located."""
  countryCode: CountryCode

  """The customer account associated with the cart."""
  customer: Customer

  "An ordered set of delivery addresses tied to the buyer that is interacting with the cart.\nThe rank of the preferences is determined by the order of the addresses in the array. Preferences\ncan be used to populate relevant fields in the checkout flow.\n\nAs of the `2025-01` release, `buyerIdentity.deliveryAddressPreferences` is deprecated.\nDelivery addresses are now part of the `CartDelivery` object and managed with three new mutations:\n- `cartDeliveryAddressAdd`\n- `cartDeliveryAddressUpdate`\n- `cartDeliveryAddressDelete`\n"
  deliveryAddressPreferences: [DeliveryAddress!]! @deprecated(reason: "Use `cart.delivery` instead.")

  """The email address of the buyer that's interacting with the cart."""
  email: String

  """The phone number of the buyer that's interacting with the cart."""
  phone: String

  "A set of preferences tied to the buyer interacting with the cart. Preferences are used to prefill fields in at checkout to streamline information collection.\nPreferences are not synced back to the cart if they are overwritten.\n"
  preferences: CartPreferences

  """The purchasing company associated with the cart."""
  purchasingCompany: PurchasingCompany
}

"Specifies the input fields to update the buyer information associated with a cart.\nBuyer identity is used to determine\n[international pricing](https://shopify.dev/custom-storefronts/internationalization/international-pricing)\nand should match the customer's shipping address.\n"
input CartBuyerIdentityInput {
  """The email address of the buyer that is interacting with the cart."""
  email: String

  """The phone number of the buyer that is interacting with the cart."""
  phone: String

  """The company location of the buyer that is interacting with the cart."""
  companyLocationId: ID

  """The country where the buyer is located."""
  countryCode: CountryCode

  """
  The access token used to identify the customer associated with the cart.
  """
  customerAccessToken: String

  "A set of preferences tied to the buyer interacting with the cart. Preferences are used to prefill fields in at checkout to streamline information collection.\nPreferences are not synced back to the cart if they are overwritten.\n"
  preferences: CartPreferencesInput
}

"""Return type for `cartBuyerIdentityUpdate` mutation."""
type CartBuyerIdentityUpdatePayload {
  """The updated cart."""
  cart: Cart

  """The list of errors that occurred from executing the mutation."""
  userErrors: [CartUserError!]!

  """A list of warnings that occurred during the mutation."""
  warnings: [CartWarning!]!
}

"Represents how credit card details are provided for a direct payment.\n"
enum CartCardSource {
  "The credit card was provided by a third party and vaulted on their system.\nUsing this value requires a separate permission from Shopify.\n"
  SAVED_CREDIT_CARD
}

"""
The discount that has been applied to the cart line using a discount code.
"""
type CartCodeDiscountAllocation implements CartDiscountAllocation {
  """The code used to apply the discount."""
  code: String!

  """The discounted amount that has been applied to the cart line."""
  discountedAmount: MoneyV2!

  """The type of line that the discount is applicable towards."""
  targetType: DiscountApplicationTargetType!
}

"""The completion action to checkout a cart."""
union CartCompletionAction = CompletePaymentChallenge

"""The required completion action to checkout a cart."""
type CartCompletionActionRequired {
  """The action required to complete the cart completion attempt."""
  action: CartCompletionAction

  """The ID of the cart completion attempt."""
  id: String!
}

"""The result of a cart completion attempt."""
union CartCompletionAttemptResult = CartCompletionActionRequired | CartCompletionFailed | CartCompletionProcessing | CartCompletionSuccess

"""A failed completion to checkout a cart."""
type CartCompletionFailed {
  """The errors that caused the checkout to fail."""
  errors: [CompletionError!]!

  """The ID of the cart completion attempt."""
  id: String!
}

"""A cart checkout completion that's still processing."""
type CartCompletionProcessing {
  """The ID of the cart completion attempt."""
  id: String!

  """The number of milliseconds to wait before polling again."""
  pollDelay: Int!
}

"""A successful completion to checkout a cart and a created order."""
type CartCompletionSuccess {
  """The date and time when the job completed."""
  completedAt: DateTime

  """The ID of the cart completion attempt."""
  id: String!

  """The ID of the order that's created in Shopify."""
  orderId: ID!

  """The URL of the order confirmation in Shopify."""
  orderUrl: URL!
}

"The costs that the buyer will pay at checkout.\nThe cart cost uses [`CartBuyerIdentity`](https://shopify.dev/api/storefront/reference/cart/cartbuyeridentity) to determine\n[international pricing](https://shopify.dev/custom-storefronts/internationalization/international-pricing).\n"
type CartCost {
  """
  The estimated amount, before taxes and discounts, for the customer to pay at checkout. The checkout charge amount doesn't include any deferred payments that'll be paid at a later date. If the cart has no deferred payments, then the checkout charge amount is equivalent to `subtotalAmount`.
  """
  checkoutChargeAmount: MoneyV2!

  """
  The amount, before taxes and cart-level discounts, for the customer to pay.
  """
  subtotalAmount: MoneyV2!

  """Whether the subtotal amount is estimated."""
  subtotalAmountEstimated: Boolean!

  """The total amount for the customer to pay."""
  totalAmount: MoneyV2!

  """Whether the total amount is estimated."""
  totalAmountEstimated: Boolean!

  """The duty amount for the customer to pay at checkout."""
  totalDutyAmount: MoneyV2 @deprecated(reason: "Tax and duty amounts are no longer available and will be removed in a future version.\nPlease see [the changelog](https://shopify.dev/changelog/tax-and-duties-are-deprecated-in-storefront-cart-api)\nfor more information.\n")

  """Whether the total duty amount is estimated."""
  totalDutyAmountEstimated: Boolean! @deprecated(reason: "Tax and duty amounts are no longer available and will be removed in a future version.\nPlease see [the changelog](https://shopify.dev/changelog/tax-and-duties-are-deprecated-in-storefront-cart-api)\nfor more information.\n")

  """The tax amount for the customer to pay at checkout."""
  totalTaxAmount: MoneyV2 @deprecated(reason: "Tax and duty amounts are no longer available and will be removed in a future version.\nPlease see [the changelog](https://shopify.dev/changelog/tax-and-duties-are-deprecated-in-storefront-cart-api)\nfor more information.\n")

  """Whether the total tax amount is estimated."""
  totalTaxAmountEstimated: Boolean! @deprecated(reason: "Tax and duty amounts are no longer available and will be removed in a future version.\nPlease see [the changelog](https://shopify.dev/changelog/tax-and-duties-are-deprecated-in-storefront-cart-api)\nfor more information.\n")
}

"""Return type for `cartCreate` mutation."""
type CartCreatePayload {
  """The new cart."""
  cart: Cart

  """The list of errors that occurred from executing the mutation."""
  userErrors: [CartUserError!]!

  """A list of warnings that occurred during the mutation."""
  warnings: [CartWarning!]!
}

"""
The discounts automatically applied to the cart line based on prerequisites that have been met.
"""
type CartCustomDiscountAllocation implements CartDiscountAllocation {
  """The discounted amount that has been applied to the cart line."""
  discountedAmount: MoneyV2!

  """The type of line that the discount is applicable towards."""
  targetType: DiscountApplicationTargetType!

  """The title of the allocated discount."""
  title: String!
}

"""
Preferred location used to find the closest pick up point based on coordinates.
"""
type CartDeliveryCoordinatesPreference {
  "The two-letter code for the country of the preferred location.\n\nFor example, US.\n"
  countryCode: CountryCode!

  """
  The geographic latitude for a given location. Coordinates are required in order to set pickUpHandle for pickup points.
  """
  latitude: Float!

  """
  The geographic longitude for a given location. Coordinates are required in order to set pickUpHandle for pickup points.
  """
  longitude: Float!
}

"""
Preferred location used to find the closest pick up point based on coordinates.
"""
input CartDeliveryCoordinatesPreferenceInput {
  """
  The geographic latitude for a given location. Coordinates are required in order to set pickUpHandle for pickup points.
  """
  latitude: Float!

  """
  The geographic longitude for a given location. Coordinates are required in order to set pickUpHandle for pickup points.
  """
  longitude: Float!

  "The two-letter code for the country of the preferred location.\n\nFor example, US.\n"
  countryCode: CountryCode!
}

"""
Information about the options available for one or more line items to be delivered to a specific address.
"""
type CartDeliveryGroup {
  """A list of cart lines for the delivery group."""
  cartLines(
    """Returns up to the first `n` elements from the list."""
    first: Int

    """Returns the elements that come after the specified cursor."""
    after: String

    """Returns up to the last `n` elements from the list."""
    last: Int

    """Returns the elements that come before the specified cursor."""
    before: String

    """Reverse the order of the underlying list."""
    reverse: Boolean = false
  ): BaseCartLineConnection!

  """The destination address for the delivery group."""
  deliveryAddress: MailingAddress!

  """The delivery options available for the delivery group."""
  deliveryOptions: [CartDeliveryOption!]!

  """The type of merchandise in the delivery group."""
  groupType: CartDeliveryGroupType!

  """The ID for the delivery group."""
  id: ID!

  """The selected delivery option for the delivery group."""
  selectedDeliveryOption: CartDeliveryOption
}

"An auto-generated type for paginating through multiple CartDeliveryGroups.\n"
type CartDeliveryGroupConnection {
  """A list of edges."""
  edges: [CartDeliveryGroupEdge!]!

  """A list of the nodes contained in CartDeliveryGroupEdge."""
  nodes: [CartDeliveryGroup!]!

  """Information to aid in pagination."""
  pageInfo: PageInfo!
}

"An auto-generated type which holds one CartDeliveryGroup and a cursor during pagination.\n"
type CartDeliveryGroupEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of CartDeliveryGroupEdge."""
  node: CartDeliveryGroup!
}

"Defines what type of merchandise is in the delivery group.\n"
enum CartDeliveryGroupType {
  """The delivery group only contains subscription merchandise."""
  SUBSCRIPTION

  "The delivery group only contains merchandise that is either a one time purchase or a first delivery of\nsubscription merchandise.\n"
  ONE_TIME_PURCHASE
}

"""Information about a delivery option."""
type CartDeliveryOption {
  """The code of the delivery option."""
  code: String

  """The method for the delivery option."""
  deliveryMethodType: DeliveryMethodType!

  """The description of the delivery option."""
  description: String

  """The estimated cost for the delivery option."""
  estimatedCost: MoneyV2!

  """The unique identifier of the delivery option."""
  handle: String!

  """The title of the delivery option."""
  title: String
}

"A set of preferences tied to the buyer interacting with the cart. Preferences are used to prefill fields in at checkout to streamline information collection. \nPreferences are not synced back to the cart if they are overwritten.\n"
type CartDeliveryPreference {
  """
  Preferred location used to find the closest pick up point based on coordinates.
  """
  coordinates: CartDeliveryCoordinatesPreference

  """
  The preferred delivery methods such as shipping, local pickup or through pickup points.
  """
  deliveryMethod: [PreferenceDeliveryMethodType!]!

  "The pickup handle prefills checkout fields with the location for either local pickup or pickup points delivery methods.\nIt accepts both location ID for local pickup and external IDs for pickup points.\n"
  pickupHandle: [String!]!
}

"""
Delivery preferences can be used to prefill the delivery section at checkout.
"""
input CartDeliveryPreferenceInput {
  """
  The preferred delivery methods such as shipping, local pickup or through pickup points.
  
  The input must not contain more than `250` values.
  """
  deliveryMethod: [PreferenceDeliveryMethodType!]

  """
  The pickup handle prefills checkout fields with the location for either local pickup or pickup points delivery methods.
  It accepts both location ID for local pickup and external IDs for pickup points.
  
  The input must not contain more than `250` values.
  """
  pickupHandle: [String!]

  """The coordinates of a delivery location in order of preference."""
  coordinates: CartDeliveryCoordinatesPreferenceInput
}

"The input fields for submitting direct payment method information for checkout.\n"
input CartDirectPaymentMethodInput {
  """The customer's billing address."""
  billingAddress: MailingAddressInput!

  """
  The session ID for the direct payment method used to create the payment.
  """
  sessionId: String!

  """The source of the credit card payment."""
  cardSource: CartCardSource
}

"""The discounts that have been applied to the cart line."""
interface CartDiscountAllocation {
  """The discounted amount that has been applied to the cart line."""
  discountedAmount: MoneyV2!

  """The type of line that the discount is applicable towards."""
  targetType: DiscountApplicationTargetType!
}

"""The discount codes applied to the cart."""
type CartDiscountCode {
  """
  Whether the discount code is applicable to the cart's current contents.
  """
  applicable: Boolean!

  """The code for the discount."""
  code: String!
}

"""Return type for `cartDiscountCodesUpdate` mutation."""
type CartDiscountCodesUpdatePayload {
  """The updated cart."""
  cart: Cart

  """The list of errors that occurred from executing the mutation."""
  userErrors: [CartUserError!]!

  """A list of warnings that occurred during the mutation."""
  warnings: [CartWarning!]!
}

"""Possible error codes that can be returned by `CartUserError`."""
enum CartErrorCode {
  """The input value is invalid."""
  INVALID

  """The input value should be less than the maximum value allowed."""
  LESS_THAN

  """Merchandise line was not found in cart."""
  INVALID_MERCHANDISE_LINE

  """Missing discount code."""
  MISSING_DISCOUNT_CODE

  """Missing note."""
  MISSING_NOTE

  """The note length must be below the specified maximum."""
  NOTE_TOO_LONG

  """Delivery group was not found in cart."""
  INVALID_DELIVERY_GROUP

  """Delivery option was not valid."""
  INVALID_DELIVERY_OPTION

  """The payment wasn't valid."""
  INVALID_PAYMENT

  """The payment method is not supported."""
  PAYMENT_METHOD_NOT_SUPPORTED

  """Cannot update payment on an empty cart"""
  INVALID_PAYMENT_EMPTY_CART

  """Validation failed."""
  VALIDATION_CUSTOM

  """The metafields were not valid."""
  INVALID_METAFIELDS

  """The customer access token is required when setting a company location."""
  MISSING_CUSTOMER_ACCESS_TOKEN

  """Company location not found or not allowed."""
  INVALID_COMPANY_LOCATION

  """The quantity must be a multiple of the specified increment."""
  INVALID_INCREMENT

  """The quantity must be above the specified minimum for the item."""
  MINIMUM_NOT_MET

  """The quantity must be below the specified maximum for the item."""
  MAXIMUM_EXCEEDED

  """The specified address field is required."""
  ADDRESS_FIELD_IS_REQUIRED

  """The specified address field is too long."""
  ADDRESS_FIELD_IS_TOO_LONG

  """The specified address field contains emojis."""
  ADDRESS_FIELD_CONTAINS_EMOJIS

  """The specified address field contains HTML tags."""
  ADDRESS_FIELD_CONTAINS_HTML_TAGS

  """The specified address field contains a URL."""
  ADDRESS_FIELD_CONTAINS_URL

  """The specified address field does not match the expected pattern."""
  ADDRESS_FIELD_DOES_NOT_MATCH_EXPECTED_PATTERN

  """The given zip code is invalid for the provided province."""
  INVALID_ZIP_CODE_FOR_PROVINCE

  """The given zip code is invalid for the provided country."""
  INVALID_ZIP_CODE_FOR_COUNTRY

  """The given zip code is unsupported."""
  ZIP_CODE_NOT_SUPPORTED

  """The given province cannot be found."""
  PROVINCE_NOT_FOUND

  """A general error occurred during address validation."""
  UNSPECIFIED_ADDRESS_ERROR
}

"""
The estimated costs that the buyer will pay at checkout. The estimated cost uses [`CartBuyerIdentity`](https://shopify.dev/api/storefront/reference/cart/cartbuyeridentity) to determine [international pricing](https://shopify.dev/custom-storefronts/internationalization/international-pricing).
"""
type CartEstimatedCost {
  """
  The estimated amount, before taxes and discounts, for the customer to pay at checkout. The checkout charge amount doesn't include any deferred payments that'll be paid at a later date. If the cart has no deferred payments, then the checkout charge amount is equivalent to`subtotal_amount`.
  """
  checkoutChargeAmount: MoneyV2!

  """
  The estimated amount, before taxes and discounts, for the customer to pay.
  """
  subtotalAmount: MoneyV2!

  """The estimated total amount for the customer to pay."""
  totalAmount: MoneyV2!

  """The estimated duty amount for the customer to pay at checkout."""
  totalDutyAmount: MoneyV2

  """The estimated tax amount for the customer to pay at checkout."""
  totalTaxAmount: MoneyV2
}

"The input fields for submitting a billing address without a selected payment method.\n"
input CartFreePaymentMethodInput {
  """The customer's billing address."""
  billingAddress: MailingAddressInput!
}

"""Return type for `cartGiftCardCodesUpdate` mutation."""
type CartGiftCardCodesUpdatePayload {
  """The updated cart."""
  cart: Cart

  """The list of errors that occurred from executing the mutation."""
  userErrors: [CartUserError!]!

  """A list of warnings that occurred during the mutation."""
  warnings: [CartWarning!]!
}

"""The input fields to create a cart."""
input CartInput {
  """
  An array of key-value pairs that contains additional information about the cart.
  
  The input must not contain more than `250` values.
  """
  attributes: [AttributeInput!]

  """
  A list of merchandise lines to add to the cart.
  
  The input must not contain more than `250` values.
  """
  lines: [CartLineInput!]

  """
  The case-insensitive discount codes that the customer added at checkout.
  
  The input must not contain more than `250` values.
  """
  discountCodes: [String!]

  """
  The case-insensitive gift card codes.
  
  The input must not contain more than `250` values.
  """
  giftCardCodes: [String!]

  "A note that's associated with the cart. For example, the note can be a personalized message to the buyer.\n"
  note: String

  "The customer associated with the cart. Used to determine [international pricing]\n(https://shopify.dev/custom-storefronts/internationalization/international-pricing).\nBuyer identity should match the customer's shipping address.\n"
  buyerIdentity: CartBuyerIdentityInput

  """
  The metafields to associate with this cart.
  
  The input must not contain more than `250` values.
  """
  metafields: [CartInputMetafieldInput!]
}

"""The input fields for a cart metafield value to set."""
input CartInputMetafieldInput {
  """The key name of the metafield."""
  key: String!

  "The data to store in the cart metafield. The data is always stored as a string, regardless of the metafield's type.\n"
  value: String!

  "The type of data that the cart metafield stores.\nThe type of data must be a [supported type](https://shopify.dev/apps/metafields/types).\n"
  type: String!
}

"""Represents information about the merchandise in the cart."""
type CartLine implements BaseCartLine & Node {
  """An attribute associated with the cart line."""
  attribute(
    """The key of the attribute."""
    key: String!
  ): Attribute

  """
  The attributes associated with the cart line. Attributes are represented as key-value pairs.
  """
  attributes: [Attribute!]!

  """
  The cost of the merchandise that the buyer will pay for at checkout. The costs are subject to change and changes will be reflected at checkout.
  """
  cost: CartLineCost!

  """The discounts that have been applied to the cart line."""
  discountAllocations: [CartDiscountAllocation!]!

  """
  The estimated cost of the merchandise that the buyer will pay for at checkout. The estimated costs are subject to change and changes will be reflected at checkout.
  """
  estimatedCost: CartLineEstimatedCost! @deprecated(reason: "Use `cost` instead.")

  """A globally-unique ID."""
  id: ID!

  """The merchandise that the buyer intends to purchase."""
  merchandise: Merchandise!

  """The quantity of the merchandise that the customer intends to purchase."""
  quantity: Int!

  """
  The selling plan associated with the cart line and the effect that each selling plan has on variants when they're purchased.
  """
  sellingPlanAllocation: SellingPlanAllocation
}

"""The cost of the merchandise line that the buyer will pay at checkout."""
type CartLineCost {
  """The amount of the merchandise line."""
  amountPerQuantity: MoneyV2!

  """The compare at amount of the merchandise line."""
  compareAtAmountPerQuantity: MoneyV2

  """The cost of the merchandise line before line-level discounts."""
  subtotalAmount: MoneyV2!

  """The total cost of the merchandise line."""
  totalAmount: MoneyV2!
}

"The estimated cost of the merchandise line that the buyer will pay at checkout.\n"
type CartLineEstimatedCost {
  """The amount of the merchandise line."""
  amount: MoneyV2!

  """The compare at amount of the merchandise line."""
  compareAtAmount: MoneyV2

  """The estimated cost of the merchandise line before discounts."""
  subtotalAmount: MoneyV2!

  """The estimated total cost of the merchandise line."""
  totalAmount: MoneyV2!
}

"""The input fields to create a merchandise line on a cart."""
input CartLineInput {
  """
  An array of key-value pairs that contains additional information about the merchandise line.
  
  The input must not contain more than `250` values.
  """
  attributes: [AttributeInput!]

  """The quantity of the merchandise."""
  quantity: Int = 1

  """The ID of the merchandise that the buyer intends to purchase."""
  merchandiseId: ID!

  """
  The ID of the selling plan that the merchandise is being purchased with.
  """
  sellingPlanId: ID
}

"""The input fields to update a line item on a cart."""
input CartLineUpdateInput {
  """The ID of the merchandise line."""
  id: ID!

  """The quantity of the line item."""
  quantity: Int

  """The ID of the merchandise for the line item."""
  merchandiseId: ID

  """
  An array of key-value pairs that contains additional information about the merchandise line.
  
  The input must not contain more than `250` values.
  """
  attributes: [AttributeInput!]

  """
  The ID of the selling plan that the merchandise is being purchased with.
  """
  sellingPlanId: ID
}

"""Return type for `cartLinesAdd` mutation."""
type CartLinesAddPayload {
  """The updated cart."""
  cart: Cart

  """The list of errors that occurred from executing the mutation."""
  userErrors: [CartUserError!]!

  """A list of warnings that occurred during the mutation."""
  warnings: [CartWarning!]!
}

"""Return type for `cartLinesRemove` mutation."""
type CartLinesRemovePayload {
  """The updated cart."""
  cart: Cart

  """The list of errors that occurred from executing the mutation."""
  userErrors: [CartUserError!]!

  """A list of warnings that occurred during the mutation."""
  warnings: [CartWarning!]!
}

"""Return type for `cartLinesUpdate` mutation."""
type CartLinesUpdatePayload {
  """The updated cart."""
  cart: Cart

  """The list of errors that occurred from executing the mutation."""
  userErrors: [CartUserError!]!

  """A list of warnings that occurred during the mutation."""
  warnings: [CartWarning!]!
}

"""The input fields to delete a cart metafield."""
input CartMetafieldDeleteInput {
  """The ID of the cart resource."""
  ownerId: ID!

  "The key name of the cart metafield. Can either be a composite key (`namespace.key`) or a simple key\n that relies on the default app-reserved namespace.\n"
  key: String!
}

"""Return type for `cartMetafieldDelete` mutation."""
type CartMetafieldDeletePayload {
  """The ID of the deleted cart metafield."""
  deletedId: ID

  """The list of errors that occurred from executing the mutation."""
  userErrors: [MetafieldDeleteUserError!]!
}

"""The input fields for a cart metafield value to set."""
input CartMetafieldsSetInput {
  """The ID of the cart resource."""
  ownerId: ID!

  """The key name of the cart metafield."""
  key: String!

  "The data to store in the cart metafield. The data is always stored as a string, regardless of the metafield's type.\n"
  value: String!

  "The type of data that the cart metafield stores.\nThe type of data must be a [supported type](https://shopify.dev/apps/metafields/types).\n"
  type: String!
}

"""Return type for `cartMetafieldsSet` mutation."""
type CartMetafieldsSetPayload {
  """The list of cart metafields that were set."""
  metafields: [Metafield!]

  """The list of errors that occurred from executing the mutation."""
  userErrors: [MetafieldsSetUserError!]!
}

"""Return type for `cartNoteUpdate` mutation."""
type CartNoteUpdatePayload {
  """The updated cart."""
  cart: Cart

  """The list of errors that occurred from executing the mutation."""
  userErrors: [CartUserError!]!

  """A list of warnings that occurred during the mutation."""
  warnings: [CartWarning!]!
}

"The input fields for updating the payment method that will be used to checkout.\n"
input CartPaymentInput {
  """The amount that the customer will be charged at checkout."""
  amount: MoneyInput!

  "An ID of the order placed on the originating platform.\nNote that this value doesn't correspond to the Shopify Order ID.\n"
  sourceIdentifier: String

  "The input fields to use to checkout a cart without providing a payment method.\nUse this payment method input if the total cost of the cart is 0.\n"
  freePaymentMethod: CartFreePaymentMethodInput

  "The input fields to use when checking out a cart with a direct payment method (like a credit card).\n"
  directPaymentMethod: CartDirectPaymentMethodInput

  "The input fields to use when checking out a cart with a wallet payment method (like Shop Pay or Apple Pay).\n"
  walletPaymentMethod: CartWalletPaymentMethodInput
}

"""Return type for `cartPaymentUpdate` mutation."""
type CartPaymentUpdatePayload {
  """The updated cart."""
  cart: Cart

  """The list of errors that occurred from executing the mutation."""
  userErrors: [CartUserError!]!

  """A list of warnings that occurred during the mutation."""
  warnings: [CartWarning!]!
}

"A set of preferences tied to the buyer interacting with the cart. Preferences are used to prefill fields in at checkout to streamline information collection. \nPreferences are not synced back to the cart if they are overwritten.\n"
type CartPreferences {
  """
  Delivery preferences can be used to prefill the delivery section in at checkout.
  """
  delivery: CartDeliveryPreference

  "Wallet preferences are used to populate relevant payment fields in the checkout flow.\nAccepted value: `[\"shop_pay\"]`.\n"
  wallet: [String!]
}

"""
The input fields represent preferences for the buyer that is interacting with the cart.
"""
input CartPreferencesInput {
  """
  Delivery preferences can be used to prefill the delivery section in at checkout.
  """
  delivery: CartDeliveryPreferenceInput

  """
  Wallet preferences are used to populate relevant payment fields in the checkout flow.
  Accepted value: `["shop_pay"]`.
  
  The input must not contain more than `250` values.
  """
  wallet: [String!]
}

"The input fields for updating the selected delivery options for a delivery group.\n"
input CartSelectedDeliveryOptionInput {
  """The ID of the cart delivery group."""
  deliveryGroupId: ID!

  """The handle of the selected delivery option."""
  deliveryOptionHandle: String!
}

"""Return type for `cartSelectedDeliveryOptionsUpdate` mutation."""
type CartSelectedDeliveryOptionsUpdatePayload {
  """The updated cart."""
  cart: Cart

  """The list of errors that occurred from executing the mutation."""
  userErrors: [CartUserError!]!

  """A list of warnings that occurred during the mutation."""
  warnings: [CartWarning!]!
}

"""Return type for `cartSubmitForCompletion` mutation."""
type CartSubmitForCompletionPayload {
  """The result of cart submission for completion."""
  result: CartSubmitForCompletionResult

  """The list of errors that occurred from executing the mutation."""
  userErrors: [CartUserError!]!
}

"""The result of cart submit completion."""
union CartSubmitForCompletionResult = SubmitAlreadyAccepted | SubmitFailed | SubmitSuccess | SubmitThrottled

"""Represents an error that happens during execution of a cart mutation."""
type CartUserError implements DisplayableError {
  """The error code."""
  code: CartErrorCode

  """The path to the input field that caused the error."""
  field: [String!]

  """The error message."""
  message: String!
}

"The input fields for submitting wallet payment method information for checkout.\n"
input CartWalletPaymentMethodInput {
  """The payment method information for the Apple Pay wallet."""
  applePayWalletContent: ApplePayWalletContentInput

  """The payment method information for the Shop Pay wallet."""
  shopPayWalletContent: ShopPayWalletContentInput
}

"""A warning that occurred during a cart mutation."""
type CartWarning {
  """The code of the warning."""
  code: CartWarningCode!

  """The message text of the warning."""
  message: String!

  """The target of the warning."""
  target: ID!
}

"""The code for the cart warning."""
enum CartWarningCode {
  """The merchandise does not have enough stock."""
  MERCHANDISE_NOT_ENOUGH_STOCK

  """The merchandise is out of stock."""
  MERCHANDISE_OUT_OF_STOCK

  """Gift cards are not available as a payment method."""
  PAYMENTS_GIFT_CARDS_UNAVAILABLE
}

"A collection represents a grouping of products that a shop owner can create to\norganize them or make their shops easier to browse.\n"
type Collection implements HasMetafields & Node & OnlineStorePublishable & Trackable {
  """
  Stripped description of the collection, single line with HTML tags removed.
  """
  description(
    """Truncates a string after the given length."""
    truncateAt: Int
  ): String!

  """The description of the collection, complete with HTML formatting."""
  descriptionHtml: HTML!

  "A human-friendly unique string for the collection automatically generated from its title.\nLimit of 255 characters.\n"
  handle: String!

  """A globally-unique ID."""
  id: ID!

  """Image associated with the collection."""
  image: Image

  """
  A [custom field](https://shopify.dev/docs/apps/build/custom-data), including its `namespace` and `key`, that's associated with a Shopify resource for the purposes of adding and storing additional information.
  """
  metafield(
    """
    The container the metafield belongs to. If omitted, the app-reserved namespace will be used.
    """
    namespace: String

    """The identifier for the metafield."""
    key: String!
  ): Metafield

  """
  A list of [custom fields](/docs/apps/build/custom-data) that a merchant associates with a Shopify resource.
  """
  metafields(
    """
    The list of metafields to retrieve by namespace and key.
    
    The input must not contain more than `250` values.
    """
    identifiers: [HasMetafieldsIdentifier!]!
  ): [Metafield]!

  """
  The URL used for viewing the resource on the shop's Online Store. Returns `null` if the resource is currently not published to the Online Store sales channel.
  """
  onlineStoreUrl: URL

  """List of products in the collection."""
  products(
    """Returns up to the first `n` elements from the list."""
    first: Int

    """Returns the elements that come after the specified cursor."""
    after: String

    """Returns up to the last `n` elements from the list."""
    last: Int

    """Returns the elements that come before the specified cursor."""
    before: String

    """Reverse the order of the underlying list."""
    reverse: Boolean = false

    """Sort the underlying list by the given key."""
    sortKey: ProductCollectionSortKeys = COLLECTION_DEFAULT

    """
    Returns a subset of products matching all product filters.
    
    The input must not contain more than `250` values.
    """
    filters: [ProductFilter!]
  ): ProductConnection!

  """The collection's SEO information."""
  seo: SEO!

  """The collection’s name. Limit of 255 characters."""
  title: String!

  """
  URL parameters to be added to a page URL to track the origin of on-site search traffic for [analytics reporting](https://help.shopify.com/manual/reports-and-analytics/shopify-reports/report-types/default-reports/behaviour-reports). Returns a result when accessed through the [search](https://shopify.dev/docs/api/storefront/current/queries/search) or [predictiveSearch](https://shopify.dev/docs/api/storefront/current/queries/predictiveSearch) queries, otherwise returns null.
  """
  trackingParameters: String

  """The date and time when the collection was last modified."""
  updatedAt: DateTime!
}

"An auto-generated type for paginating through multiple Collections.\n"
type CollectionConnection {
  """A list of edges."""
  edges: [CollectionEdge!]!

  """A list of the nodes contained in CollectionEdge."""
  nodes: [Collection!]!

  """Information to aid in pagination."""
  pageInfo: PageInfo!

  """The total count of Collections."""
  totalCount: UnsignedInt64!
}

"An auto-generated type which holds one Collection and a cursor during pagination.\n"
type CollectionEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of CollectionEdge."""
  node: Collection!
}

"""The set of valid sort keys for the Collection query."""
enum CollectionSortKeys {
  """Sort by the `title` value."""
  TITLE

  """Sort by the `updated_at` value."""
  UPDATED_AT

  """Sort by the `id` value."""
  ID

  "Sort by relevance to the search terms when the `query` parameter is specified on the connection.\nDon't use this sort key when no search query is specified.\n"
  RELEVANCE
}

"A string containing a hexadecimal representation of a color.\n\nFor example, \"#6A8D48\".\n"
scalar Color

"""A comment on an article."""
type Comment implements Node {
  """The comment’s author."""
  author: CommentAuthor!

  """Stripped content of the comment, single line with HTML tags removed."""
  content(
    """Truncates a string after the given length."""
    truncateAt: Int
  ): String!

  """The content of the comment, complete with HTML formatting."""
  contentHtml: HTML!

  """A globally-unique ID."""
  id: ID!
}

"""The author of a comment."""
type CommentAuthor {
  """The author's email."""
  email: String!

  """The author’s name."""
  name: String!
}

"An auto-generated type for paginating through multiple Comments.\n"
type CommentConnection {
  """A list of edges."""
  edges: [CommentEdge!]!

  """A list of the nodes contained in CommentEdge."""
  nodes: [Comment!]!

  """Information to aid in pagination."""
  pageInfo: PageInfo!
}

"An auto-generated type which holds one Comment and a cursor during pagination.\n"
type CommentEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of CommentEdge."""
  node: Comment!
}

"""
Represents information about a company which is also a customer of the shop.
"""
type Company implements HasMetafields & Node {
  """
  The date and time ([ISO 8601 format](http://en.wikipedia.org/wiki/ISO_8601)) at which the company was created in Shopify.
  """
  createdAt: DateTime!

  """A unique externally-supplied ID for the company."""
  externalId: String

  """A globally-unique ID."""
  id: ID!

  """
  A [custom field](https://shopify.dev/docs/apps/build/custom-data), including its `namespace` and `key`, that's associated with a Shopify resource for the purposes of adding and storing additional information.
  """
  metafield(
    """
    The container the metafield belongs to. If omitted, the app-reserved namespace will be used.
    """
    namespace: String

    """The identifier for the metafield."""
    key: String!
  ): Metafield

  """
  A list of [custom fields](/docs/apps/build/custom-data) that a merchant associates with a Shopify resource.
  """
  metafields(
    """
    The list of metafields to retrieve by namespace and key.
    
    The input must not contain more than `250` values.
    """
    identifiers: [HasMetafieldsIdentifier!]!
  ): [Metafield]!

  """The name of the company."""
  name: String!

  """
  The date and time ([ISO 8601 format](http://en.wikipedia.org/wiki/ISO_8601)) at which the company was last modified.
  """
  updatedAt: DateTime!
}

"""A company's main point of contact."""
type CompanyContact implements Node {
  """
  The date and time ([ISO 8601 format](http://en.wikipedia.org/wiki/ISO_8601)) at which the company contact was created in Shopify.
  """
  createdAt: DateTime!

  """A globally-unique ID."""
  id: ID!

  """The company contact's locale (language)."""
  locale: String

  """The company contact's job title."""
  title: String

  """
  The date and time ([ISO 8601 format](http://en.wikipedia.org/wiki/ISO_8601)) at which the company contact was last modified.
  """
  updatedAt: DateTime!
}

"""A company's location."""
type CompanyLocation implements HasMetafields & Node {
  """
  The date and time ([ISO 8601 format](http://en.wikipedia.org/wiki/ISO_8601)) at which the company location was created in Shopify.
  """
  createdAt: DateTime!

  """A unique externally-supplied ID for the company."""
  externalId: String

  """A globally-unique ID."""
  id: ID!

  """The preferred locale of the company location."""
  locale: String

  """
  A [custom field](https://shopify.dev/docs/apps/build/custom-data), including its `namespace` and `key`, that's associated with a Shopify resource for the purposes of adding and storing additional information.
  """
  metafield(
    """
    The container the metafield belongs to. If omitted, the app-reserved namespace will be used.
    """
    namespace: String

    """The identifier for the metafield."""
    key: String!
  ): Metafield

  """
  A list of [custom fields](/docs/apps/build/custom-data) that a merchant associates with a Shopify resource.
  """
  metafields(
    """
    The list of metafields to retrieve by namespace and key.
    
    The input must not contain more than `250` values.
    """
    identifiers: [HasMetafieldsIdentifier!]!
  ): [Metafield]!

  """The name of the company location."""
  name: String!

  """
  The date and time ([ISO 8601 format](http://en.wikipedia.org/wiki/ISO_8601)) at which the company location was last modified.
  """
  updatedAt: DateTime!
}

"""The action for the 3DS payment redirect."""
type CompletePaymentChallenge {
  """The URL for the 3DS payment redirect."""
  redirectUrl: URL
}

"""An error that occurred during a cart completion attempt."""
type CompletionError {
  """The error code."""
  code: CompletionErrorCode!

  """The error message."""
  message: String
}

"""The code of the error that occurred during a cart completion attempt."""
enum CompletionErrorCode {
  ERROR
  INVENTORY_RESERVATION_ERROR
  PAYMENT_ERROR
  PAYMENT_TRANSIENT_ERROR
  PAYMENT_AMOUNT_TOO_SMALL
  PAYMENT_GATEWAY_NOT_ENABLED_ERROR
  PAYMENT_INSUFFICIENT_FUNDS
  PAYMENT_INVALID_PAYMENT_METHOD
  PAYMENT_INVALID_CURRENCY
  PAYMENT_INVALID_CREDIT_CARD
  PAYMENT_INVALID_BILLING_ADDRESS
  PAYMENT_CARD_DECLINED
  PAYMENT_CALL_ISSUER
}

"""Represents information about the grouped merchandise in the cart."""
type ComponentizableCartLine implements BaseCartLine & Node {
  """An attribute associated with the cart line."""
  attribute(
    """The key of the attribute."""
    key: String!
  ): Attribute

  """
  The attributes associated with the cart line. Attributes are represented as key-value pairs.
  """
  attributes: [Attribute!]!

  """
  The cost of the merchandise that the buyer will pay for at checkout. The costs are subject to change and changes will be reflected at checkout.
  """
  cost: CartLineCost!

  """The discounts that have been applied to the cart line."""
  discountAllocations: [CartDiscountAllocation!]!

  """
  The estimated cost of the merchandise that the buyer will pay for at checkout. The estimated costs are subject to change and changes will be reflected at checkout.
  """
  estimatedCost: CartLineEstimatedCost! @deprecated(reason: "Use `cost` instead.")

  """A globally-unique ID."""
  id: ID!

  """The components of the line item."""
  lineComponents: [CartLine!]!

  """The merchandise that the buyer intends to purchase."""
  merchandise: Merchandise!

  """The quantity of the merchandise that the customer intends to purchase."""
  quantity: Int!

  """
  The selling plan associated with the cart line and the effect that each selling plan has on variants when they're purchased.
  """
  sellingPlanAllocation: SellingPlanAllocation
}

"""Details for count of elements."""
type Count {
  """Count of elements."""
  count: Int!

  """Precision of count, how exact is the value."""
  precision: CountPrecision!
}

"""The precision of the value returned by a count field."""
enum CountPrecision {
  """The count is exactly the value."""
  EXACT

  """The count is at least the value. A limit was reached."""
  AT_LEAST
}

"""A country."""
type Country {
  """The languages available for the country."""
  availableLanguages: [Language!]!

  """The currency of the country."""
  currency: Currency!

  """The ISO code of the country."""
  isoCode: CountryCode!

  """The market that includes this country."""
  market: Market

  """The name of the country."""
  name: String!

  """The unit system used in the country."""
  unitSystem: UnitSystem!
}

"The code designating a country/region, which generally follows ISO 3166-1 alpha-2 guidelines.\nIf a territory doesn't have a country code value in the `CountryCode` enum, then it might be considered a subdivision\nof another country. For example, the territories associated with Spain are represented by the country code `ES`,\nand the territories associated with the United States of America are represented by the country code `US`.\n"
enum CountryCode {
  """Afghanistan."""
  AF

  """Åland Islands."""
  AX

  """Albania."""
  AL

  """Algeria."""
  DZ

  """Andorra."""
  AD

  """Angola."""
  AO

  """Anguilla."""
  AI

  """Antigua & Barbuda."""
  AG

  """Argentina."""
  AR

  """Armenia."""
  AM

  """Aruba."""
  AW

  """Ascension Island."""
  AC

  """Australia."""
  AU

  """Austria."""
  AT

  """Azerbaijan."""
  AZ

  """Bahamas."""
  BS

  """Bahrain."""
  BH

  """Bangladesh."""
  BD

  """Barbados."""
  BB

  """Belarus."""
  BY

  """Belgium."""
  BE

  """Belize."""
  BZ

  """Benin."""
  BJ

  """Bermuda."""
  BM

  """Bhutan."""
  BT

  """Bolivia."""
  BO

  """Bosnia & Herzegovina."""
  BA

  """Botswana."""
  BW

  """Bouvet Island."""
  BV

  """Brazil."""
  BR

  """British Indian Ocean Territory."""
  IO

  """Brunei."""
  BN

  """Bulgaria."""
  BG

  """Burkina Faso."""
  BF

  """Burundi."""
  BI

  """Cambodia."""
  KH

  """Canada."""
  CA

  """Cape Verde."""
  CV

  """Caribbean Netherlands."""
  BQ

  """Cayman Islands."""
  KY

  """Central African Republic."""
  CF

  """Chad."""
  TD

  """Chile."""
  CL

  """China."""
  CN

  """Christmas Island."""
  CX

  """Cocos (Keeling) Islands."""
  CC

  """Colombia."""
  CO

  """Comoros."""
  KM

  """Congo - Brazzaville."""
  CG

  """Congo - Kinshasa."""
  CD

  """Cook Islands."""
  CK

  """Costa Rica."""
  CR

  """Croatia."""
  HR

  """Cuba."""
  CU

  """Curaçao."""
  CW

  """Cyprus."""
  CY

  """Czechia."""
  CZ

  """Côte d’Ivoire."""
  CI

  """Denmark."""
  DK

  """Djibouti."""
  DJ

  """Dominica."""
  DM

  """Dominican Republic."""
  DO

  """Ecuador."""
  EC

  """Egypt."""
  EG

  """El Salvador."""
  SV

  """Equatorial Guinea."""
  GQ

  """Eritrea."""
  ER

  """Estonia."""
  EE

  """Eswatini."""
  SZ

  """Ethiopia."""
  ET

  """Falkland Islands."""
  FK

  """Faroe Islands."""
  FO

  """Fiji."""
  FJ

  """Finland."""
  FI

  """France."""
  FR

  """French Guiana."""
  GF

  """French Polynesia."""
  PF

  """French Southern Territories."""
  TF

  """Gabon."""
  GA

  """Gambia."""
  GM

  """Georgia."""
  GE

  """Germany."""
  DE

  """Ghana."""
  GH

  """Gibraltar."""
  GI

  """Greece."""
  GR

  """Greenland."""
  GL

  """Grenada."""
  GD

  """Guadeloupe."""
  GP

  """Guatemala."""
  GT

  """Guernsey."""
  GG

  """Guinea."""
  GN

  """Guinea-Bissau."""
  GW

  """Guyana."""
  GY

  """Haiti."""
  HT

  """Heard & McDonald Islands."""
  HM

  """Vatican City."""
  VA

  """Honduras."""
  HN

  """Hong Kong SAR."""
  HK

  """Hungary."""
  HU

  """Iceland."""
  IS

  """India."""
  IN

  """Indonesia."""
  ID

  """Iran."""
  IR

  """Iraq."""
  IQ

  """Ireland."""
  IE

  """Isle of Man."""
  IM

  """Israel."""
  IL

  """Italy."""
  IT

  """Jamaica."""
  JM

  """Japan."""
  JP

  """Jersey."""
  JE

  """Jordan."""
  JO

  """Kazakhstan."""
  KZ

  """Kenya."""
  KE

  """Kiribati."""
  KI

  """North Korea."""
  KP

  """Kosovo."""
  XK

  """Kuwait."""
  KW

  """Kyrgyzstan."""
  KG

  """Laos."""
  LA

  """Latvia."""
  LV

  """Lebanon."""
  LB

  """Lesotho."""
  LS

  """Liberia."""
  LR

  """Libya."""
  LY

  """Liechtenstein."""
  LI

  """Lithuania."""
  LT

  """Luxembourg."""
  LU

  """Macao SAR."""
  MO

  """Madagascar."""
  MG

  """Malawi."""
  MW

  """Malaysia."""
  MY

  """Maldives."""
  MV

  """Mali."""
  ML

  """Malta."""
  MT

  """Martinique."""
  MQ

  """Mauritania."""
  MR

  """Mauritius."""
  MU

  """Mayotte."""
  YT

  """Mexico."""
  MX

  """Moldova."""
  MD

  """Monaco."""
  MC

  """Mongolia."""
  MN

  """Montenegro."""
  ME

  """Montserrat."""
  MS

  """Morocco."""
  MA

  """Mozambique."""
  MZ

  """Myanmar (Burma)."""
  MM

  """Namibia."""
  NA

  """Nauru."""
  NR

  """Nepal."""
  NP

  """Netherlands."""
  NL

  """Netherlands Antilles."""
  AN

  """New Caledonia."""
  NC

  """New Zealand."""
  NZ

  """Nicaragua."""
  NI

  """Niger."""
  NE

  """Nigeria."""
  NG

  """Niue."""
  NU

  """Norfolk Island."""
  NF

  """North Macedonia."""
  MK

  """Norway."""
  NO

  """Oman."""
  OM

  """Pakistan."""
  PK

  """Palestinian Territories."""
  PS

  """Panama."""
  PA

  """Papua New Guinea."""
  PG

  """Paraguay."""
  PY

  """Peru."""
  PE

  """Philippines."""
  PH

  """Pitcairn Islands."""
  PN

  """Poland."""
  PL

  """Portugal."""
  PT

  """Qatar."""
  QA

  """Cameroon."""
  CM

  """Réunion."""
  RE

  """Romania."""
  RO

  """Russia."""
  RU

  """Rwanda."""
  RW

  """St. Barthélemy."""
  BL

  """St. Helena."""
  SH

  """St. Kitts & Nevis."""
  KN

  """St. Lucia."""
  LC

  """St. Martin."""
  MF

  """St. Pierre & Miquelon."""
  PM

  """Samoa."""
  WS

  """San Marino."""
  SM

  """São Tomé & Príncipe."""
  ST

  """Saudi Arabia."""
  SA

  """Senegal."""
  SN

  """Serbia."""
  RS

  """Seychelles."""
  SC

  """Sierra Leone."""
  SL

  """Singapore."""
  SG

  """Sint Maarten."""
  SX

  """Slovakia."""
  SK

  """Slovenia."""
  SI

  """Solomon Islands."""
  SB

  """Somalia."""
  SO

  """South Africa."""
  ZA

  """South Georgia & South Sandwich Islands."""
  GS

  """South Korea."""
  KR

  """South Sudan."""
  SS

  """Spain."""
  ES

  """Sri Lanka."""
  LK

  """St. Vincent & Grenadines."""
  VC

  """Sudan."""
  SD

  """Suriname."""
  SR

  """Svalbard & Jan Mayen."""
  SJ

  """Sweden."""
  SE

  """Switzerland."""
  CH

  """Syria."""
  SY

  """Taiwan."""
  TW

  """Tajikistan."""
  TJ

  """Tanzania."""
  TZ

  """Thailand."""
  TH

  """Timor-Leste."""
  TL

  """Togo."""
  TG

  """Tokelau."""
  TK

  """Tonga."""
  TO

  """Trinidad & Tobago."""
  TT

  """Tristan da Cunha."""
  TA

  """Tunisia."""
  TN

  """Türkiye."""
  TR

  """Turkmenistan."""
  TM

  """Turks & Caicos Islands."""
  TC

  """Tuvalu."""
  TV

  """Uganda."""
  UG

  """Ukraine."""
  UA

  """United Arab Emirates."""
  AE

  """United Kingdom."""
  GB

  """United States."""
  US

  """U.S. Outlying Islands."""
  UM

  """Uruguay."""
  UY

  """Uzbekistan."""
  UZ

  """Vanuatu."""
  VU

  """Venezuela."""
  VE

  """Vietnam."""
  VN

  """British Virgin Islands."""
  VG

  """Wallis & Futuna."""
  WF

  """Western Sahara."""
  EH

  """Yemen."""
  YE

  """Zambia."""
  ZM

  """Zimbabwe."""
  ZW

  """Unknown Region."""
  ZZ
}

"""The part of the image that should remain after cropping."""
enum CropRegion {
  """Keep the center of the image."""
  CENTER

  """Keep the top of the image."""
  TOP

  """Keep the bottom of the image."""
  BOTTOM

  """Keep the left of the image."""
  LEFT

  """Keep the right of the image."""
  RIGHT
}

"""A currency."""
type Currency {
  """The ISO code of the currency."""
  isoCode: CurrencyCode!

  """The name of the currency."""
  name: String!

  """The symbol of the currency."""
  symbol: String!
}

"The three-letter currency codes that represent the world currencies used in\nstores. These include standard ISO 4217 codes, legacy codes,\nand non-standard codes.\n"
enum CurrencyCode {
  """United States Dollars (USD)."""
  USD

  """Euro (EUR)."""
  EUR

  """United Kingdom Pounds (GBP)."""
  GBP

  """Canadian Dollars (CAD)."""
  CAD

  """Afghan Afghani (AFN)."""
  AFN

  """Albanian Lek (ALL)."""
  ALL

  """Algerian Dinar (DZD)."""
  DZD

  """Angolan Kwanza (AOA)."""
  AOA

  """Argentine Pesos (ARS)."""
  ARS

  """Armenian Dram (AMD)."""
  AMD

  """Aruban Florin (AWG)."""
  AWG

  """Australian Dollars (AUD)."""
  AUD

  """Barbadian Dollar (BBD)."""
  BBD

  """Azerbaijani Manat (AZN)."""
  AZN

  """Bangladesh Taka (BDT)."""
  BDT

  """Bahamian Dollar (BSD)."""
  BSD

  """Bahraini Dinar (BHD)."""
  BHD

  """Burundian Franc (BIF)."""
  BIF

  """Belize Dollar (BZD)."""
  BZD

  """Bermudian Dollar (BMD)."""
  BMD

  """Bhutanese Ngultrum (BTN)."""
  BTN

  """Bosnia and Herzegovina Convertible Mark (BAM)."""
  BAM

  """Brazilian Real (BRL)."""
  BRL

  """Bolivian Boliviano (BOB)."""
  BOB

  """Botswana Pula (BWP)."""
  BWP

  """Brunei Dollar (BND)."""
  BND

  """Bulgarian Lev (BGN)."""
  BGN

  """Burmese Kyat (MMK)."""
  MMK

  """Cambodian Riel."""
  KHR

  """Cape Verdean escudo (CVE)."""
  CVE

  """Cayman Dollars (KYD)."""
  KYD

  """Central African CFA Franc (XAF)."""
  XAF

  """Chilean Peso (CLP)."""
  CLP

  """Chinese Yuan Renminbi (CNY)."""
  CNY

  """Colombian Peso (COP)."""
  COP

  """Comorian Franc (KMF)."""
  KMF

  """Congolese franc (CDF)."""
  CDF

  """Costa Rican Colones (CRC)."""
  CRC

  """Croatian Kuna (HRK)."""
  HRK

  """Czech Koruny (CZK)."""
  CZK

  """Danish Kroner (DKK)."""
  DKK

  """Dominican Peso (DOP)."""
  DOP

  """East Caribbean Dollar (XCD)."""
  XCD

  """Egyptian Pound (EGP)."""
  EGP

  """Eritrean Nakfa (ERN)."""
  ERN

  """Ethiopian Birr (ETB)."""
  ETB

  """Falkland Islands Pounds (FKP)."""
  FKP

  """CFP Franc (XPF)."""
  XPF

  """Fijian Dollars (FJD)."""
  FJD

  """Gibraltar Pounds (GIP)."""
  GIP

  """Gambian Dalasi (GMD)."""
  GMD

  """Ghanaian Cedi (GHS)."""
  GHS

  """Guatemalan Quetzal (GTQ)."""
  GTQ

  """Guyanese Dollar (GYD)."""
  GYD

  """Georgian Lari (GEL)."""
  GEL

  """Haitian Gourde (HTG)."""
  HTG

  """Honduran Lempira (HNL)."""
  HNL

  """Hong Kong Dollars (HKD)."""
  HKD

  """Hungarian Forint (HUF)."""
  HUF

  """Icelandic Kronur (ISK)."""
  ISK

  """Indian Rupees (INR)."""
  INR

  """Indonesian Rupiah (IDR)."""
  IDR

  """Israeli New Shekel (NIS)."""
  ILS

  """Iraqi Dinar (IQD)."""
  IQD

  """Jamaican Dollars (JMD)."""
  JMD

  """Japanese Yen (JPY)."""
  JPY

  """Jersey Pound."""
  JEP

  """Jordanian Dinar (JOD)."""
  JOD

  """Kazakhstani Tenge (KZT)."""
  KZT

  """Kenyan Shilling (KES)."""
  KES

  """Kiribati Dollar (KID)."""
  KID

  """Kuwaiti Dinar (KWD)."""
  KWD

  """Kyrgyzstani Som (KGS)."""
  KGS

  """Laotian Kip (LAK)."""
  LAK

  """Latvian Lati (LVL)."""
  LVL

  """Lebanese Pounds (LBP)."""
  LBP

  """Lesotho Loti (LSL)."""
  LSL

  """Liberian Dollar (LRD)."""
  LRD

  """Lithuanian Litai (LTL)."""
  LTL

  """Malagasy Ariary (MGA)."""
  MGA

  """Macedonia Denar (MKD)."""
  MKD

  """Macanese Pataca (MOP)."""
  MOP

  """Malawian Kwacha (MWK)."""
  MWK

  """Maldivian Rufiyaa (MVR)."""
  MVR

  """Mauritanian Ouguiya (MRU)."""
  MRU

  """Mexican Pesos (MXN)."""
  MXN

  """Malaysian Ringgits (MYR)."""
  MYR

  """Mauritian Rupee (MUR)."""
  MUR

  """Moldovan Leu (MDL)."""
  MDL

  """Moroccan Dirham."""
  MAD

  """Mongolian Tugrik."""
  MNT

  """Mozambican Metical."""
  MZN

  """Namibian Dollar."""
  NAD

  """Nepalese Rupee (NPR)."""
  NPR

  """Netherlands Antillean Guilder."""
  ANG

  """New Zealand Dollars (NZD)."""
  NZD

  """Nicaraguan Córdoba (NIO)."""
  NIO

  """Nigerian Naira (NGN)."""
  NGN

  """Norwegian Kroner (NOK)."""
  NOK

  """Omani Rial (OMR)."""
  OMR

  """Panamian Balboa (PAB)."""
  PAB

  """Pakistani Rupee (PKR)."""
  PKR

  """Papua New Guinean Kina (PGK)."""
  PGK

  """Paraguayan Guarani (PYG)."""
  PYG

  """Peruvian Nuevo Sol (PEN)."""
  PEN

  """Philippine Peso (PHP)."""
  PHP

  """Polish Zlotych (PLN)."""
  PLN

  """Qatari Rial (QAR)."""
  QAR

  """Romanian Lei (RON)."""
  RON

  """Russian Rubles (RUB)."""
  RUB

  """Rwandan Franc (RWF)."""
  RWF

  """Samoan Tala (WST)."""
  WST

  """Saint Helena Pounds (SHP)."""
  SHP

  """Saudi Riyal (SAR)."""
  SAR

  """Serbian dinar (RSD)."""
  RSD

  """Seychellois Rupee (SCR)."""
  SCR

  """Singapore Dollars (SGD)."""
  SGD

  """Sudanese Pound (SDG)."""
  SDG

  """Somali Shilling (SOS)."""
  SOS

  """Syrian Pound (SYP)."""
  SYP

  """South African Rand (ZAR)."""
  ZAR

  """South Korean Won (KRW)."""
  KRW

  """South Sudanese Pound (SSP)."""
  SSP

  """Solomon Islands Dollar (SBD)."""
  SBD

  """Sri Lankan Rupees (LKR)."""
  LKR

  """Surinamese Dollar (SRD)."""
  SRD

  """Swazi Lilangeni (SZL)."""
  SZL

  """Swedish Kronor (SEK)."""
  SEK

  """Swiss Francs (CHF)."""
  CHF

  """Taiwan Dollars (TWD)."""
  TWD

  """Thai baht (THB)."""
  THB

  """Tanzanian Shilling (TZS)."""
  TZS

  """Trinidad and Tobago Dollars (TTD)."""
  TTD

  """Tunisian Dinar (TND)."""
  TND

  """Turkish Lira (TRY)."""
  TRY

  """Turkmenistani Manat (TMT)."""
  TMT

  """Ugandan Shilling (UGX)."""
  UGX

  """Ukrainian Hryvnia (UAH)."""
  UAH

  """United Arab Emirates Dirham (AED)."""
  AED

  """Uruguayan Pesos (UYU)."""
  UYU

  """Uzbekistan som (UZS)."""
  UZS

  """Vanuatu Vatu (VUV)."""
  VUV

  """Venezuelan Bolivares Soberanos (VES)."""
  VES

  """Vietnamese đồng (VND)."""
  VND

  """West African CFA franc (XOF)."""
  XOF

  """Yemeni Rial (YER)."""
  YER

  """Zambian Kwacha (ZMW)."""
  ZMW

  """Belarusian Ruble (BYN)."""
  BYN

  """Belarusian Ruble (BYR)."""
  BYR @deprecated(reason: "`BYR` is deprecated. Use `BYN` available from version `2021-01` onwards instead.")

  """Djiboutian Franc (DJF)."""
  DJF

  """Guinean Franc (GNF)."""
  GNF

  """Iranian Rial (IRR)."""
  IRR

  """Libyan Dinar (LYD)."""
  LYD

  """Sierra Leonean Leone (SLL)."""
  SLL

  """Sao Tome And Principe Dobra (STD)."""
  STD @deprecated(reason: "`STD` is deprecated. Use `STN` available from version `2022-07` onwards instead.")

  """Sao Tome And Principe Dobra (STN)."""
  STN

  """Tajikistani Somoni (TJS)."""
  TJS

  """Tongan Pa'anga (TOP)."""
  TOP

  """Venezuelan Bolivares (VED)."""
  VED

  """Venezuelan Bolivares (VEF)."""
  VEF @deprecated(reason: "`VEF` is deprecated. Use `VES` available from version `2020-10` onwards instead.")

  """Unrecognized currency."""
  XXX
}

"""
A customer represents a customer account with the shop. Customer accounts store contact information for the customer, saving logged-in customers the trouble of having to provide it at every checkout.
"""
type Customer implements HasMetafields {
  """
  Indicates whether the customer has consented to be sent marketing material via email.
  """
  acceptsMarketing: Boolean!

  """A list of addresses for the customer."""
  addresses(
    """Returns up to the first `n` elements from the list."""
    first: Int

    """Returns the elements that come after the specified cursor."""
    after: String

    """Returns up to the last `n` elements from the list."""
    last: Int

    """Returns the elements that come before the specified cursor."""
    before: String

    """Reverse the order of the underlying list."""
    reverse: Boolean = false
  ): MailingAddressConnection!

  """The date and time when the customer was created."""
  createdAt: DateTime!

  """The customer’s default address."""
  defaultAddress: MailingAddress

  """The customer’s name, email or phone number."""
  displayName: String!

  """The customer’s email address."""
  email: String

  """The customer’s first name."""
  firstName: String

  """A unique ID for the customer."""
  id: ID!

  """The customer’s last name."""
  lastName: String

  """
  A [custom field](https://shopify.dev/docs/apps/build/custom-data), including its `namespace` and `key`, that's associated with a Shopify resource for the purposes of adding and storing additional information.
  """
  metafield(
    """
    The container the metafield belongs to. If omitted, the app-reserved namespace will be used.
    """
    namespace: String

    """The identifier for the metafield."""
    key: String!
  ): Metafield

  """
  A list of [custom fields](/docs/apps/build/custom-data) that a merchant associates with a Shopify resource.
  """
  metafields(
    """
    The list of metafields to retrieve by namespace and key.
    
    The input must not contain more than `250` values.
    """
    identifiers: [HasMetafieldsIdentifier!]!
  ): [Metafield]!

  """
  The number of orders that the customer has made at the store in their lifetime.
  """
  numberOfOrders: UnsignedInt64!

  """The orders associated with the customer."""
  orders(
    """Returns up to the first `n` elements from the list."""
    first: Int

    """Returns the elements that come after the specified cursor."""
    after: String

    """Returns up to the last `n` elements from the list."""
    last: Int

    """Returns the elements that come before the specified cursor."""
    before: String

    """Reverse the order of the underlying list."""
    reverse: Boolean = false

    """Sort the underlying list by the given key."""
    sortKey: OrderSortKeys = ID

    "Apply one or multiple filters to the query.\n| name | description | acceptable_values | default_value | example_use |\n| ---- | ---- | ---- | ---- | ---- |\n| processed_at |\nRefer to the detailed [search syntax](https://shopify.dev/api/usage/search-syntax) for more information about using filters.\n"
    query: String
  ): OrderConnection!

  """The customer’s phone number."""
  phone: String

  "A comma separated list of tags that have been added to the customer.\nAdditional access scope required: unauthenticated_read_customer_tags.\n"
  tags: [String!]!

  """The date and time when the customer information was updated."""
  updatedAt: DateTime!
}

"""
A CustomerAccessToken represents the unique token required to make modifications to the customer object.
"""
type CustomerAccessToken {
  """The customer’s access token."""
  accessToken: String!

  """The date and time when the customer access token expires."""
  expiresAt: DateTime!
}

"""The input fields required to create a customer access token."""
input CustomerAccessTokenCreateInput {
  """The email associated to the customer."""
  email: String!

  """The login password to be used by the customer."""
  password: String!
}

"""Return type for `customerAccessTokenCreate` mutation."""
type CustomerAccessTokenCreatePayload {
  """The newly created customer access token object."""
  customerAccessToken: CustomerAccessToken

  """The list of errors that occurred from executing the mutation."""
  customerUserErrors: [CustomerUserError!]!

  """The list of errors that occurred from executing the mutation."""
  userErrors: [UserError!]! @deprecated(reason: "Use `customerUserErrors` instead.")
}

"""Return type for `customerAccessTokenCreateWithMultipass` mutation."""
type CustomerAccessTokenCreateWithMultipassPayload {
  """An access token object associated with the customer."""
  customerAccessToken: CustomerAccessToken

  """The list of errors that occurred from executing the mutation."""
  customerUserErrors: [CustomerUserError!]!
}

"""Return type for `customerAccessTokenDelete` mutation."""
type CustomerAccessTokenDeletePayload {
  """The destroyed access token."""
  deletedAccessToken: String

  """ID of the destroyed customer access token."""
  deletedCustomerAccessTokenId: String

  """The list of errors that occurred from executing the mutation."""
  userErrors: [UserError!]!
}

"""Return type for `customerAccessTokenRenew` mutation."""
type CustomerAccessTokenRenewPayload {
  """The renewed customer access token object."""
  customerAccessToken: CustomerAccessToken

  """The list of errors that occurred from executing the mutation."""
  userErrors: [UserError!]!
}

"""Return type for `customerActivateByUrl` mutation."""
type CustomerActivateByUrlPayload {
  """The customer that was activated."""
  customer: Customer

  """A new customer access token for the customer."""
  customerAccessToken: CustomerAccessToken

  """The list of errors that occurred from executing the mutation."""
  customerUserErrors: [CustomerUserError!]!
}

"""The input fields to activate a customer."""
input CustomerActivateInput {
  """The activation token required to activate the customer."""
  activationToken: String!

  """New password that will be set during activation."""
  password: String!
}

"""Return type for `customerActivate` mutation."""
type CustomerActivatePayload {
  """The customer object."""
  customer: Customer

  """A newly created customer access token object for the customer."""
  customerAccessToken: CustomerAccessToken

  """The list of errors that occurred from executing the mutation."""
  customerUserErrors: [CustomerUserError!]!

  """The list of errors that occurred from executing the mutation."""
  userErrors: [UserError!]! @deprecated(reason: "Use `customerUserErrors` instead.")
}

"""Return type for `customerAddressCreate` mutation."""
type CustomerAddressCreatePayload {
  """The new customer address object."""
  customerAddress: MailingAddress

  """The list of errors that occurred from executing the mutation."""
  customerUserErrors: [CustomerUserError!]!

  """The list of errors that occurred from executing the mutation."""
  userErrors: [UserError!]! @deprecated(reason: "Use `customerUserErrors` instead.")
}

"""Return type for `customerAddressDelete` mutation."""
type CustomerAddressDeletePayload {
  """The list of errors that occurred from executing the mutation."""
  customerUserErrors: [CustomerUserError!]!

  """ID of the deleted customer address."""
  deletedCustomerAddressId: String

  """The list of errors that occurred from executing the mutation."""
  userErrors: [UserError!]! @deprecated(reason: "Use `customerUserErrors` instead.")
}

"""Return type for `customerAddressUpdate` mutation."""
type CustomerAddressUpdatePayload {
  """The customer’s updated mailing address."""
  customerAddress: MailingAddress

  """The list of errors that occurred from executing the mutation."""
  customerUserErrors: [CustomerUserError!]!

  """The list of errors that occurred from executing the mutation."""
  userErrors: [UserError!]! @deprecated(reason: "Use `customerUserErrors` instead.")
}

"""The input fields to create a new customer."""
input CustomerCreateInput {
  """The customer’s first name."""
  firstName: String

  """The customer’s last name."""
  lastName: String

  """The customer’s email."""
  email: String!

  "A unique phone number for the customer.\n\nFormatted using E.164 standard. For example, _+16135551111_.\n"
  phone: String

  """The login password used by the customer."""
  password: String!

  """
  Indicates whether the customer has consented to be sent marketing material via email.
  """
  acceptsMarketing: Boolean
}

"""Return type for `customerCreate` mutation."""
type CustomerCreatePayload {
  """The created customer object."""
  customer: Customer

  """The list of errors that occurred from executing the mutation."""
  customerUserErrors: [CustomerUserError!]!

  """The list of errors that occurred from executing the mutation."""
  userErrors: [UserError!]! @deprecated(reason: "Use `customerUserErrors` instead.")
}

"""Return type for `customerDefaultAddressUpdate` mutation."""
type CustomerDefaultAddressUpdatePayload {
  """The updated customer object."""
  customer: Customer

  """The list of errors that occurred from executing the mutation."""
  customerUserErrors: [CustomerUserError!]!

  """The list of errors that occurred from executing the mutation."""
  userErrors: [UserError!]! @deprecated(reason: "Use `customerUserErrors` instead.")
}

"""Possible error codes that can be returned by `CustomerUserError`."""
enum CustomerErrorCode {
  """The input value is blank."""
  BLANK

  """The input value is invalid."""
  INVALID

  """The input value is already taken."""
  TAKEN

  """The input value is too long."""
  TOO_LONG

  """The input value is too short."""
  TOO_SHORT

  """Unidentified customer."""
  UNIDENTIFIED_CUSTOMER

  """Customer is disabled."""
  CUSTOMER_DISABLED

  """Input password starts or ends with whitespace."""
  PASSWORD_STARTS_OR_ENDS_WITH_WHITESPACE

  """Input contains HTML tags."""
  CONTAINS_HTML_TAGS

  """Input contains URL."""
  CONTAINS_URL

  """Invalid activation token."""
  TOKEN_INVALID

  """Customer already enabled."""
  ALREADY_ENABLED

  """Address does not exist."""
  NOT_FOUND

  """Input email contains an invalid domain name."""
  BAD_DOMAIN

  """Multipass token is not valid."""
  INVALID_MULTIPASS_REQUEST
}

"""Return type for `customerRecover` mutation."""
type CustomerRecoverPayload {
  """The list of errors that occurred from executing the mutation."""
  customerUserErrors: [CustomerUserError!]!

  """The list of errors that occurred from executing the mutation."""
  userErrors: [UserError!]! @deprecated(reason: "Use `customerUserErrors` instead.")
}

"""Return type for `customerResetByUrl` mutation."""
type CustomerResetByUrlPayload {
  """The customer object which was reset."""
  customer: Customer

  """A newly created customer access token object for the customer."""
  customerAccessToken: CustomerAccessToken

  """The list of errors that occurred from executing the mutation."""
  customerUserErrors: [CustomerUserError!]!

  """The list of errors that occurred from executing the mutation."""
  userErrors: [UserError!]! @deprecated(reason: "Use `customerUserErrors` instead.")
}

"""The input fields to reset a customer's password."""
input CustomerResetInput {
  """The reset token required to reset the customer’s password."""
  resetToken: String!

  """New password that will be set as part of the reset password process."""
  password: String!
}

"""Return type for `customerReset` mutation."""
type CustomerResetPayload {
  """The customer object which was reset."""
  customer: Customer

  """A newly created customer access token object for the customer."""
  customerAccessToken: CustomerAccessToken

  """The list of errors that occurred from executing the mutation."""
  customerUserErrors: [CustomerUserError!]!

  """The list of errors that occurred from executing the mutation."""
  userErrors: [UserError!]! @deprecated(reason: "Use `customerUserErrors` instead.")
}

"""The input fields to update the Customer information."""
input CustomerUpdateInput {
  """The customer’s first name."""
  firstName: String

  """The customer’s last name."""
  lastName: String

  """The customer’s email."""
  email: String

  "A unique phone number for the customer.\n\nFormatted using E.164 standard. For example, _+16135551111_. To remove the phone number, specify `null`.\n"
  phone: String

  """The login password used by the customer."""
  password: String

  """
  Indicates whether the customer has consented to be sent marketing material via email.
  """
  acceptsMarketing: Boolean
}

"""Return type for `customerUpdate` mutation."""
type CustomerUpdatePayload {
  """The updated customer object."""
  customer: Customer

  "The newly created customer access token. If the customer's password is updated, all previous access tokens\n(including the one used to perform this mutation) become invalid, and a new token is generated.\n"
  customerAccessToken: CustomerAccessToken

  """The list of errors that occurred from executing the mutation."""
  customerUserErrors: [CustomerUserError!]!

  """The list of errors that occurred from executing the mutation."""
  userErrors: [UserError!]! @deprecated(reason: "Use `customerUserErrors` instead.")
}

"""
Represents an error that happens during execution of a customer mutation.
"""
type CustomerUserError implements DisplayableError {
  """The error code."""
  code: CustomerErrorCode

  """The path to the input field that caused the error."""
  field: [String!]

  """The error message."""
  message: String!
}

"Represents an [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601)-encoded date and time string.\nFor example, 3:50 pm on September 7, 2019 in the time zone of UTC (Coordinated Universal Time) is\nrepresented as `\"2019-09-07T15:50:00Z`\".\n"
scalar DateTime

"A signed decimal number, which supports arbitrary precision and is serialized as a string.\n\nExample values: `\"29.99\"`, `\"29.999\"`.\n"
scalar Decimal

"""A delivery address of the buyer that is interacting with the cart."""
union DeliveryAddress = MailingAddress

"The input fields for delivery address preferences.\n"
input DeliveryAddressInput {
  """
  A delivery address preference of a buyer that is interacting with the cart.
  """
  deliveryAddress: MailingAddressInput

  "Whether the given delivery address is considered to be a one-time use address. One-time use addresses do not\nget persisted to the buyer's personal addresses when checking out.\n"
  oneTimeUse: Boolean = false

  """Defines what kind of address validation is requested."""
  deliveryAddressValidationStrategy: DeliveryAddressValidationStrategy = COUNTRY_CODE_ONLY

  "The ID of a customer address that is associated with the buyer that is interacting with the cart.\n"
  customerAddressId: ID
}

"Defines the types of available validation strategies for delivery addresses.\n"
enum DeliveryAddressValidationStrategy {
  """Only the country code is validated."""
  COUNTRY_CODE_ONLY

  "Strict validation is performed, i.e. all fields in the address are validated\naccording to Shopify's checkout rules. If the address fails validation, the cart will not be updated.\n"
  STRICT
}

"""List of different delivery method types."""
enum DeliveryMethodType {
  """Shipping."""
  SHIPPING

  """Local Pickup."""
  PICK_UP

  """Retail."""
  RETAIL

  """Local Delivery."""
  LOCAL

  """Shipping to a Pickup Point."""
  PICKUP_POINT

  """None."""
  NONE
}

"""
Digital wallet, such as Apple Pay, which can be used for accelerated checkouts.
"""
enum DigitalWallet {
  """Apple Pay."""
  APPLE_PAY

  """Android Pay."""
  ANDROID_PAY

  """Google Pay."""
  GOOGLE_PAY

  """Shopify Pay."""
  SHOPIFY_PAY
}

"An amount discounting the line that has been allocated by a discount.\n"
type DiscountAllocation {
  """Amount of discount allocated."""
  allocatedAmount: MoneyV2!

  """The discount this allocated amount originated from."""
  discountApplication: DiscountApplication!
}

"Discount applications capture the intentions of a discount source at\nthe time of application.\n"
interface DiscountApplication {
  """
  The method by which the discount's value is allocated to its entitled items.
  """
  allocationMethod: DiscountApplicationAllocationMethod!

  """Which lines of targetType that the discount is allocated over."""
  targetSelection: DiscountApplicationTargetSelection!

  """The type of line that the discount is applicable towards."""
  targetType: DiscountApplicationTargetType!

  """The value of the discount application."""
  value: PricingValue!
}

"""
The method by which the discount's value is allocated onto its entitled lines.
"""
enum DiscountApplicationAllocationMethod {
  """The value is spread across all entitled lines."""
  ACROSS

  """The value is applied onto every entitled line."""
  EACH

  """The value is specifically applied onto a particular line."""
  ONE @deprecated(reason: "Use ACROSS instead.")
}

"An auto-generated type for paginating through multiple DiscountApplications.\n"
type DiscountApplicationConnection {
  """A list of edges."""
  edges: [DiscountApplicationEdge!]!

  """A list of the nodes contained in DiscountApplicationEdge."""
  nodes: [DiscountApplication!]!

  """Information to aid in pagination."""
  pageInfo: PageInfo!
}

"An auto-generated type which holds one DiscountApplication and a cursor during pagination.\n"
type DiscountApplicationEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of DiscountApplicationEdge."""
  node: DiscountApplication!
}

"The lines on the order to which the discount is applied, of the type defined by\nthe discount application's `targetType`. For example, the value `ENTITLED`, combined with a `targetType` of\n`LINE_ITEM`, applies the discount on all line items that are entitled to the discount.\nThe value `ALL`, combined with a `targetType` of `SHIPPING_LINE`, applies the discount on all shipping lines.\n"
enum DiscountApplicationTargetSelection {
  """The discount is allocated onto all the lines."""
  ALL

  """The discount is allocated onto only the lines that it's entitled for."""
  ENTITLED

  """The discount is allocated onto explicitly chosen lines."""
  EXPLICIT
}

"The type of line (i.e. line item or shipping line) on an order that the discount is applicable towards.\n"
enum DiscountApplicationTargetType {
  """The discount applies onto line items."""
  LINE_ITEM

  """The discount applies onto shipping lines."""
  SHIPPING_LINE
}

"Discount code applications capture the intentions of a discount code at\nthe time that it is applied.\n"
type DiscountCodeApplication implements DiscountApplication {
  """
  The method by which the discount's value is allocated to its entitled items.
  """
  allocationMethod: DiscountApplicationAllocationMethod!

  """Specifies whether the discount code was applied successfully."""
  applicable: Boolean!

  """
  The string identifying the discount code that was used at the time of application.
  """
  code: String!

  """Which lines of targetType that the discount is allocated over."""
  targetSelection: DiscountApplicationTargetSelection!

  """The type of line that the discount is applicable towards."""
  targetType: DiscountApplicationTargetType!

  """The value of the discount application."""
  value: PricingValue!
}

"""Represents an error in the input of a mutation."""
interface DisplayableError {
  """The path to the input field that caused the error."""
  field: [String!]

  """The error message."""
  message: String!
}

"""Represents a web address."""
type Domain {
  """The host name of the domain (eg: `example.com`)."""
  host: String!

  """Whether SSL is enabled or not."""
  sslEnabled: Boolean!

  """The URL of the domain (eg: `https://example.com`)."""
  url: URL!
}

"""Represents a video hosted outside of Shopify."""
type ExternalVideo implements Media & Node {
  """A word or phrase to share the nature or contents of a media."""
  alt: String

  """The embed URL of the video for the respective host."""
  embedUrl: URL!

  """The URL."""
  embeddedUrl: URL! @deprecated(reason: "Use `originUrl` instead.")

  """The host of the external video."""
  host: MediaHost!

  """A globally-unique ID."""
  id: ID!

  """The media content type."""
  mediaContentType: MediaContentType!

  """The origin URL of the video on the respective host."""
  originUrl: URL!

  """The presentation for a media."""
  presentation: MediaPresentation

  """The preview image for the media."""
  previewImage: Image
}

"""A filter that is supported on the parent field."""
type Filter {
  """A unique identifier."""
  id: String!

  """A human-friendly string for this filter."""
  label: String!

  "Describes how to present the filter values.\nReturns a value only for filters of type `LIST`. Returns null for other types.\n"
  presentation: FilterPresentation

  """An enumeration that denotes the type of data this filter represents."""
  type: FilterType!

  """The list of values for this filter."""
  values: [FilterValue!]!
}

"Defines how to present the filter values, specifies the presentation of the filter.\n"
enum FilterPresentation {
  """Image presentation, filter values display an image."""
  IMAGE

  """Swatch presentation, filter values display color or image patterns."""
  SWATCH

  """Text presentation, no additional visual display for filter values."""
  TEXT
}

"The type of data that the filter group represents.\n\nFor more information, refer to [Filter products in a collection with the Storefront API]\n(https://shopify.dev/custom-storefronts/products-collections/filter-products).\n"
enum FilterType {
  """A list of selectable values."""
  LIST

  """A range of prices."""
  PRICE_RANGE

  """A boolean value."""
  BOOLEAN
}

"""A selectable value within a filter."""
type FilterValue {
  """The number of results that match this filter value."""
  count: Int!

  """A unique identifier."""
  id: String!

  """The visual representation when the filter's presentation is `IMAGE`."""
  image: MediaImage

  "An input object that can be used to filter by this value on the parent field.\n\nThe value is provided as a helper for building dynamic filtering UI. For\nexample, if you have a list of selected `FilterValue` objects, you can combine\ntheir respective `input` values to use in a subsequent query.\n"
  input: JSON!

  """A human-friendly string for this filter value."""
  label: String!

  """The visual representation when the filter's presentation is `SWATCH`."""
  swatch: Swatch
}

"""Represents a single fulfillment in an order."""
type Fulfillment {
  """List of the fulfillment's line items."""
  fulfillmentLineItems(
    """Returns up to the first `n` elements from the list."""
    first: Int

    """Returns the elements that come after the specified cursor."""
    after: String

    """Returns up to the last `n` elements from the list."""
    last: Int

    """Returns the elements that come before the specified cursor."""
    before: String

    """Reverse the order of the underlying list."""
    reverse: Boolean = false
  ): FulfillmentLineItemConnection!

  """The name of the tracking company."""
  trackingCompany: String

  "Tracking information associated with the fulfillment,\nsuch as the tracking number and tracking URL.\n"
  trackingInfo(
    """Truncate the array result to this size."""
    first: Int
  ): [FulfillmentTrackingInfo!]!
}

"""
Represents a single line item in a fulfillment. There is at most one fulfillment line item for each order line item.
"""
type FulfillmentLineItem {
  """The associated order's line item."""
  lineItem: OrderLineItem!

  """The amount fulfilled in this fulfillment."""
  quantity: Int!
}

"An auto-generated type for paginating through multiple FulfillmentLineItems.\n"
type FulfillmentLineItemConnection {
  """A list of edges."""
  edges: [FulfillmentLineItemEdge!]!

  """A list of the nodes contained in FulfillmentLineItemEdge."""
  nodes: [FulfillmentLineItem!]!

  """Information to aid in pagination."""
  pageInfo: PageInfo!
}

"An auto-generated type which holds one FulfillmentLineItem and a cursor during pagination.\n"
type FulfillmentLineItemEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of FulfillmentLineItemEdge."""
  node: FulfillmentLineItem!
}

"""Tracking information associated with the fulfillment."""
type FulfillmentTrackingInfo {
  """The tracking number of the fulfillment."""
  number: String

  """The URL to track the fulfillment."""
  url: URL
}

"""
The generic file resource lets you manage files in a merchant’s store. Generic files include any file that doesn’t fit into a designated type such as image or video. Example: PDF, JSON.
"""
type GenericFile implements Node {
  """A word or phrase to indicate the contents of a file."""
  alt: String

  """A globally-unique ID."""
  id: ID!

  """The MIME type of the file."""
  mimeType: String

  """The size of the original file in bytes."""
  originalFileSize: Int

  """The preview image for the file."""
  previewImage: Image

  """The URL of the file."""
  url: URL
}

"""The input fields used to specify a geographical location."""
input GeoCoordinateInput {
  """The coordinate's latitude value."""
  latitude: Float!

  """The coordinate's longitude value."""
  longitude: Float!
}

"A string containing HTML code. Refer to the [HTML spec](https://html.spec.whatwg.org/#elements-3) for a\ncomplete list of HTML elements.\n\nExample value: `\"<p>Grey cotton knit sweater.</p>\"`\n"
scalar HTML

"""
Represents information about the metafields associated to the specified resource.
"""
interface HasMetafields {
  """
  A [custom field](https://shopify.dev/docs/apps/build/custom-data), including its `namespace` and `key`, that's associated with a Shopify resource for the purposes of adding and storing additional information.
  """
  metafield(
    """
    The container the metafield belongs to. If omitted, the app-reserved namespace will be used.
    """
    namespace: String

    """The identifier for the metafield."""
    key: String!
  ): Metafield

  """
  A list of [custom fields](/docs/apps/build/custom-data) that a merchant associates with a Shopify resource.
  """
  metafields(
    """
    The list of metafields to retrieve by namespace and key.
    
    The input must not contain more than `250` values.
    """
    identifiers: [HasMetafieldsIdentifier!]!
  ): [Metafield]!
}

"""
The input fields to identify a metafield on an owner resource by namespace and key.
"""
input HasMetafieldsIdentifier {
  """
  The container the metafield belongs to. If omitted, the app-reserved namespace will be used.
  """
  namespace: String

  """The identifier for the metafield."""
  key: String!
}

"""An ISO 8601-encoded datetime"""
scalar ISO8601DateTime

"""Represents an image resource."""
type Image {
  """A word or phrase to share the nature or contents of an image."""
  altText: String

  """
  The original height of the image in pixels. Returns `null` if the image isn't hosted by Shopify.
  """
  height: Int

  """A unique ID for the image."""
  id: ID

  "The location of the original image as a URL.\n\nIf there are any existing transformations in the original source URL, they will remain and not be stripped.\n"
  originalSrc: URL! @deprecated(reason: "Use `url` instead.")

  """The location of the image as a URL."""
  src: URL! @deprecated(reason: "Use `url` instead.")

  "The location of the transformed image as a URL.\n\nAll transformation arguments are considered \"best-effort\". If they can be applied to an image, they will be.\nOtherwise any transformations which an image type doesn't support will be ignored.\n"
  transformedSrc(
    """Image width in pixels between 1 and 5760."""
    maxWidth: Int

    """Image height in pixels between 1 and 5760."""
    maxHeight: Int

    """Crops the image according to the specified region."""
    crop: CropRegion

    """
    Image size multiplier for high-resolution retina displays. Must be between 1 and 3.
    """
    scale: Int = 1

    """
    Best effort conversion of image into content type (SVG -> PNG, Anything -> JPG, Anything -> WEBP are supported).
    """
    preferredContentType: ImageContentType
  ): URL! @deprecated(reason: "Use `url(transform:)` instead")

  "The location of the image as a URL.\n\nIf no transform options are specified, then the original image will be preserved including any pre-applied transforms.\n\nAll transformation options are considered \"best-effort\". Any transformation that the original image type doesn't support will be ignored.\n\nIf you need multiple variations of the same image, then you can use [GraphQL aliases](https://graphql.org/learn/queries/#aliases).\n"
  url(
    """A set of options to transform the original image."""
    transform: ImageTransformInput
  ): URL!

  """
  The original width of the image in pixels. Returns `null` if the image isn't hosted by Shopify.
  """
  width: Int
}

"An auto-generated type for paginating through multiple Images.\n"
type ImageConnection {
  """A list of edges."""
  edges: [ImageEdge!]!

  """A list of the nodes contained in ImageEdge."""
  nodes: [Image!]!

  """Information to aid in pagination."""
  pageInfo: PageInfo!
}

"""List of supported image content types."""
enum ImageContentType {
  """A PNG image."""
  PNG

  """A JPG image."""
  JPG

  """A WEBP image."""
  WEBP
}

"An auto-generated type which holds one Image and a cursor during pagination.\n"
type ImageEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of ImageEdge."""
  node: Image!
}

"The available options for transforming an image.\n\nAll transformation options are considered best effort. Any transformation that\nthe original image type doesn't support will be ignored.\n"
input ImageTransformInput {
  "The region of the image to remain after cropping.\nMust be used in conjunction with the `maxWidth` and/or `maxHeight` fields,\nwhere the `maxWidth` and `maxHeight` aren't equal.\nThe `crop` argument should coincide with the smaller value. A smaller `maxWidth` indicates a `LEFT` or `RIGHT` crop, while\na smaller `maxHeight` indicates a `TOP` or `BOTTOM` crop. For example, `{\nmaxWidth: 5, maxHeight: 10, crop: LEFT }` will result\nin an image with a width of 5 and height of 10, where the right side of the image is removed.\n"
  crop: CropRegion

  "Image width in pixels between 1 and 5760.\n"
  maxWidth: Int

  "Image height in pixels between 1 and 5760.\n"
  maxHeight: Int

  "Image size multiplier for high-resolution retina displays. Must be within 1..3.\n"
  scale: Int = 1

  "Convert the source image into the preferred content type.\nSupported conversions: `.svg` to `.png`, any file type to `.jpg`, and any file type to `.webp`.\n"
  preferredContentType: ImageContentType
}

"""
Provide details about the contexts influenced by the @inContext directive on a field.
"""
type InContextAnnotation {
  description: String!
  type: InContextAnnotationType!
}

"""
This gives information about the type of context that impacts a field. For example, for a query with @inContext(language: "EN"), the type would point to the name: LanguageCode and kind: ENUM.
"""
type InContextAnnotationType {
  kind: String!
  name: String!
}

"A [JSON](https://www.json.org/json-en.html) object.\n\nExample value:\n`{\n  \"product\": {\n    \"id\": \"gid://shopify/Product/1346443542550\",\n    \"title\": \"White T-shirt\",\n    \"options\": [{\n      \"name\": \"Size\",\n      \"values\": [\"M\", \"L\"]\n    }]\n  }\n}`\n"
scalar JSON

"""A language."""
type Language {
  """
  The name of the language in the language itself. If the language uses capitalization, it is capitalized for a mid-sentence position.
  """
  endonymName: String!

  """The ISO code."""
  isoCode: LanguageCode!

  """The name of the language in the current language."""
  name: String!
}

"""Language codes supported by Shopify."""
enum LanguageCode {
  """Afrikaans."""
  AF

  """Akan."""
  AK

  """Amharic."""
  AM

  """Arabic."""
  AR

  """Assamese."""
  AS

  """Azerbaijani."""
  AZ

  """Belarusian."""
  BE

  """Bulgarian."""
  BG

  """Bambara."""
  BM

  """Bangla."""
  BN

  """Tibetan."""
  BO

  """Breton."""
  BR

  """Bosnian."""
  BS

  """Catalan."""
  CA

  """Chechen."""
  CE

  """Central Kurdish."""
  CKB

  """Czech."""
  CS

  """Welsh."""
  CY

  """Danish."""
  DA

  """German."""
  DE

  """Dzongkha."""
  DZ

  """Ewe."""
  EE

  """Greek."""
  EL

  """English."""
  EN

  """Esperanto."""
  EO

  """Spanish."""
  ES

  """Estonian."""
  ET

  """Basque."""
  EU

  """Persian."""
  FA

  """Fulah."""
  FF

  """Finnish."""
  FI

  """Filipino."""
  FIL

  """Faroese."""
  FO

  """French."""
  FR

  """Western Frisian."""
  FY

  """Irish."""
  GA

  """Scottish Gaelic."""
  GD

  """Galician."""
  GL

  """Gujarati."""
  GU

  """Manx."""
  GV

  """Hausa."""
  HA

  """Hebrew."""
  HE

  """Hindi."""
  HI

  """Croatian."""
  HR

  """Hungarian."""
  HU

  """Armenian."""
  HY

  """Interlingua."""
  IA

  """Indonesian."""
  ID

  """Igbo."""
  IG

  """Sichuan Yi."""
  II

  """Icelandic."""
  IS

  """Italian."""
  IT

  """Japanese."""
  JA

  """Javanese."""
  JV

  """Georgian."""
  KA

  """Kikuyu."""
  KI

  """Kazakh."""
  KK

  """Kalaallisut."""
  KL

  """Khmer."""
  KM

  """Kannada."""
  KN

  """Korean."""
  KO

  """Kashmiri."""
  KS

  """Kurdish."""
  KU

  """Cornish."""
  KW

  """Kyrgyz."""
  KY

  """Luxembourgish."""
  LB

  """Ganda."""
  LG

  """Lingala."""
  LN

  """Lao."""
  LO

  """Lithuanian."""
  LT

  """Luba-Katanga."""
  LU

  """Latvian."""
  LV

  """Malagasy."""
  MG

  """Māori."""
  MI

  """Macedonian."""
  MK

  """Malayalam."""
  ML

  """Mongolian."""
  MN

  """Marathi."""
  MR

  """Malay."""
  MS

  """Maltese."""
  MT

  """Burmese."""
  MY

  """Norwegian (Bokmål)."""
  NB

  """North Ndebele."""
  ND

  """Nepali."""
  NE

  """Dutch."""
  NL

  """Norwegian Nynorsk."""
  NN

  """Norwegian."""
  NO

  """Oromo."""
  OM

  """Odia."""
  OR

  """Ossetic."""
  OS

  """Punjabi."""
  PA

  """Polish."""
  PL

  """Pashto."""
  PS

  """Portuguese (Brazil)."""
  PT_BR

  """Portuguese (Portugal)."""
  PT_PT

  """Quechua."""
  QU

  """Romansh."""
  RM

  """Rundi."""
  RN

  """Romanian."""
  RO

  """Russian."""
  RU

  """Kinyarwanda."""
  RW

  """Sanskrit."""
  SA

  """Sardinian."""
  SC

  """Sindhi."""
  SD

  """Northern Sami."""
  SE

  """Sango."""
  SG

  """Sinhala."""
  SI

  """Slovak."""
  SK

  """Slovenian."""
  SL

  """Shona."""
  SN

  """Somali."""
  SO

  """Albanian."""
  SQ

  """Serbian."""
  SR

  """Sundanese."""
  SU

  """Swedish."""
  SV

  """Swahili."""
  SW

  """Tamil."""
  TA

  """Telugu."""
  TE

  """Tajik."""
  TG

  """Thai."""
  TH

  """Tigrinya."""
  TI

  """Turkmen."""
  TK

  """Tongan."""
  TO

  """Turkish."""
  TR

  """Tatar."""
  TT

  """Uyghur."""
  UG

  """Ukrainian."""
  UK

  """Urdu."""
  UR

  """Uzbek."""
  UZ

  """Vietnamese."""
  VI

  """Wolof."""
  WO

  """Xhosa."""
  XH

  """Yiddish."""
  YI

  """Yoruba."""
  YO

  """Chinese (Simplified)."""
  ZH_CN

  """Chinese (Traditional)."""
  ZH_TW

  """Zulu."""
  ZU

  """Chinese."""
  ZH

  """Portuguese."""
  PT

  """Church Slavic."""
  CU

  """Volapük."""
  VO

  """Latin."""
  LA

  """Serbo-Croatian."""
  SH

  """Moldavian."""
  MO
}

"""Information about the localized experiences configured for the shop."""
type Localization {
  """The list of countries with enabled localized experiences."""
  availableCountries: [Country!]!

  """The list of languages available for the active country."""
  availableLanguages: [Language!]!

  """
  The country of the active localized experience. Use the `@inContext` directive to change this value.
  """
  country: Country!

  """
  The language of the active localized experience. Use the `@inContext` directive to change this value.
  """
  language: Language!

  """
  The market including the country of the active localized experience. Use the `@inContext` directive to change this value.
  """
  market: Market!
}

"""Represents a location where product inventory is held."""
type Location implements HasMetafields & Node {
  """The address of the location."""
  address: LocationAddress!

  """A globally-unique ID."""
  id: ID!

  """
  A [custom field](https://shopify.dev/docs/apps/build/custom-data), including its `namespace` and `key`, that's associated with a Shopify resource for the purposes of adding and storing additional information.
  """
  metafield(
    """
    The container the metafield belongs to. If omitted, the app-reserved namespace will be used.
    """
    namespace: String

    """The identifier for the metafield."""
    key: String!
  ): Metafield

  """
  A list of [custom fields](/docs/apps/build/custom-data) that a merchant associates with a Shopify resource.
  """
  metafields(
    """
    The list of metafields to retrieve by namespace and key.
    
    The input must not contain more than `250` values.
    """
    identifiers: [HasMetafieldsIdentifier!]!
  ): [Metafield]!

  """The name of the location."""
  name: String!
}

"Represents the address of a location.\n"
type LocationAddress {
  """The first line of the address for the location."""
  address1: String

  """The second line of the address for the location."""
  address2: String

  """The city of the location."""
  city: String

  """The country of the location."""
  country: String

  """The country code of the location."""
  countryCode: String

  """A formatted version of the address for the location."""
  formatted: [String!]!

  """The latitude coordinates of the location."""
  latitude: Float

  """The longitude coordinates of the location."""
  longitude: Float

  """The phone number of the location."""
  phone: String

  """The province of the location."""
  province: String

  "The code for the province, state, or district of the address of the location.\n"
  provinceCode: String

  """The ZIP code of the location."""
  zip: String
}

"An auto-generated type for paginating through multiple Locations.\n"
type LocationConnection {
  """A list of edges."""
  edges: [LocationEdge!]!

  """A list of the nodes contained in LocationEdge."""
  nodes: [Location!]!

  """Information to aid in pagination."""
  pageInfo: PageInfo!
}

"An auto-generated type which holds one Location and a cursor during pagination.\n"
type LocationEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of LocationEdge."""
  node: Location!
}

"""The set of valid sort keys for the Location query."""
enum LocationSortKeys {
  """Sort by the `id` value."""
  ID

  """Sort by the `name` value."""
  NAME

  """Sort by the `city` value."""
  CITY

  """Sort by the `distance` value."""
  DISTANCE
}

"""Represents a mailing address for customers and shipping."""
type MailingAddress implements Node {
  """
  The first line of the address. Typically the street address or PO Box number.
  """
  address1: String

  "The second line of the address. Typically the number of the apartment, suite, or unit.\n"
  address2: String

  """The name of the city, district, village, or town."""
  city: String

  """The name of the customer's company or organization."""
  company: String

  """The name of the country."""
  country: String

  "The two-letter code for the country of the address.\n\nFor example, US.\n"
  countryCode: String @deprecated(reason: "Use `countryCodeV2` instead.")

  "The two-letter code for the country of the address.\n\nFor example, US.\n"
  countryCodeV2: CountryCode

  """The first name of the customer."""
  firstName: String

  """
  A formatted version of the address, customized by the provided arguments.
  """
  formatted(
    """Whether to include the customer's name in the formatted address."""
    withName: Boolean = false

    """Whether to include the customer's company in the formatted address."""
    withCompany: Boolean = true
  ): [String!]!

  """A comma-separated list of the values for city, province, and country."""
  formattedArea: String

  """A globally-unique ID."""
  id: ID!

  """The last name of the customer."""
  lastName: String

  """The latitude coordinate of the customer address."""
  latitude: Float

  """The longitude coordinate of the customer address."""
  longitude: Float

  """The full name of the customer, based on firstName and lastName."""
  name: String

  "A unique phone number for the customer.\n\nFormatted using E.164 standard. For example, _+16135551111_.\n"
  phone: String

  """The region of the address, such as the province, state, or district."""
  province: String

  "The alphanumeric code for the region.\n\nFor example, ON.\n"
  provinceCode: String

  """The zip or postal code of the address."""
  zip: String
}

"An auto-generated type for paginating through multiple MailingAddresses.\n"
type MailingAddressConnection {
  """A list of edges."""
  edges: [MailingAddressEdge!]!

  """A list of the nodes contained in MailingAddressEdge."""
  nodes: [MailingAddress!]!

  """Information to aid in pagination."""
  pageInfo: PageInfo!
}

"An auto-generated type which holds one MailingAddress and a cursor during pagination.\n"
type MailingAddressEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of MailingAddressEdge."""
  node: MailingAddress!
}

"""The input fields to create or update a mailing address."""
input MailingAddressInput {
  "The first line of the address. Typically the street address or PO Box number.\n"
  address1: String

  "The second line of the address. Typically the number of the apartment, suite, or unit.\n"
  address2: String

  "The name of the city, district, village, or town.\n"
  city: String

  "The name of the customer's company or organization.\n"
  company: String

  """The name of the country."""
  country: String

  """The first name of the customer."""
  firstName: String

  """The last name of the customer."""
  lastName: String

  "A unique phone number for the customer.\n\nFormatted using E.164 standard. For example, _+16135551111_.\n"
  phone: String

  """The region of the address, such as the province, state, or district."""
  province: String

  """The zip or postal code of the address."""
  zip: String
}

"Manual discount applications capture the intentions of a discount that was manually created.\n"
type ManualDiscountApplication implements DiscountApplication {
  """
  The method by which the discount's value is allocated to its entitled items.
  """
  allocationMethod: DiscountApplicationAllocationMethod!

  """The description of the application."""
  description: String

  """Which lines of targetType that the discount is allocated over."""
  targetSelection: DiscountApplicationTargetSelection!

  """The type of line that the discount is applicable towards."""
  targetType: DiscountApplicationTargetType!

  """The title of the application."""
  title: String!

  """The value of the discount application."""
  value: PricingValue!
}

"""
A group of one or more regions of the world that a merchant is targeting for sales. To learn more about markets, refer to [the Shopify Markets conceptual overview](/docs/apps/markets).
"""
type Market implements HasMetafields & Node {
  "A human-readable unique string for the market automatically generated from its title.\n"
  handle: String!

  """A globally-unique ID."""
  id: ID!

  """
  A [custom field](https://shopify.dev/docs/apps/build/custom-data), including its `namespace` and `key`, that's associated with a Shopify resource for the purposes of adding and storing additional information.
  """
  metafield(
    """
    The container the metafield belongs to. If omitted, the app-reserved namespace will be used.
    """
    namespace: String

    """The identifier for the metafield."""
    key: String!
  ): Metafield

  """
  A list of [custom fields](/docs/apps/build/custom-data) that a merchant associates with a Shopify resource.
  """
  metafields(
    """
    The list of metafields to retrieve by namespace and key.
    
    The input must not contain more than `250` values.
    """
    identifiers: [HasMetafieldsIdentifier!]!
  ): [Metafield]!
}

"""Represents a media interface."""
interface Media {
  """A word or phrase to share the nature or contents of a media."""
  alt: String

  """A globally-unique ID."""
  id: ID!

  """The media content type."""
  mediaContentType: MediaContentType!

  """The presentation for a media."""
  presentation: MediaPresentation

  """The preview image for the media."""
  previewImage: Image
}

"An auto-generated type for paginating through multiple Media.\n"
type MediaConnection {
  """A list of edges."""
  edges: [MediaEdge!]!

  """A list of the nodes contained in MediaEdge."""
  nodes: [Media!]!

  """Information to aid in pagination."""
  pageInfo: PageInfo!
}

"""The possible content types for a media object."""
enum MediaContentType {
  """An externally hosted video."""
  EXTERNAL_VIDEO

  """A Shopify hosted image."""
  IMAGE

  """A 3d model."""
  MODEL_3D

  """A Shopify hosted video."""
  VIDEO
}

"An auto-generated type which holds one Media and a cursor during pagination.\n"
type MediaEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of MediaEdge."""
  node: Media!
}

"""Host for a Media Resource."""
enum MediaHost {
  """Host for YouTube embedded videos."""
  YOUTUBE

  """Host for Vimeo embedded videos."""
  VIMEO
}

"""Represents a Shopify hosted image."""
type MediaImage implements Media & Node {
  """A word or phrase to share the nature or contents of a media."""
  alt: String

  """A globally-unique ID."""
  id: ID!

  """The image for the media."""
  image: Image

  """The media content type."""
  mediaContentType: MediaContentType!

  """The presentation for a media."""
  presentation: MediaPresentation

  """The preview image for the media."""
  previewImage: Image
}

"""A media presentation."""
type MediaPresentation implements Node {
  """A JSON object representing a presentation view."""
  asJson(
    """The format to transform the settings."""
    format: MediaPresentationFormat!
  ): JSON

  """A globally-unique ID."""
  id: ID!
}

"""The possible formats for a media presentation."""
enum MediaPresentationFormat {
  """A model viewer presentation."""
  MODEL_VIEWER

  """A media image presentation."""
  IMAGE
}

"A [navigation menu](https://help.shopify.com/manual/online-store/menus-and-links) representing a hierarchy\nof hyperlinks (items).\n"
type Menu implements Node {
  """The menu's handle."""
  handle: String!

  """A globally-unique ID."""
  id: ID!

  """The menu's child items."""
  items: [MenuItem!]!

  """The count of items on the menu."""
  itemsCount: Int!

  """The menu's title."""
  title: String!
}

"""A menu item within a parent menu."""
type MenuItem implements Node {
  """A globally-unique ID."""
  id: ID!

  """The menu item's child items."""
  items: [MenuItem!]!

  """The linked resource."""
  resource: MenuItemResource

  """The ID of the linked resource."""
  resourceId: ID

  """The menu item's tags to filter a collection."""
  tags: [String!]!

  """The menu item's title."""
  title: String!

  """The menu item's type."""
  type: MenuItemType!

  """The menu item's URL."""
  url: URL
}

"The list of possible resources a `MenuItem` can reference.\n"
union MenuItemResource = Article | Blog | Collection | Metaobject | Page | Product | ShopPolicy

"""A menu item type."""
enum MenuItemType {
  """A frontpage link."""
  FRONTPAGE

  """A collection link."""
  COLLECTION

  """A collection link."""
  COLLECTIONS

  """A product link."""
  PRODUCT

  """A catalog link."""
  CATALOG

  """A page link."""
  PAGE

  """A blog link."""
  BLOG

  """An article link."""
  ARTICLE

  """A search link."""
  SEARCH

  """A shop policy link."""
  SHOP_POLICY

  """An http link."""
  HTTP

  """A metaobject page link."""
  METAOBJECT

  """A customer account page link."""
  CUSTOMER_ACCOUNT_PAGE
}

"""The merchandise to be purchased at checkout."""
union Merchandise = ProductVariant

"Metafields represent custom metadata attached to a resource. Metafields can be sorted into namespaces and are\ncomprised of keys, values, and value types.\n"
type Metafield implements Node {
  """The date and time when the storefront metafield was created."""
  createdAt: DateTime!

  """The description of a metafield."""
  description: String

  """A globally-unique ID."""
  id: ID!

  """The unique identifier for the metafield within its namespace."""
  key: String!

  """
  The container for a group of metafields that the metafield is associated with.
  """
  namespace: String!

  """The type of resource that the metafield is attached to."""
  parentResource: MetafieldParentResource!

  """
  Returns a reference object if the metafield's type is a resource reference.
  """
  reference: MetafieldReference

  """
  A list of reference objects if the metafield's type is a resource reference list.
  """
  references(
    """Returns up to the first `n` elements from the list."""
    first: Int

    """Returns the elements that come after the specified cursor."""
    after: String

    """Returns up to the last `n` elements from the list."""
    last: Int

    """Returns the elements that come before the specified cursor."""
    before: String
  ): MetafieldReferenceConnection

  "The type name of the metafield.\nRefer to the list of [supported types](https://shopify.dev/apps/metafields/definitions/types).\n"
  type: String!

  """The date and time when the metafield was last updated."""
  updatedAt: DateTime!

  """
  The data stored in the metafield. Always stored as a string, regardless of the metafield's type.
  """
  value: String!
}

"""
Possible error codes that can be returned by `MetafieldDeleteUserError`.
"""
enum MetafieldDeleteErrorCode {
  """The owner ID is invalid."""
  INVALID_OWNER

  """Metafield not found."""
  METAFIELD_DOES_NOT_EXIST
}

"""An error that occurs during the execution of cart metafield deletion."""
type MetafieldDeleteUserError implements DisplayableError {
  """The error code."""
  code: MetafieldDeleteErrorCode

  """The path to the input field that caused the error."""
  field: [String!]

  """The error message."""
  message: String!
}

"A filter used to view a subset of products in a collection matching a specific metafield value.\n\nOnly the following metafield types are currently supported:\n- `number_integer`\n- `number_decimal`\n- `single_line_text_field`\n- `boolean` as of 2022-04.\n"
input MetafieldFilter {
  """The namespace of the metafield to filter on."""
  namespace: String!

  """The key of the metafield to filter on."""
  key: String!

  """The value of the metafield."""
  value: String!
}

"""A resource that the metafield belongs to."""
union MetafieldParentResource = Article | Blog | Cart | Collection | Company | CompanyLocation | Customer | Location | Market | Order | Page | Product | ProductVariant | SellingPlan | Shop

"Returns the resource which is being referred to by a metafield.\n"
union MetafieldReference = Collection | GenericFile | MediaImage | Metaobject | Model3d | Page | Product | ProductVariant | Video

"An auto-generated type for paginating through multiple MetafieldReferences.\n"
type MetafieldReferenceConnection {
  """A list of edges."""
  edges: [MetafieldReferenceEdge!]!

  """A list of the nodes contained in MetafieldReferenceEdge."""
  nodes: [MetafieldReference!]!

  """Information to aid in pagination."""
  pageInfo: PageInfo!
}

"An auto-generated type which holds one MetafieldReference and a cursor during pagination.\n"
type MetafieldReferenceEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of MetafieldReferenceEdge."""
  node: MetafieldReference!
}

"""An error that occurs during the execution of `MetafieldsSet`."""
type MetafieldsSetUserError implements DisplayableError {
  """The error code."""
  code: MetafieldsSetUserErrorCode

  """The index of the array element that's causing the error."""
  elementIndex: Int

  """The path to the input field that caused the error."""
  field: [String!]

  """The error message."""
  message: String!
}

"""Possible error codes that can be returned by `MetafieldsSetUserError`."""
enum MetafieldsSetUserErrorCode {
  """The input value is blank."""
  BLANK

  """The input value isn't included in the list."""
  INCLUSION

  """
  The input value should be less than or equal to the maximum value allowed.
  """
  LESS_THAN_OR_EQUAL_TO

  """The input value needs to be blank."""
  PRESENT

  """The input value is too short."""
  TOO_SHORT

  """The input value is too long."""
  TOO_LONG

  """The owner ID is invalid."""
  INVALID_OWNER

  """The value is invalid for metafield type or for definition options."""
  INVALID_VALUE

  """The type is invalid."""
  INVALID_TYPE
}

"""An instance of a user-defined model based on a MetaobjectDefinition."""
type Metaobject implements Node & OnlineStorePublishable {
  """Accesses a field of the object by key."""
  field(
    """The key of the field."""
    key: String!
  ): MetaobjectField

  "All object fields with defined values.\nOmitted object keys can be assumed null, and no guarantees are made about field order.\n"
  fields: [MetaobjectField!]!

  """The unique handle of the metaobject. Useful as a custom ID."""
  handle: String!

  """A globally-unique ID."""
  id: ID!

  """
  The URL used for viewing the metaobject on the shop's Online Store. Returns `null` if the metaobject definition doesn't have the `online_store` capability.
  """
  onlineStoreUrl: URL

  "The metaobject's SEO information. Returns `null` if the metaobject definition\ndoesn't have the `renderable` capability.\n"
  seo: MetaobjectSEO

  """
  The type of the metaobject. Defines the namespace of its associated metafields.
  """
  type: String!

  """The date and time when the metaobject was last updated."""
  updatedAt: DateTime!
}

"An auto-generated type for paginating through multiple Metaobjects.\n"
type MetaobjectConnection {
  """A list of edges."""
  edges: [MetaobjectEdge!]!

  """A list of the nodes contained in MetaobjectEdge."""
  nodes: [Metaobject!]!

  """Information to aid in pagination."""
  pageInfo: PageInfo!
}

"An auto-generated type which holds one Metaobject and a cursor during pagination.\n"
type MetaobjectEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of MetaobjectEdge."""
  node: Metaobject!
}

"""Provides the value of a Metaobject field."""
type MetaobjectField {
  """The field key."""
  key: String!

  """A referenced object if the field type is a resource reference."""
  reference: MetafieldReference

  """
  A list of referenced objects if the field type is a resource reference list.
  """
  references(
    """Returns up to the first `n` elements from the list."""
    first: Int

    """Returns the elements that come after the specified cursor."""
    after: String

    """Returns up to the last `n` elements from the list."""
    last: Int

    """Returns the elements that come before the specified cursor."""
    before: String
  ): MetafieldReferenceConnection

  "The type name of the field.\nSee the list of [supported types](https://shopify.dev/apps/metafields/definitions/types).\n"
  type: String!

  """The field value."""
  value: String
}

"""The input fields used to retrieve a metaobject by handle."""
input MetaobjectHandleInput {
  """The handle of the metaobject."""
  handle: String!

  """The type of the metaobject."""
  type: String!
}

"""SEO information for a metaobject."""
type MetaobjectSEO {
  """The meta description."""
  description: MetaobjectField

  """The SEO title."""
  title: MetaobjectField
}

"""Represents a Shopify hosted 3D model."""
type Model3d implements Media & Node {
  """A word or phrase to share the nature or contents of a media."""
  alt: String

  """A globally-unique ID."""
  id: ID!

  """The media content type."""
  mediaContentType: MediaContentType!

  """The presentation for a media."""
  presentation: MediaPresentation

  """The preview image for the media."""
  previewImage: Image

  """The sources for a 3d model."""
  sources: [Model3dSource!]!
}

"""Represents a source for a Shopify hosted 3d model."""
type Model3dSource {
  """The filesize of the 3d model."""
  filesize: Int!

  """The format of the 3d model."""
  format: String!

  """The MIME type of the 3d model."""
  mimeType: String!

  """The URL of the 3d model."""
  url: String!
}

"""The input fields for a monetary value with currency."""
input MoneyInput {
  """Decimal money amount."""
  amount: Decimal!

  """Currency of the money."""
  currencyCode: CurrencyCode!
}

"A monetary value with currency.\n"
type MoneyV2 {
  """Decimal money amount."""
  amount: Decimal!

  """Currency of the money."""
  currencyCode: CurrencyCode!
}

"""
The schema’s entry-point for mutations. This acts as the public, top-level API from which all mutation queries must start.
"""
type Mutation {
  """Updates the attributes on a cart."""
  cartAttributesUpdate(
    """
    An array of key-value pairs that contains additional information about the cart.
    
    The input must not contain more than `250` values.
    """
    attributes: [AttributeInput!]!

    """The ID of the cart."""
    cartId: ID!
  ): CartAttributesUpdatePayload

  """Updates the billing address on the cart."""
  cartBillingAddressUpdate(
    """The ID of the cart."""
    cartId: ID!

    """The customer's billing address."""
    billingAddress: MailingAddressInput
  ): CartBillingAddressUpdatePayload

  "Updates customer information associated with a cart.\nBuyer identity is used to determine\n[international pricing](https://shopify.dev/custom-storefronts/internationalization/international-pricing)\nand should match the customer's shipping address.\n"
  cartBuyerIdentityUpdate(
    """The ID of the cart."""
    cartId: ID!

    "The customer associated with the cart. Used to determine\n[international pricing](https://shopify.dev/custom-storefronts/internationalization/international-pricing).\nBuyer identity should match the customer's shipping address.\n"
    buyerIdentity: CartBuyerIdentityInput!
  ): CartBuyerIdentityUpdatePayload

  """Creates a new cart."""
  cartCreate(
    """The fields used to create a cart."""
    input: CartInput
  ): CartCreatePayload

  """Updates the discount codes applied to the cart."""
  cartDiscountCodesUpdate(
    """The ID of the cart."""
    cartId: ID!

    """
    The case-insensitive discount codes that the customer added at checkout.
    
    The input must not contain more than `250` values.
    """
    discountCodes: [String!]
  ): CartDiscountCodesUpdatePayload

  """Updates the gift card codes applied to the cart."""
  cartGiftCardCodesUpdate(
    """The ID of the cart."""
    cartId: ID!

    """
    The case-insensitive gift card codes.
    
    The input must not contain more than `250` values.
    """
    giftCardCodes: [String!]!
  ): CartGiftCardCodesUpdatePayload

  """Adds a merchandise line to the cart."""
  cartLinesAdd(
    """The ID of the cart."""
    cartId: ID!

    """
    A list of merchandise lines to add to the cart.
    
    The input must not contain more than `250` values.
    """
    lines: [CartLineInput!]!
  ): CartLinesAddPayload

  """Removes one or more merchandise lines from the cart."""
  cartLinesRemove(
    """The ID of the cart."""
    cartId: ID!

    """
    The merchandise line IDs to remove.
    
    The input must not contain more than `250` values.
    """
    lineIds: [ID!]!
  ): CartLinesRemovePayload

  """Updates one or more merchandise lines on a cart."""
  cartLinesUpdate(
    """The ID of the cart."""
    cartId: ID!

    """
    The merchandise lines to update.
    
    The input must not contain more than `250` values.
    """
    lines: [CartLineUpdateInput!]!
  ): CartLinesUpdatePayload

  """Deletes a cart metafield."""
  cartMetafieldDelete(
    """The input fields used to delete a cart metafield."""
    input: CartMetafieldDeleteInput!
  ): CartMetafieldDeletePayload

  "Sets cart metafield values. Cart metafield values will be set regardless if they were previously created or not.\n\nAllows a maximum of 25 cart metafields to be set at a time.\n"
  cartMetafieldsSet(
    """
    The list of Cart metafield values to set. Maximum of 25.
    
    The input must not contain more than `250` values.
    """
    metafields: [CartMetafieldsSetInput!]!
  ): CartMetafieldsSetPayload

  """Updates the note on the cart."""
  cartNoteUpdate(
    """The ID of the cart."""
    cartId: ID!

    """The note on the cart."""
    note: String!
  ): CartNoteUpdatePayload

  """Update the customer's payment method that will be used to checkout."""
  cartPaymentUpdate(
    """The ID of the cart."""
    cartId: ID!

    """The payment information for the cart that will be used at checkout."""
    payment: CartPaymentInput!
  ): CartPaymentUpdatePayload

  """Update the selected delivery options for a delivery group."""
  cartSelectedDeliveryOptionsUpdate(
    """The ID of the cart."""
    cartId: ID!

    """
    The selected delivery options.
    
    The input must not contain more than `250` values.
    """
    selectedDeliveryOptions: [CartSelectedDeliveryOptionInput!]!
  ): CartSelectedDeliveryOptionsUpdatePayload

  """Submit the cart for checkout completion."""
  cartSubmitForCompletion(
    """The ID of the cart."""
    cartId: ID!

    "The attemptToken is used to guarantee an idempotent result.\nIf more than one call uses the same attemptToken within a short period of time, only one will be accepted.\n"
    attemptToken: String!
  ): CartSubmitForCompletionPayload

  "Creates a customer access token.\nThe customer access token is required to modify the customer object in any way.\n"
  customerAccessTokenCreate(
    """The fields used to create a customer access token."""
    input: CustomerAccessTokenCreateInput!
  ): CustomerAccessTokenCreatePayload

  "Creates a customer access token using a\n[multipass token](https://shopify.dev/api/multipass) instead of email and\npassword. A customer record is created if the customer doesn't exist. If a customer\nrecord already exists but the record is disabled, then the customer record is enabled.\n"
  customerAccessTokenCreateWithMultipass(
    """
    A valid [multipass token](https://shopify.dev/api/multipass) to be authenticated.
    """
    multipassToken: String!
  ): CustomerAccessTokenCreateWithMultipassPayload

  """Permanently destroys a customer access token."""
  customerAccessTokenDelete(
    """The access token used to identify the customer."""
    customerAccessToken: String!
  ): CustomerAccessTokenDeletePayload

  "Renews a customer access token.\n\nAccess token renewal must happen *before* a token expires.\nIf a token has already expired, a new one should be created instead via `customerAccessTokenCreate`.\n"
  customerAccessTokenRenew(
    """The access token used to identify the customer."""
    customerAccessToken: String!
  ): CustomerAccessTokenRenewPayload

  """Activates a customer."""
  customerActivate(
    """Specifies the customer to activate."""
    id: ID!

    """The fields used to activate a customer."""
    input: CustomerActivateInput!
  ): CustomerActivatePayload

  """
  Activates a customer with the activation url received from `customerCreate`.
  """
  customerActivateByUrl(
    """The customer activation URL."""
    activationUrl: URL!

    """A new password set during activation."""
    password: String!
  ): CustomerActivateByUrlPayload

  """Creates a new address for a customer."""
  customerAddressCreate(
    """The access token used to identify the customer."""
    customerAccessToken: String!

    """The customer mailing address to create."""
    address: MailingAddressInput!
  ): CustomerAddressCreatePayload

  """Permanently deletes the address of an existing customer."""
  customerAddressDelete(
    """Specifies the address to delete."""
    id: ID!

    """The access token used to identify the customer."""
    customerAccessToken: String!
  ): CustomerAddressDeletePayload

  """Updates the address of an existing customer."""
  customerAddressUpdate(
    """The access token used to identify the customer."""
    customerAccessToken: String!

    """Specifies the customer address to update."""
    id: ID!

    """The customer’s mailing address."""
    address: MailingAddressInput!
  ): CustomerAddressUpdatePayload

  """Creates a new customer."""
  customerCreate(
    """The fields used to create a new customer."""
    input: CustomerCreateInput!
  ): CustomerCreatePayload

  """Updates the default address of an existing customer."""
  customerDefaultAddressUpdate(
    """The access token used to identify the customer."""
    customerAccessToken: String!

    """ID of the address to set as the new default for the customer."""
    addressId: ID!
  ): CustomerDefaultAddressUpdatePayload

  "Sends a reset password email to the customer. The reset password\nemail contains a reset password URL and token that you can pass to\nthe [`customerResetByUrl`](https://shopify.dev/api/storefront/latest/mutations/customerResetByUrl) or\n[`customerReset`](https://shopify.dev/api/storefront/latest/mutations/customerReset) mutation to reset the\ncustomer password.\n\nThis mutation is throttled by IP. With private access,\nyou can provide a [`Shopify-Storefront-Buyer-IP`](https://shopify.dev/api/usage/authentication#optional-ip-header) instead of the request IP.\nThe header is case-sensitive and must be sent as `Shopify-Storefront-Buyer-IP`.\n\nMake sure that the value provided to `Shopify-Storefront-Buyer-IP` is trusted. Unthrottled access to this\nmutation presents a security risk.\n"
  customerRecover(
    """The email address of the customer to recover."""
    email: String!
  ): CustomerRecoverPayload

  "\"Resets a customer’s password with the token received from a reset password email. You can send a reset password email with the [`customerRecover`](https://shopify.dev/api/storefront/latest/mutations/customerRecover) mutation.\"\n"
  customerReset(
    """Specifies the customer to reset."""
    id: ID!

    """The fields used to reset a customer’s password."""
    input: CustomerResetInput!
  ): CustomerResetPayload

  "\"Resets a customer’s password with the reset password URL received from a reset password email. You can send a reset password email with the [`customerRecover`](https://shopify.dev/api/storefront/latest/mutations/customerRecover) mutation.\"\n"
  customerResetByUrl(
    """The customer's reset password url."""
    resetUrl: URL!

    """New password that will be set as part of the reset password process."""
    password: String!
  ): CustomerResetByUrlPayload

  """Updates an existing customer."""
  customerUpdate(
    """The access token used to identify the customer."""
    customerAccessToken: String!

    """The customer object input."""
    customer: CustomerUpdateInput!
  ): CustomerUpdatePayload

  """Create a new Shop Pay payment request session."""
  shopPayPaymentRequestSessionCreate(
    """A unique identifier for the payment request session."""
    sourceIdentifier: String!

    """A payment request object."""
    paymentRequest: ShopPayPaymentRequestInput!
  ): ShopPayPaymentRequestSessionCreatePayload

  """Submits a Shop Pay payment request session."""
  shopPayPaymentRequestSessionSubmit(
    """A token representing a payment session request."""
    token: String!

    """The final payment request object."""
    paymentRequest: ShopPayPaymentRequestInput!

    """The idempotency key is used to guarantee an idempotent result."""
    idempotencyKey: String!

    """
    The order name to be used for the order created from the payment request.
    """
    orderName: String
  ): ShopPayPaymentRequestSessionSubmitPayload
}

"An object with an ID field to support global identification, in accordance with the\n[Relay specification](https://relay.dev/graphql/objectidentification.htm#sec-Node-Interface).\nThis interface is used by the [node](https://shopify.dev/api/admin-graphql/unstable/queries/node)\nand [nodes](https://shopify.dev/api/admin-graphql/unstable/queries/nodes) queries.\n"
interface Node {
  """A globally-unique ID."""
  id: ID!
}

"""
Represents a resource that can be published to the Online Store sales channel.
"""
interface OnlineStorePublishable {
  """
  The URL used for viewing the resource on the shop's Online Store. Returns `null` if the resource is currently not published to the Online Store sales channel.
  """
  onlineStoreUrl: URL
}

"""
An order is a customer’s completed request to purchase one or more products from a shop. An order is created when a customer completes the checkout process, during which time they provides an email address, billing address and payment information.
"""
type Order implements HasMetafields & Node {
  """The address associated with the payment method."""
  billingAddress: MailingAddress

  """
  The reason for the order's cancellation. Returns `null` if the order wasn't canceled.
  """
  cancelReason: OrderCancelReason

  """
  The date and time when the order was canceled. Returns null if the order wasn't canceled.
  """
  canceledAt: DateTime

  """The code of the currency used for the payment."""
  currencyCode: CurrencyCode!

  """
  The subtotal of line items and their discounts, excluding line items that have been removed. Does not contain order-level discounts, duties, shipping costs, or shipping discounts. Taxes aren't included unless the order is a taxes-included order.
  """
  currentSubtotalPrice: MoneyV2!

  """The total cost of duties for the order, including refunds."""
  currentTotalDuties: MoneyV2

  """
  The total amount of the order, including duties, taxes and discounts, minus amounts for line items that have been removed.
  """
  currentTotalPrice: MoneyV2!

  """
  The total cost of shipping, excluding shipping lines that have been refunded or removed. Taxes aren't included unless the order is a taxes-included order.
  """
  currentTotalShippingPrice: MoneyV2!

  """
  The total of all taxes applied to the order, excluding taxes for returned line items.
  """
  currentTotalTax: MoneyV2!

  """
  A list of the custom attributes added to the order. For example, whether an order is a customer's first.
  """
  customAttributes: [Attribute!]!

  """The locale code in which this specific order happened."""
  customerLocale: String

  """The unique URL that the customer can use to access the order."""
  customerUrl: URL

  """Discounts that have been applied on the order."""
  discountApplications(
    """Returns up to the first `n` elements from the list."""
    first: Int

    """Returns the elements that come after the specified cursor."""
    after: String

    """Returns up to the last `n` elements from the list."""
    last: Int

    """Returns the elements that come before the specified cursor."""
    before: String

    """Reverse the order of the underlying list."""
    reverse: Boolean = false
  ): DiscountApplicationConnection!

  """Whether the order has had any edits applied or not."""
  edited: Boolean!

  """The customer's email address."""
  email: String

  """The financial status of the order."""
  financialStatus: OrderFinancialStatus

  """The fulfillment status for the order."""
  fulfillmentStatus: OrderFulfillmentStatus!

  """A globally-unique ID."""
  id: ID!

  """List of the order’s line items."""
  lineItems(
    """Returns up to the first `n` elements from the list."""
    first: Int

    """Returns the elements that come after the specified cursor."""
    after: String

    """Returns up to the last `n` elements from the list."""
    last: Int

    """Returns the elements that come before the specified cursor."""
    before: String

    """Reverse the order of the underlying list."""
    reverse: Boolean = false
  ): OrderLineItemConnection!

  """
  A [custom field](https://shopify.dev/docs/apps/build/custom-data), including its `namespace` and `key`, that's associated with a Shopify resource for the purposes of adding and storing additional information.
  """
  metafield(
    """
    The container the metafield belongs to. If omitted, the app-reserved namespace will be used.
    """
    namespace: String

    """The identifier for the metafield."""
    key: String!
  ): Metafield

  """
  A list of [custom fields](/docs/apps/build/custom-data) that a merchant associates with a Shopify resource.
  """
  metafields(
    """
    The list of metafields to retrieve by namespace and key.
    
    The input must not contain more than `250` values.
    """
    identifiers: [HasMetafieldsIdentifier!]!
  ): [Metafield]!

  "Unique identifier for the order that appears on the order.\nFor example, _#1000_ or _Store1001.\n"
  name: String!

  """
  A unique numeric identifier for the order for use by shop owner and customer.
  """
  orderNumber: Int!

  """The total cost of duties charged at checkout."""
  originalTotalDuties: MoneyV2

  """The total price of the order before any applied edits."""
  originalTotalPrice: MoneyV2!

  """The customer's phone number for receiving SMS notifications."""
  phone: String

  "The date and time when the order was imported.\nThis value can be set to dates in the past when importing from other systems.\nIf no value is provided, it will be auto-generated based on current date and time.\n"
  processedAt: DateTime!

  """The address to where the order will be shipped."""
  shippingAddress: MailingAddress

  "The discounts that have been allocated onto the shipping line by discount applications.\n"
  shippingDiscountAllocations: [DiscountAllocation!]!

  """The unique URL for the order's status page."""
  statusUrl: URL!

  """Price of the order before shipping and taxes."""
  subtotalPrice: MoneyV2

  """Price of the order before duties, shipping and taxes."""
  subtotalPriceV2: MoneyV2 @deprecated(reason: "Use `subtotalPrice` instead.")

  """List of the order’s successful fulfillments."""
  successfulFulfillments(
    """Truncate the array result to this size."""
    first: Int
  ): [Fulfillment!]

  """
  The sum of all the prices of all the items in the order, duties, taxes and discounts included (must be positive).
  """
  totalPrice: MoneyV2!

  """
  The sum of all the prices of all the items in the order, duties, taxes and discounts included (must be positive).
  """
  totalPriceV2: MoneyV2! @deprecated(reason: "Use `totalPrice` instead.")

  """The total amount that has been refunded."""
  totalRefunded: MoneyV2!

  """The total amount that has been refunded."""
  totalRefundedV2: MoneyV2! @deprecated(reason: "Use `totalRefunded` instead.")

  """The total cost of shipping."""
  totalShippingPrice: MoneyV2!

  """The total cost of shipping."""
  totalShippingPriceV2: MoneyV2! @deprecated(reason: "Use `totalShippingPrice` instead.")

  """The total cost of taxes."""
  totalTax: MoneyV2

  """The total cost of taxes."""
  totalTaxV2: MoneyV2 @deprecated(reason: "Use `totalTax` instead.")
}

"""Represents the reason for the order's cancellation."""
enum OrderCancelReason {
  """The customer wanted to cancel the order."""
  CUSTOMER

  """Payment was declined."""
  DECLINED

  """The order was fraudulent."""
  FRAUD

  """There was insufficient inventory."""
  INVENTORY

  """Staff made an error."""
  STAFF

  """The order was canceled for an unlisted reason."""
  OTHER
}

"An auto-generated type for paginating through multiple Orders.\n"
type OrderConnection {
  """A list of edges."""
  edges: [OrderEdge!]!

  """A list of the nodes contained in OrderEdge."""
  nodes: [Order!]!

  """Information to aid in pagination."""
  pageInfo: PageInfo!

  """The total count of Orders."""
  totalCount: UnsignedInt64!
}

"An auto-generated type which holds one Order and a cursor during pagination.\n"
type OrderEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of OrderEdge."""
  node: Order!
}

"""Represents the order's current financial status."""
enum OrderFinancialStatus {
  """Displayed as **Pending**."""
  PENDING

  """Displayed as **Authorized**."""
  AUTHORIZED

  """Displayed as **Partially paid**."""
  PARTIALLY_PAID

  """Displayed as **Partially refunded**."""
  PARTIALLY_REFUNDED

  """Displayed as **Voided**."""
  VOIDED

  """Displayed as **Paid**."""
  PAID

  """Displayed as **Refunded**."""
  REFUNDED
}

"""
Represents the order's aggregated fulfillment status for display purposes.
"""
enum OrderFulfillmentStatus {
  """
  Displayed as **Unfulfilled**. None of the items in the order have been fulfilled.
  """
  UNFULFILLED

  """
  Displayed as **Partially fulfilled**. Some of the items in the order have been fulfilled.
  """
  PARTIALLY_FULFILLED

  """
  Displayed as **Fulfilled**. All of the items in the order have been fulfilled.
  """
  FULFILLED

  """
  Displayed as **Restocked**. All of the items in the order have been restocked. Replaced by "UNFULFILLED" status.
  """
  RESTOCKED

  """
  Displayed as **Pending fulfillment**. A request for fulfillment of some items awaits a response from the fulfillment service. Replaced by "IN_PROGRESS" status.
  """
  PENDING_FULFILLMENT

  """
  Displayed as **Open**. None of the items in the order have been fulfilled. Replaced by "UNFULFILLED" status.
  """
  OPEN

  """
  Displayed as **In progress**. Some of the items in the order have been fulfilled, or a request for fulfillment has been sent to the fulfillment service.
  """
  IN_PROGRESS

  """
  Displayed as **On hold**. All of the unfulfilled items in this order are on hold.
  """
  ON_HOLD

  """
  Displayed as **Scheduled**. All of the unfulfilled items in this order are scheduled for fulfillment at later time.
  """
  SCHEDULED
}

"""
Represents a single line in an order. There is one line item for each distinct product variant.
"""
type OrderLineItem {
  """
  The number of entries associated to the line item minus the items that have been removed.
  """
  currentQuantity: Int!

  """List of custom attributes associated to the line item."""
  customAttributes: [Attribute!]!

  """
  The discounts that have been allocated onto the order line item by discount applications.
  """
  discountAllocations: [DiscountAllocation!]!

  """
  The total price of the line item, including discounts, and displayed in the presentment currency.
  """
  discountedTotalPrice: MoneyV2!

  """
  The total price of the line item, not including any discounts. The total price is calculated using the original unit price multiplied by the quantity, and it's displayed in the presentment currency.
  """
  originalTotalPrice: MoneyV2!

  """The number of products variants associated to the line item."""
  quantity: Int!

  """The title of the product combined with title of the variant."""
  title: String!

  """The product variant object associated to the line item."""
  variant: ProductVariant
}

"An auto-generated type for paginating through multiple OrderLineItems.\n"
type OrderLineItemConnection {
  """A list of edges."""
  edges: [OrderLineItemEdge!]!

  """A list of the nodes contained in OrderLineItemEdge."""
  nodes: [OrderLineItem!]!

  """Information to aid in pagination."""
  pageInfo: PageInfo!
}

"An auto-generated type which holds one OrderLineItem and a cursor during pagination.\n"
type OrderLineItemEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of OrderLineItemEdge."""
  node: OrderLineItem!
}

"""The set of valid sort keys for the Order query."""
enum OrderSortKeys {
  """Sort by the `processed_at` value."""
  PROCESSED_AT

  """Sort by the `total_price` value."""
  TOTAL_PRICE

  """Sort by the `id` value."""
  ID

  "Sort by relevance to the search terms when the `query` parameter is specified on the connection.\nDon't use this sort key when no search query is specified.\n"
  RELEVANCE
}

"""
Shopify merchants can create pages to hold static HTML content. Each Page object represents a custom page on the online store.
"""
type Page implements HasMetafields & Node & OnlineStorePublishable & Trackable {
  """The description of the page, complete with HTML formatting."""
  body: HTML!

  """Summary of the page body."""
  bodySummary: String!

  """The timestamp of the page creation."""
  createdAt: DateTime!

  """
  A human-friendly unique string for the page automatically generated from its title.
  """
  handle: String!

  """A globally-unique ID."""
  id: ID!

  """
  A [custom field](https://shopify.dev/docs/apps/build/custom-data), including its `namespace` and `key`, that's associated with a Shopify resource for the purposes of adding and storing additional information.
  """
  metafield(
    """
    The container the metafield belongs to. If omitted, the app-reserved namespace will be used.
    """
    namespace: String

    """The identifier for the metafield."""
    key: String!
  ): Metafield

  """
  A list of [custom fields](/docs/apps/build/custom-data) that a merchant associates with a Shopify resource.
  """
  metafields(
    """
    The list of metafields to retrieve by namespace and key.
    
    The input must not contain more than `250` values.
    """
    identifiers: [HasMetafieldsIdentifier!]!
  ): [Metafield]!

  """
  The URL used for viewing the resource on the shop's Online Store. Returns `null` if the resource is currently not published to the Online Store sales channel.
  """
  onlineStoreUrl: URL

  """The page's SEO information."""
  seo: SEO

  """The title of the page."""
  title: String!

  """
  URL parameters to be added to a page URL to track the origin of on-site search traffic for [analytics reporting](https://help.shopify.com/manual/reports-and-analytics/shopify-reports/report-types/default-reports/behaviour-reports). Returns a result when accessed through the [search](https://shopify.dev/docs/api/storefront/current/queries/search) or [predictiveSearch](https://shopify.dev/docs/api/storefront/current/queries/predictiveSearch) queries, otherwise returns null.
  """
  trackingParameters: String

  """The timestamp of the latest page update."""
  updatedAt: DateTime!
}

"An auto-generated type for paginating through multiple Pages.\n"
type PageConnection {
  """A list of edges."""
  edges: [PageEdge!]!

  """A list of the nodes contained in PageEdge."""
  nodes: [Page!]!

  """Information to aid in pagination."""
  pageInfo: PageInfo!
}

"An auto-generated type which holds one Page and a cursor during pagination.\n"
type PageEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of PageEdge."""
  node: Page!
}

"Returns information about pagination in a connection, in accordance with the\n[Relay specification](https://relay.dev/graphql/connections.htm#sec-undefined.PageInfo).\nFor more information, please read our [GraphQL Pagination Usage Guide](https://shopify.dev/api/usage/pagination-graphql).\n"
type PageInfo {
  """The cursor corresponding to the last node in edges."""
  endCursor: String

  """Whether there are more pages to fetch following the current page."""
  hasNextPage: Boolean!

  """Whether there are any pages prior to the current page."""
  hasPreviousPage: Boolean!

  """The cursor corresponding to the first node in edges."""
  startCursor: String
}

"""The set of valid sort keys for the Page query."""
enum PageSortKeys {
  """Sort by the `title` value."""
  TITLE

  """Sort by the `updated_at` value."""
  UPDATED_AT

  """Sort by the `id` value."""
  ID

  "Sort by relevance to the search terms when the `query` parameter is specified on the connection.\nDon't use this sort key when no search query is specified.\n"
  RELEVANCE
}

"""Type for paginating through multiple sitemap's resources."""
type PaginatedSitemapResources {
  """Whether there are more pages to fetch following the current page."""
  hasNextPage: Boolean!

  "List of sitemap resources for the current page.\nNote: The number of items varies between 0 and 250 per page.\n"
  items: [SitemapResourceInterface!]!
}

"""Settings related to payments."""
type PaymentSettings {
  """List of the card brands which the business entity accepts."""
  acceptedCardBrands: [CardBrand!]!

  """The url pointing to the endpoint to vault credit cards."""
  cardVaultUrl: URL!

  """
  The country where the shop is located. When multiple business entities operate within the shop, then this will represent the country of the business entity that's serving the specified buyer context.
  """
  countryCode: CountryCode!

  """The three-letter code for the shop's primary currency."""
  currencyCode: CurrencyCode!

  "A list of enabled currencies (ISO 4217 format) that the shop accepts.\nMerchants can enable currencies from their Shopify Payments settings in the Shopify admin.\n"
  enabledPresentmentCurrencies: [CurrencyCode!]!

  """The shop’s Shopify Payments account ID."""
  shopifyPaymentsAccountId: String

  """List of the digital wallets which the business entity supports."""
  supportedDigitalWallets: [DigitalWallet!]!
}

"""Decides the distribution of results."""
enum PredictiveSearchLimitScope {
  """Return results up to limit across all types."""
  ALL

  """Return results up to limit per type."""
  EACH
}

"A predictive search result represents a list of products, collections, pages, articles, and query suggestions\nthat matches the predictive search query.\n"
type PredictiveSearchResult {
  """The articles that match the search query."""
  articles: [Article!]!

  """The articles that match the search query."""
  collections: [Collection!]!

  """The pages that match the search query."""
  pages: [Page!]!

  """The products that match the search query."""
  products: [Product!]!

  """The query suggestions that are relevant to the search query."""
  queries: [SearchQuerySuggestion!]!
}

"""The types of search items to perform predictive search on."""
enum PredictiveSearchType {
  """Returns matching collections."""
  COLLECTION

  """Returns matching products."""
  PRODUCT

  """Returns matching pages."""
  PAGE

  """Returns matching articles."""
  ARTICLE

  """Returns matching query strings."""
  QUERY
}

"""
The preferred delivery methods such as shipping, local pickup or through pickup points.
"""
enum PreferenceDeliveryMethodType {
  """
  A delivery method used to send items directly to a buyer’s specified address.
  """
  SHIPPING

  """
  A delivery method used to let buyers receive items directly from a specific location within an area.
  """
  PICK_UP

  """
  A delivery method used to let buyers collect purchases at designated locations like parcel lockers.
  """
  PICKUP_POINT
}

"The input fields for a filter used to view a subset of products in a collection matching a specific price range.\n"
input PriceRangeFilter {
  """The minimum price in the range. Defaults to zero."""
  min: Float = 0

  """The maximum price in the range. Empty indicates no max price."""
  max: Float
}

"""The value of the percentage pricing object."""
type PricingPercentageValue {
  """The percentage value of the object."""
  percentage: Float!
}

"""The price value (fixed or percentage) for a discount application."""
union PricingValue = MoneyV2 | PricingPercentageValue

"The `Product` object lets you manage products in a merchant’s store.\n\nProducts are the goods and services that merchants offer to customers.\nThey can include various details such as title, description, price, images, and options such as size or color.\nYou can use [product variants](/docs/api/storefront/latest/objects/ProductVariant)\nto create or update different versions of the same product.\nYou can also add or update product [media](/docs/api/storefront/latest/interfaces/Media).\nProducts can be organized by grouping them into a [collection](/docs/api/storefront/latest/objects/Collection).\n\nLearn more about working with [products and collections](/docs/storefronts/headless/building-with-the-storefront-api/products-collections).\n"
type Product implements HasMetafields & Node & OnlineStorePublishable & Trackable {
  "A list of variants whose selected options differ with the provided selected options by one, ordered by variant id.\nIf selected options are not provided, adjacent variants to the first available variant is returned.\n\nNote that this field returns an array of variants. In most cases, the number of variants in this array will be low.\nHowever, with a low number of options and a high number of values per option, the number of variants returned\nhere can be high. In such cases, it recommended to avoid using this field.\n\nThis list of variants can be used in combination with the `options` field to build a rich variant picker that\nincludes variant availability or other variant information.\n"
  adjacentVariants(
    """
    The input fields used for a selected option.
    
    The input must not contain more than `250` values.
    """
    selectedOptions: [SelectedOptionInput!]

    """
    Whether to ignore product options that are not present on the requested product.
    """
    ignoreUnknownOptions: Boolean = true

    """Whether to perform case insensitive match on option names and values."""
    caseInsensitiveMatch: Boolean = false
  ): [ProductVariant!]!

  """Indicates if at least one product variant is available for sale."""
  availableForSale: Boolean!

  """
  The category of a product from [Shopify's Standard Product Taxonomy](https://shopify.github.io/product-taxonomy/releases/unstable/?categoryId=sg-4-17-2-17).
  """
  category: TaxonomyCategory

  """
  A list of [collections](/docs/api/storefront/latest/objects/Collection) that include the product.
  """
  collections(
    """Returns up to the first `n` elements from the list."""
    first: Int

    """Returns the elements that come after the specified cursor."""
    after: String

    """Returns up to the last `n` elements from the list."""
    last: Int

    """Returns the elements that come before the specified cursor."""
    before: String

    """Reverse the order of the underlying list."""
    reverse: Boolean = false
  ): CollectionConnection!

  """
  The [compare-at price range](https://help.shopify.com/manual/products/details/product-pricing/sale-pricing) of the product in the shop's default currency.
  """
  compareAtPriceRange: ProductPriceRange!

  """The date and time when the product was created."""
  createdAt: DateTime!

  """
  A single-line description of the product, with [HTML tags](https://developer.mozilla.org/en-US/docs/Web/HTML) removed.
  """
  description(
    """Truncates a string after the given length."""
    truncateAt: Int
  ): String!

  "The description of the product, with\nHTML tags. For example, the description might include\nbold `<strong></strong>` and italic `<i></i>` text.\n"
  descriptionHtml: HTML!

  "An encoded string containing all option value combinations\nwith a corresponding variant that is currently available for sale.\n\nIntegers represent option and values:\n[0,1] represents option_value at array index 0 for the option at array index 0\n\n`:`, `,`, ` ` and `-` are control characters.\n`:` indicates a new option. ex: 0:1 indicates value 0 for the option in position 1, value 1 for the option in position 2.\n`,` indicates the end of a repeated prefix, mulitple consecutive commas indicate the end of multiple repeated prefixes.\n` ` indicates a gap in the sequence of option values. ex: 0 4 indicates option values in position 0 and 4 are present.\n`-` indicates a continuous range of option values. ex: 0 1-3 4\n\nDecoding process:\n\nExample options: [Size, Color, Material]\nExample values: [[Small, Medium, Large], [Red, Blue], [Cotton, Wool]]\nExample encoded string: \"0:0:0,1:0-1,,1:0:0-1,1:1,,2:0:1,1:0,,\"\n\nStep 1: Expand ranges into the numbers they represent: \"0:0:0,1:0 1,,1:0:0 1,1:1,,2:0:1,1:0,,\"\nStep 2: Expand repeated prefixes: \"0:0:0,0:1:0 1,1:0:0 1,1:1:1,2:0:1,2:1:0,\"\nStep 3: Expand shared prefixes so data is encoded as a string: \"0:0:0,0:1:0,0:1:1,1:0:0,1:0:1,1:1:1,2:0:1,2:1:0,\"\nStep 4: Map to options + option values to determine existing variants:\n\n[Small, Red, Cotton] (0:0:0), [Small, Blue, Cotton] (0:1:0), [Small, Blue, Wool] (0:1:1),\n[Medium, Red, Cotton] (1:0:0), [Medium, Red, Wool] (1:0:1), [Medium, Blue, Wool] (1:1:1),\n[Large, Red, Wool] (2:0:1), [Large, Blue, Cotton] (2:1:0).\n\n"
  encodedVariantAvailability: String

  "An encoded string containing all option value combinations with a corresponding variant.\n\nIntegers represent option and values:\n[0,1] represents option_value at array index 0 for the option at array index 0\n\n`:`, `,`, ` ` and `-` are control characters.\n`:` indicates a new option. ex: 0:1 indicates value 0 for the option in position 1, value 1 for the option in position 2.\n`,` indicates the end of a repeated prefix, mulitple consecutive commas indicate the end of multiple repeated prefixes.\n` ` indicates a gap in the sequence of option values. ex: 0 4 indicates option values in position 0 and 4 are present.\n`-` indicates a continuous range of option values. ex: 0 1-3 4\n\nDecoding process:\n\nExample options: [Size, Color, Material]\nExample values: [[Small, Medium, Large], [Red, Blue], [Cotton, Wool]]\nExample encoded string: \"0:0:0,1:0-1,,1:0:0-1,1:1,,2:0:1,1:0,,\"\n\nStep 1: Expand ranges into the numbers they represent: \"0:0:0,1:0 1,,1:0:0 1,1:1,,2:0:1,1:0,,\"\nStep 2: Expand repeated prefixes: \"0:0:0,0:1:0 1,1:0:0 1,1:1:1,2:0:1,2:1:0,\"\nStep 3: Expand shared prefixes so data is encoded as a string: \"0:0:0,0:1:0,0:1:1,1:0:0,1:0:1,1:1:1,2:0:1,2:1:0,\"\nStep 4: Map to options + option values to determine existing variants:\n\n[Small, Red, Cotton] (0:0:0), [Small, Blue, Cotton] (0:1:0), [Small, Blue, Wool] (0:1:1),\n[Medium, Red, Cotton] (1:0:0), [Medium, Red, Wool] (1:0:1), [Medium, Blue, Wool] (1:1:1),\n[Large, Red, Wool] (2:0:1), [Large, Blue, Cotton] (2:1:0).\n\n"
  encodedVariantExistence: String

  "The featured image for the product.\n\nThis field is functionally equivalent to `images(first: 1)`.\n"
  featuredImage: Image

  "A unique, human-readable string of the product's title.\nA handle can contain letters, hyphens (`-`), and numbers, but no spaces.\nThe handle is used in the online store URL for the product.\n"
  handle: String!

  """A globally-unique ID."""
  id: ID!

  """List of images associated with the product."""
  images(
    """Returns up to the first `n` elements from the list."""
    first: Int

    """Returns the elements that come after the specified cursor."""
    after: String

    """Returns up to the last `n` elements from the list."""
    last: Int

    """Returns the elements that come before the specified cursor."""
    before: String

    """Reverse the order of the underlying list."""
    reverse: Boolean = false

    """Sort the underlying list by the given key."""
    sortKey: ProductImageSortKeys = POSITION
  ): ImageConnection!

  """Whether the product is a gift card."""
  isGiftCard: Boolean!

  """
  The [media](/docs/apps/build/online-store/product-media) that are associated with the product. Valid media are images, 3D models, videos.
  """
  media(
    """Returns up to the first `n` elements from the list."""
    first: Int

    """Returns the elements that come after the specified cursor."""
    after: String

    """Returns up to the last `n` elements from the list."""
    last: Int

    """Returns the elements that come before the specified cursor."""
    before: String

    """Reverse the order of the underlying list."""
    reverse: Boolean = false

    """Sort the underlying list by the given key."""
    sortKey: ProductMediaSortKeys = POSITION
  ): MediaConnection!

  """
  A [custom field](https://shopify.dev/docs/apps/build/custom-data), including its `namespace` and `key`, that's associated with a Shopify resource for the purposes of adding and storing additional information.
  """
  metafield(
    """
    The container the metafield belongs to. If omitted, the app-reserved namespace will be used.
    """
    namespace: String

    """The identifier for the metafield."""
    key: String!
  ): Metafield

  """
  A list of [custom fields](/docs/apps/build/custom-data) that a merchant associates with a Shopify resource.
  """
  metafields(
    """
    The list of metafields to retrieve by namespace and key.
    
    The input must not contain more than `250` values.
    """
    identifiers: [HasMetafieldsIdentifier!]!
  ): [Metafield]!

  "The product's URL on the online store.\nIf `null`, then the product isn't published to the online store sales channel.\n"
  onlineStoreUrl: URL

  """
  A list of product options. The limit is defined by the [shop's resource limits for product options](/docs/api/admin-graphql/latest/objects/Shop#field-resourcelimits) (`Shop.resourceLimits.maxProductOptions`).
  """
  options(
    """Truncate the array result to this size."""
    first: Int
  ): [ProductOption!]!

  "The minimum and maximum prices of a product, expressed in decimal numbers.\nFor example, if the product is priced between $10.00 and $50.00,\nthen the price range is $10.00 - $50.00.\n"
  priceRange: ProductPriceRange!

  "The [product type](https://help.shopify.com/manual/products/details/product-type)\nthat merchants define.\n"
  productType: String!

  """The date and time when the product was published to the channel."""
  publishedAt: DateTime!

  """
  Whether the product can only be purchased with a [selling plan](/docs/apps/build/purchase-options/subscriptions/selling-plans). Products that are sold on subscription (`requiresSellingPlan: true`) can be updated only for online stores. If you update a product to be subscription-only (`requiresSellingPlan:false`), then the product is unpublished from all channels, except the online store.
  """
  requiresSellingPlan: Boolean!

  "Find an active product variant based on selected options, availability or the first variant.\n\nAll arguments are optional. If no selected options are provided, the first available variant is returned.\nIf no variants are available, the first variant is returned.\n"
  selectedOrFirstAvailableVariant(
    """
    The input fields used for a selected option.
    
    The input must not contain more than `250` values.
    """
    selectedOptions: [SelectedOptionInput!]

    """Whether to ignore unknown product options."""
    ignoreUnknownOptions: Boolean = true

    """Whether to perform case insensitive match on option names and values."""
    caseInsensitiveMatch: Boolean = false
  ): ProductVariant

  """
  A list of all [selling plan groups](/docs/apps/build/purchase-options/subscriptions/selling-plans/build-a-selling-plan) that are associated with the product either directly, or through the product's variants.
  """
  sellingPlanGroups(
    """Returns up to the first `n` elements from the list."""
    first: Int

    """Returns the elements that come after the specified cursor."""
    after: String

    """Returns up to the last `n` elements from the list."""
    last: Int

    """Returns the elements that come before the specified cursor."""
    before: String

    """Reverse the order of the underlying list."""
    reverse: Boolean = false
  ): SellingPlanGroupConnection!

  "The [SEO title and description](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords)\nthat are associated with a product.\n"
  seo: SEO!

  "A comma-separated list of searchable keywords that are\nassociated with the product. For example, a merchant might apply the `sports`\nand `summer` tags to products that are associated with sportwear for summer.\nUpdating `tags` overwrites any existing tags that were previously added to the product.\nTo add new tags without overwriting existing tags,\nuse the GraphQL Admin API's [`tagsAdd`](/docs/api/admin-graphql/latest/mutations/tagsadd)\nmutation.\n"
  tags: [String!]!

  "The name for the product that displays to customers. The title is used to construct the product's handle.\nFor example, if a product is titled \"Black Sunglasses\", then the handle is `black-sunglasses`.\n"
  title: String!

  """The quantity of inventory that's in stock."""
  totalInventory: Int

  """
  URL parameters to be added to a page URL to track the origin of on-site search traffic for [analytics reporting](https://help.shopify.com/manual/reports-and-analytics/shopify-reports/report-types/default-reports/behaviour-reports). Returns a result when accessed through the [search](https://shopify.dev/docs/api/storefront/current/queries/search) or [predictiveSearch](https://shopify.dev/docs/api/storefront/current/queries/predictiveSearch) queries, otherwise returns null.
  """
  trackingParameters: String

  "The date and time when the product was last modified.\nA product's `updatedAt` value can change for different reasons. For example, if an order\nis placed for a product that has inventory tracking set up, then the inventory adjustment\nis counted as an update.\n"
  updatedAt: DateTime!

  "Find a product’s variant based on its selected options.\nThis is useful for converting a user’s selection of product options into a single matching variant.\nIf there is not a variant for the selected options, `null` will be returned.\n"
  variantBySelectedOptions(
    """
    The input fields used for a selected option.
    
    The input must not contain more than `250` values.
    """
    selectedOptions: [SelectedOptionInput!]!

    """Whether to ignore unknown product options."""
    ignoreUnknownOptions: Boolean = false

    """Whether to perform case insensitive match on option names and values."""
    caseInsensitiveMatch: Boolean = false
  ): ProductVariant

  """
  A list of [variants](/docs/api/storefront/latest/objects/ProductVariant) that are associated with the product.
  """
  variants(
    """Returns up to the first `n` elements from the list."""
    first: Int

    """Returns the elements that come after the specified cursor."""
    after: String

    """Returns up to the last `n` elements from the list."""
    last: Int

    """Returns the elements that come before the specified cursor."""
    before: String

    """Reverse the order of the underlying list."""
    reverse: Boolean = false

    """Sort the underlying list by the given key."""
    sortKey: ProductVariantSortKeys = POSITION
  ): ProductVariantConnection!

  """
  The number of [variants](/docs/api/storefront/latest/objects/ProductVariant) that are associated with the product.
  """
  variantsCount: Count

  """The name of the product's vendor."""
  vendor: String!
}

"""The set of valid sort keys for the ProductCollection query."""
enum ProductCollectionSortKeys {
  """Sort by the `title` value."""
  TITLE

  """Sort by the `price` value."""
  PRICE

  """Sort by the `best-selling` value."""
  BEST_SELLING

  """Sort by the `created` value."""
  CREATED

  """Sort by the `id` value."""
  ID

  """Sort by the `manual` value."""
  MANUAL

  """Sort by the `collection-default` value."""
  COLLECTION_DEFAULT

  "Sort by relevance to the search terms when the `query` parameter is specified on the connection.\nDon't use this sort key when no search query is specified.\n"
  RELEVANCE
}

"An auto-generated type for paginating through multiple Products.\n"
type ProductConnection {
  """A list of edges."""
  edges: [ProductEdge!]!

  """A list of available filters."""
  filters: [Filter!]!

  """A list of the nodes contained in ProductEdge."""
  nodes: [Product!]!

  """Information to aid in pagination."""
  pageInfo: PageInfo!
}

"An auto-generated type which holds one Product and a cursor during pagination.\n"
type ProductEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of ProductEdge."""
  node: Product!
}

"The input fields for a filter used to view a subset of products in a collection.\nBy default, the `available` and `price` filters are enabled. Filters are customized with the Shopify Search & Discovery app.\nLearn more about [customizing storefront filtering](https://help.shopify.com/manual/online-store/themes/customizing-themes/storefront-filters).\n"
input ProductFilter {
  """Filter on if the product is available for sale."""
  available: Boolean

  """A variant option to filter on."""
  variantOption: VariantOptionFilter

  """The product type to filter on."""
  productType: String

  """The product vendor to filter on."""
  productVendor: String

  """A range of prices to filter with-in."""
  price: PriceRangeFilter

  """A product metafield to filter on."""
  productMetafield: MetafieldFilter

  """A variant metafield to filter on."""
  variantMetafield: MetafieldFilter

  """A product tag to filter on."""
  tag: String
}

"""The set of valid sort keys for the ProductImage query."""
enum ProductImageSortKeys {
  """Sort by the `created_at` value."""
  CREATED_AT

  """Sort by the `position` value."""
  POSITION

  """Sort by the `id` value."""
  ID

  "Sort by relevance to the search terms when the `query` parameter is specified on the connection.\nDon't use this sort key when no search query is specified.\n"
  RELEVANCE
}

"""The set of valid sort keys for the ProductMedia query."""
enum ProductMediaSortKeys {
  """Sort by the `position` value."""
  POSITION

  """Sort by the `id` value."""
  ID

  "Sort by relevance to the search terms when the `query` parameter is specified on the connection.\nDon't use this sort key when no search query is specified.\n"
  RELEVANCE
}

"Product property names like \"Size\", \"Color\", and \"Material\" that the customers can select.\nVariants are selected based on permutations of these options.\n255 characters limit each.\n"
type ProductOption implements Node {
  """A globally-unique ID."""
  id: ID!

  """The product option’s name."""
  name: String!

  """The corresponding option value to the product option."""
  optionValues: [ProductOptionValue!]!

  """The corresponding value to the product option name."""
  values: [String!]! @deprecated(reason: "Use `optionValues` instead.")
}

"The product option value names. For example, \"Red\", \"Blue\", and \"Green\" for a \"Color\" option.\n"
type ProductOptionValue implements Node {
  "The product variant that combines this option value with the\nlowest-position option values for all other options.\n\nThis field will always return a variant, provided a variant including this option value exists.\n"
  firstSelectableVariant: ProductVariant

  """A globally-unique ID."""
  id: ID!

  """The name of the product option value."""
  name: String!

  """The swatch of the product option value."""
  swatch: ProductOptionValueSwatch
}

"The product option value swatch.\n"
type ProductOptionValueSwatch {
  """The swatch color."""
  color: Color

  """The swatch image."""
  image: Media
}

"""The price range of the product."""
type ProductPriceRange {
  """The highest variant's price."""
  maxVariantPrice: MoneyV2!

  """The lowest variant's price."""
  minVariantPrice: MoneyV2!
}

"The recommendation intent that is used to generate product recommendations.\nYou can use intent to generate product recommendations according to different strategies.\n"
enum ProductRecommendationIntent {
  """
  Offer customers a mix of products that are similar or complementary to a product for which recommendations are to be fetched. An example is substitutable products that display in a You may also like section.
  """
  RELATED

  """
  Offer customers products that are complementary to a product for which recommendations are to be fetched. An example is add-on products that display in a Pair it with section.
  """
  COMPLEMENTARY
}

"""The set of valid sort keys for the Product query."""
enum ProductSortKeys {
  """Sort by the `title` value."""
  TITLE

  """Sort by the `product_type` value."""
  PRODUCT_TYPE

  """Sort by the `vendor` value."""
  VENDOR

  """Sort by the `updated_at` value."""
  UPDATED_AT

  """Sort by the `created_at` value."""
  CREATED_AT

  """Sort by the `best_selling` value."""
  BEST_SELLING

  """Sort by the `price` value."""
  PRICE

  """Sort by the `id` value."""
  ID

  "Sort by relevance to the search terms when the `query` parameter is specified on the connection.\nDon't use this sort key when no search query is specified.\n"
  RELEVANCE
}

"A product variant represents a different version of a product, such as differing sizes or differing colors.\n"
type ProductVariant implements HasMetafields & Node {
  """Indicates if the product variant is available for sale."""
  availableForSale: Boolean!

  """
  The barcode (for example, ISBN, UPC, or GTIN) associated with the variant.
  """
  barcode: String

  """
  The compare at price of the variant. This can be used to mark a variant as on sale, when `compareAtPrice` is higher than `price`.
  """
  compareAtPrice: MoneyV2

  """
  The compare at price of the variant. This can be used to mark a variant as on sale, when `compareAtPriceV2` is higher than `priceV2`.
  """
  compareAtPriceV2: MoneyV2 @deprecated(reason: "Use `compareAtPrice` instead.")

  "List of bundles components included in the variant considering only fixed bundles.\n"
  components(
    """Returns up to the first `n` elements from the list."""
    first: Int

    """Returns the elements that come after the specified cursor."""
    after: String

    """Returns up to the last `n` elements from the list."""
    last: Int

    """Returns the elements that come before the specified cursor."""
    before: String
  ): ProductVariantComponentConnection!

  """
  Whether a product is out of stock but still available for purchase (used for backorders).
  """
  currentlyNotInStock: Boolean!

  "List of bundles that include this variant considering only fixed bundles.\n"
  groupedBy(
    """Returns up to the first `n` elements from the list."""
    first: Int

    """Returns the elements that come after the specified cursor."""
    after: String

    """Returns up to the last `n` elements from the list."""
    last: Int

    """Returns the elements that come before the specified cursor."""
    before: String
  ): ProductVariantConnection!

  """A globally-unique ID."""
  id: ID!

  """
  Image associated with the product variant. This field falls back to the product image if no image is available.
  """
  image: Image

  """
  A [custom field](https://shopify.dev/docs/apps/build/custom-data), including its `namespace` and `key`, that's associated with a Shopify resource for the purposes of adding and storing additional information.
  """
  metafield(
    """
    The container the metafield belongs to. If omitted, the app-reserved namespace will be used.
    """
    namespace: String

    """The identifier for the metafield."""
    key: String!
  ): Metafield

  """
  A list of [custom fields](/docs/apps/build/custom-data) that a merchant associates with a Shopify resource.
  """
  metafields(
    """
    The list of metafields to retrieve by namespace and key.
    
    The input must not contain more than `250` values.
    """
    identifiers: [HasMetafieldsIdentifier!]!
  ): [Metafield]!

  """The product variant’s price."""
  price: MoneyV2!

  """The product variant’s price."""
  priceV2: MoneyV2! @deprecated(reason: "Use `price` instead.")

  """The product object that the product variant belongs to."""
  product: Product!

  """The total sellable quantity of the variant for online sales channels."""
  quantityAvailable: Int

  """A list of quantity breaks for the product variant."""
  quantityPriceBreaks(
    """Returns up to the first `n` elements from the list."""
    first: Int

    """Returns the elements that come after the specified cursor."""
    after: String

    """Returns up to the last `n` elements from the list."""
    last: Int

    """Returns the elements that come before the specified cursor."""
    before: String
  ): QuantityPriceBreakConnection!

  """The quantity rule for the product variant in a given context."""
  quantityRule: QuantityRule!

  "Whether a product variant requires components. The default value is `false`.\nIf `true`, then the product variant can only be purchased as a parent bundle with components.\n"
  requiresComponents: Boolean!

  """
  Whether a customer needs to provide a shipping address when placing an order for the product variant.
  """
  requiresShipping: Boolean!

  """List of product options applied to the variant."""
  selectedOptions: [SelectedOption!]!

  """
  Represents an association between a variant and a selling plan. Selling plan allocations describe which selling plans are available for each variant, and what their impact is on pricing.
  """
  sellingPlanAllocations(
    """Returns up to the first `n` elements from the list."""
    first: Int

    """Returns the elements that come after the specified cursor."""
    after: String

    """Returns up to the last `n` elements from the list."""
    last: Int

    """Returns the elements that come before the specified cursor."""
    before: String

    """Reverse the order of the underlying list."""
    reverse: Boolean = false
  ): SellingPlanAllocationConnection!

  """The Shop Pay Installments pricing information for the product variant."""
  shopPayInstallmentsPricing: ShopPayInstallmentsProductVariantPricing

  """The SKU (stock keeping unit) associated with the variant."""
  sku: String

  """The in-store pickup availability of this variant by location."""
  storeAvailability(
    """Used to sort results based on proximity to the provided location."""
    near: GeoCoordinateInput

    """Returns up to the first `n` elements from the list."""
    first: Int

    """Returns the elements that come after the specified cursor."""
    after: String

    """Returns up to the last `n` elements from the list."""
    last: Int

    """Returns the elements that come before the specified cursor."""
    before: String

    """Reverse the order of the underlying list."""
    reverse: Boolean = false
  ): StoreAvailabilityConnection!

  """Whether tax is charged when the product variant is sold."""
  taxable: Boolean!

  """The product variant’s title."""
  title: String!

  """
  The unit price value for the variant based on the variant's measurement.
  """
  unitPrice: MoneyV2

  """The unit price measurement for the variant."""
  unitPriceMeasurement: UnitPriceMeasurement

  """
  The weight of the product variant in the unit system specified with `weight_unit`.
  """
  weight: Float

  """Unit of measurement for weight."""
  weightUnit: WeightUnit!
}

"Represents a component of a bundle variant.\n"
type ProductVariantComponent {
  """The product variant object that the component belongs to."""
  productVariant: ProductVariant!

  """The quantity of component present in the bundle."""
  quantity: Int!
}

"An auto-generated type for paginating through multiple ProductVariantComponents.\n"
type ProductVariantComponentConnection {
  """A list of edges."""
  edges: [ProductVariantComponentEdge!]!

  """A list of the nodes contained in ProductVariantComponentEdge."""
  nodes: [ProductVariantComponent!]!

  """Information to aid in pagination."""
  pageInfo: PageInfo!
}

"An auto-generated type which holds one ProductVariantComponent and a cursor during pagination.\n"
type ProductVariantComponentEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of ProductVariantComponentEdge."""
  node: ProductVariantComponent!
}

"An auto-generated type for paginating through multiple ProductVariants.\n"
type ProductVariantConnection {
  """A list of edges."""
  edges: [ProductVariantEdge!]!

  """A list of the nodes contained in ProductVariantEdge."""
  nodes: [ProductVariant!]!

  """Information to aid in pagination."""
  pageInfo: PageInfo!
}

"An auto-generated type which holds one ProductVariant and a cursor during pagination.\n"
type ProductVariantEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of ProductVariantEdge."""
  node: ProductVariant!
}

"""The set of valid sort keys for the ProductVariant query."""
enum ProductVariantSortKeys {
  """Sort by the `title` value."""
  TITLE

  """Sort by the `sku` value."""
  SKU

  """Sort by the `position` value."""
  POSITION

  """Sort by the `id` value."""
  ID

  "Sort by relevance to the search terms when the `query` parameter is specified on the connection.\nDon't use this sort key when no search query is specified.\n"
  RELEVANCE
}

"""
Represents information about the buyer that is interacting with the cart.
"""
type PurchasingCompany {
  """The company associated to the order or draft order."""
  company: Company!

  """The company contact associated to the order or draft order."""
  contact: CompanyContact

  """The company location associated to the order or draft order."""
  location: CompanyLocation!
}

"Quantity price breaks lets you offer different rates that are based on the\namount of a specific variant being ordered.\n"
type QuantityPriceBreak {
  "Minimum quantity required to reach new quantity break price.\n"
  minimumQuantity: Int!

  "The price of variant after reaching the minimum quanity.\n"
  price: MoneyV2!
}

"An auto-generated type for paginating through multiple QuantityPriceBreaks.\n"
type QuantityPriceBreakConnection {
  """A list of edges."""
  edges: [QuantityPriceBreakEdge!]!

  """A list of the nodes contained in QuantityPriceBreakEdge."""
  nodes: [QuantityPriceBreak!]!

  """Information to aid in pagination."""
  pageInfo: PageInfo!
}

"An auto-generated type which holds one QuantityPriceBreak and a cursor during pagination.\n"
type QuantityPriceBreakEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of QuantityPriceBreakEdge."""
  node: QuantityPriceBreak!
}

"The quantity rule for the product variant in a given context.\n"
type QuantityRule {
  "The value that specifies the quantity increment between minimum and maximum of the rule.\nOnly quantities divisible by this value will be considered valid.\n\nThe increment must be lower than or equal to the minimum and the maximum, and both minimum and maximum\nmust be divisible by this value.\n"
  increment: Int!

  "An optional value that defines the highest allowed quantity purchased by the customer.\nIf defined, maximum must be lower than or equal to the minimum and must be a multiple of the increment.\n"
  maximum: Int

  "The value that defines the lowest allowed quantity purchased by the customer.\nThe minimum must be a multiple of the quantity rule's increment.\n"
  minimum: Int!
}

"""
The schema’s entry-point for queries. This acts as the public, top-level API from which all queries must start.
"""
type QueryRoot {
  """Fetch a specific Article by its ID."""
  article(
    """The ID of the `Article`."""
    id: ID!
  ): Article

  """List of the shop's articles."""
  articles(
    """Returns up to the first `n` elements from the list."""
    first: Int

    """Returns the elements that come after the specified cursor."""
    after: String

    """Returns up to the last `n` elements from the list."""
    last: Int

    """Returns the elements that come before the specified cursor."""
    before: String

    """Reverse the order of the underlying list."""
    reverse: Boolean = false

    """Sort the underlying list by the given key."""
    sortKey: ArticleSortKeys = ID

    "Apply one or multiple filters to the query.\n| name | description | acceptable_values | default_value | example_use |\n| ---- | ---- | ---- | ---- | ---- |\n| author |\n| blog_title |\n| created_at |\n| tag |\n| tag_not |\n| updated_at |\nRefer to the detailed [search syntax](https://shopify.dev/api/usage/search-syntax) for more information about using filters.\n"
    query: String
  ): ArticleConnection!

  """Fetch a specific `Blog` by one of its unique attributes."""
  blog(
    """The handle of the `Blog`."""
    handle: String

    """The ID of the `Blog`."""
    id: ID
  ): Blog

  """Find a blog by its handle."""
  blogByHandle(
    """The handle of the blog."""
    handle: String!
  ): Blog @deprecated(reason: "Use `blog` instead.")

  """List of the shop's blogs."""
  blogs(
    """Returns up to the first `n` elements from the list."""
    first: Int

    """Returns the elements that come after the specified cursor."""
    after: String

    """Returns up to the last `n` elements from the list."""
    last: Int

    """Returns the elements that come before the specified cursor."""
    before: String

    """Reverse the order of the underlying list."""
    reverse: Boolean = false

    """Sort the underlying list by the given key."""
    sortKey: BlogSortKeys = ID

    "Apply one or multiple filters to the query.\n| name | description | acceptable_values | default_value | example_use |\n| ---- | ---- | ---- | ---- | ---- |\n| created_at |\n| handle |\n| title |\n| updated_at |\nRefer to the detailed [search syntax](https://shopify.dev/api/usage/search-syntax) for more information about using filters.\n"
    query: String
  ): BlogConnection!

  "Retrieve a cart by its ID. For more information, refer to\n[Manage a cart with the Storefront API](https://shopify.dev/custom-storefronts/cart/manage).\n"
  cart(
    """The ID of the cart."""
    id: ID!
  ): Cart

  "A poll for the status of the cart checkout completion and order creation.\n"
  cartCompletionAttempt(
    """The ID of the attempt."""
    attemptId: String!
  ): CartCompletionAttemptResult

  """Fetch a specific `Collection` by one of its unique attributes."""
  collection(
    """The ID of the `Collection`."""
    id: ID

    """The handle of the `Collection`."""
    handle: String
  ): Collection

  """Find a collection by its handle."""
  collectionByHandle(
    """The handle of the collection."""
    handle: String!
  ): Collection @deprecated(reason: "Use `collection` instead.")

  """List of the shop’s collections."""
  collections(
    """Returns up to the first `n` elements from the list."""
    first: Int

    """Returns the elements that come after the specified cursor."""
    after: String

    """Returns up to the last `n` elements from the list."""
    last: Int

    """Returns the elements that come before the specified cursor."""
    before: String

    """Reverse the order of the underlying list."""
    reverse: Boolean = false

    """Sort the underlying list by the given key."""
    sortKey: CollectionSortKeys = ID

    "Apply one or multiple filters to the query.\n| name | description | acceptable_values | default_value | example_use |\n| ---- | ---- | ---- | ---- | ---- |\n| collection_type |\n| title |\n| updated_at |\nRefer to the detailed [search syntax](https://shopify.dev/api/usage/search-syntax) for more information about using filters.\n"
    query: String
  ): CollectionConnection!

  "The customer associated with the given access token. Tokens are obtained by using the\n[`customerAccessTokenCreate` mutation](https://shopify.dev/docs/api/storefront/latest/mutations/customerAccessTokenCreate).\n"
  customer(
    """The customer access token."""
    customerAccessToken: String!
  ): Customer

  """Returns the localized experiences configured for the shop."""
  localization: Localization!

  "List of the shop's locations that support in-store pickup.\n\nWhen sorting by distance, you must specify a location via the `near` argument.\n\n"
  locations(
    """Returns up to the first `n` elements from the list."""
    first: Int

    """Returns the elements that come after the specified cursor."""
    after: String

    """Returns up to the last `n` elements from the list."""
    last: Int

    """Returns the elements that come before the specified cursor."""
    before: String

    """Reverse the order of the underlying list."""
    reverse: Boolean = false

    """Sort the underlying list by the given key."""
    sortKey: LocationSortKeys = ID

    """Used to sort results based on proximity to the provided location."""
    near: GeoCoordinateInput
  ): LocationConnection!

  """
  Retrieve a [navigation menu](https://help.shopify.com/manual/online-store/menus-and-links) by its handle.
  """
  menu(
    """The navigation menu's handle."""
    handle: String!
  ): Menu

  """Fetch a specific Metaobject by one of its unique identifiers."""
  metaobject(
    """The ID of the metaobject."""
    id: ID

    """The handle and type of the metaobject."""
    handle: MetaobjectHandleInput
  ): Metaobject

  """All active metaobjects for the shop."""
  metaobjects(
    """The type of metaobject to retrieve."""
    type: String!

    """The key of a field to sort with. Supports "id" and "updated_at"."""
    sortKey: String

    """Returns up to the first `n` elements from the list."""
    first: Int

    """Returns the elements that come after the specified cursor."""
    after: String

    """Returns up to the last `n` elements from the list."""
    last: Int

    """Returns the elements that come before the specified cursor."""
    before: String

    """Reverse the order of the underlying list."""
    reverse: Boolean = false
  ): MetaobjectConnection!

  """Returns a specific node by ID."""
  node(
    """The ID of the Node to return."""
    id: ID!
  ): Node

  """Returns the list of nodes with the given IDs."""
  nodes(
    """
    The IDs of the Nodes to return.
    
    The input must not contain more than `250` values.
    """
    ids: [ID!]!
  ): [Node]!

  """Fetch a specific `Page` by one of its unique attributes."""
  page(
    """The handle of the `Page`."""
    handle: String

    """The ID of the `Page`."""
    id: ID
  ): Page

  """Find a page by its handle."""
  pageByHandle(
    """The handle of the page."""
    handle: String!
  ): Page @deprecated(reason: "Use `page` instead.")

  """List of the shop's pages."""
  pages(
    """Returns up to the first `n` elements from the list."""
    first: Int

    """Returns the elements that come after the specified cursor."""
    after: String

    """Returns up to the last `n` elements from the list."""
    last: Int

    """Returns the elements that come before the specified cursor."""
    before: String

    """Reverse the order of the underlying list."""
    reverse: Boolean = false

    """Sort the underlying list by the given key."""
    sortKey: PageSortKeys = ID

    "Apply one or multiple filters to the query.\n| name | description | acceptable_values | default_value | example_use |\n| ---- | ---- | ---- | ---- | ---- |\n| created_at |\n| handle |\n| title |\n| updated_at |\nRefer to the detailed [search syntax](https://shopify.dev/api/usage/search-syntax) for more information about using filters.\n"
    query: String
  ): PageConnection!

  """Settings related to payments."""
  paymentSettings: PaymentSettings!

  """List of the predictive search results."""
  predictiveSearch(
    """
    Limits the number of results based on `limit_scope`. The value can range from 1 to 10, and the default is 10.
    """
    limit: Int

    """Decides the distribution of results."""
    limitScope: PredictiveSearchLimitScope

    """The search query."""
    query: String!

    """
    Specifies the list of resource fields to use for search. The default fields searched on are TITLE, PRODUCT_TYPE, VARIANT_TITLE, and VENDOR. For the best search experience, you should search on the default field set.
    
    The input must not contain more than `250` values.
    """
    searchableFields: [SearchableField!]

    """
    The types of resources to search for.
    
    The input must not contain more than `250` values.
    """
    types: [PredictiveSearchType!]

    """
    Specifies how unavailable products are displayed in the search results.
    """
    unavailableProducts: SearchUnavailableProductsType
  ): PredictiveSearchResult

  """Fetch a specific `Product` by one of its unique attributes."""
  product(
    """The ID of the `Product`."""
    id: ID

    """The handle of the `Product`."""
    handle: String
  ): Product

  """Find a product by its handle."""
  productByHandle(
    "A unique, human-readable string of the product's title.\nA handle can contain letters, hyphens (`-`), and numbers, but no spaces.\nThe handle is used in the online store URL for the product.\n"
    handle: String!
  ): Product @deprecated(reason: "Use `product` instead.")

  "Find recommended products related to a given `product_id`.\nTo learn more about how recommendations are generated, see\n[*Showing product recommendations on product pages*](https://help.shopify.com/themes/development/recommended-products).\n"
  productRecommendations(
    """The id of the product."""
    productId: ID

    """The handle of the product."""
    productHandle: String

    """
    The recommendation intent that is used to generate product recommendations. You can use intent to generate product recommendations on various pages across the channels, according to different strategies.
    """
    intent: ProductRecommendationIntent = RELATED
  ): [Product!]

  "Tags added to products.\nAdditional access scope required: unauthenticated_read_product_tags.\n"
  productTags(
    """Returns up to the first `n` elements from the list."""
    first: Int!
  ): StringConnection!

  """
  List of product types for the shop's products that are published to your app.
  """
  productTypes(
    """Returns up to the first `n` elements from the list."""
    first: Int!
  ): StringConnection!

  """
  Returns a list of the shop's products. For storefront search, use the [`search`](https://shopify.dev/docs/api/storefront/latest/queries/search) query.
  """
  products(
    """Returns up to the first `n` elements from the list."""
    first: Int

    """Returns the elements that come after the specified cursor."""
    after: String

    """Returns up to the last `n` elements from the list."""
    last: Int

    """Returns the elements that come before the specified cursor."""
    before: String

    """Reverse the order of the underlying list."""
    reverse: Boolean = false

    """Sort the underlying list by the given key."""
    sortKey: ProductSortKeys = ID

    "You can apply one or multiple filters to a query.\n| name | description | acceptable_values | default_value | example_use |\n| ---- | ---- | ---- | ---- | ---- |\n| available_for_sale | Filter by products that have at least one product variant available for sale. |\n| created_at | Filter by the date and time when the product was created. | | | - `created_at:>'2020-10-21T23:39:20Z'`<br/> - `created_at:<now`<br/> - `created_at:<=2024` |\n| product_type | Filter by a comma-separated list of [product types](https://help.shopify.com/en/manual/products/details/product-type). | | | `product_type:snowboard` |\n| tag | Filter products by the product [`tags`](https://shopify.dev/docs/api/storefront/latest/objects/Product#field-tags) field. | | | `tag:my_tag` |\n| tag_not | Filter by products that don't have the specified product [tags](https://shopify.dev/docs/api/storefront/latest/objects/Product#field-tags). | | | `tag_not:my_tag` |\n| title | Filter by the product [`title`](https://shopify.dev/docs/api/storefront/latest/objects/Product#field-title) field. | | | `title:The Minimal Snowboard` |\n| updated_at | Filter by the date and time when the product was last updated. | | | - `updated_at:>'2020-10-21T23:39:20Z'`<br/> - `updated_at:<now`<br/> - `updated_at:<=2024` |\n| variants.price | Filter by the price of the product's variants. |\n| vendor | Filter by the product [`vendor`](https://shopify.dev/docs/api/storefront/latest/objects/Product#field-vendor) field. | | | - `vendor:Snowdevil`<br/> - `vendor:Snowdevil OR vendor:Icedevil` |\nLearn more about [Shopify API search syntax](https://shopify.dev/api/usage/search-syntax).\n"
    query: String
  ): ProductConnection!

  """
  The list of public Storefront API versions, including supported, release candidate and unstable versions.
  """
  publicApiVersions: [ApiVersion!]!

  """List of the search results."""
  search(
    """Returns up to the first `n` elements from the list."""
    first: Int

    """Returns the elements that come after the specified cursor."""
    after: String

    """Returns up to the last `n` elements from the list."""
    last: Int

    """Returns the elements that come before the specified cursor."""
    before: String

    """Reverse the order of the underlying list."""
    reverse: Boolean = false

    """Sort the underlying list by the given key."""
    sortKey: SearchSortKeys = RELEVANCE

    """The search query."""
    query: String!

    """
    Specifies whether to perform a partial word match on the last search term.
    """
    prefix: SearchPrefixQueryType

    """
    Returns a subset of products matching all product filters.
    
    The input must not contain more than `250` values.
    """
    productFilters: [ProductFilter!]

    """
    The types of resources to search for.
    
    The input must not contain more than `250` values.
    """
    types: [SearchType!]

    """
    Specifies how unavailable products or variants are displayed in the search results.
    """
    unavailableProducts: SearchUnavailableProductsType
  ): SearchResultItemConnection!

  """The shop associated with the storefront access token."""
  shop: Shop!

  """Contains all fields required to generate sitemaps."""
  sitemap(
    """The type of the resource for the sitemap."""
    type: SitemapType!
  ): Sitemap!

  """A list of redirects for a shop."""
  urlRedirects(
    """Returns up to the first `n` elements from the list."""
    first: Int

    """Returns the elements that come after the specified cursor."""
    after: String

    """Returns up to the last `n` elements from the list."""
    last: Int

    """Returns the elements that come before the specified cursor."""
    before: String

    """Reverse the order of the underlying list."""
    reverse: Boolean = false

    "Apply one or multiple filters to the query.\n| name | description | acceptable_values | default_value | example_use |\n| ---- | ---- | ---- | ---- | ---- |\n| created_at |\n| path |\n| target |\nRefer to the detailed [search syntax](https://shopify.dev/api/usage/search-syntax) for more information about using filters.\n"
    query: String
  ): UrlRedirectConnection!
}

"""SEO information."""
type SEO {
  """The meta description."""
  description: String

  """The SEO title."""
  title: String
}

"Script discount applications capture the intentions of a discount that\nwas created by a Shopify Script.\n"
type ScriptDiscountApplication implements DiscountApplication {
  """
  The method by which the discount's value is allocated to its entitled items.
  """
  allocationMethod: DiscountApplicationAllocationMethod!

  """Which lines of targetType that the discount is allocated over."""
  targetSelection: DiscountApplicationTargetSelection!

  """The type of line that the discount is applicable towards."""
  targetType: DiscountApplicationTargetType!

  """The title of the application as defined by the Script."""
  title: String!

  """The value of the discount application."""
  value: PricingValue!
}

"""
Specifies whether to perform a partial word match on the last search term.
"""
enum SearchPrefixQueryType {
  """Perform a partial word match on the last search term."""
  LAST

  """Don't perform a partial word match on the last search term."""
  NONE
}

"""A search query suggestion."""
type SearchQuerySuggestion implements Trackable {
  """The text of the search query suggestion with highlighted HTML tags."""
  styledText: String!

  """The text of the search query suggestion."""
  text: String!

  """
  URL parameters to be added to a page URL to track the origin of on-site search traffic for [analytics reporting](https://help.shopify.com/manual/reports-and-analytics/shopify-reports/report-types/default-reports/behaviour-reports). Returns a result when accessed through the [search](https://shopify.dev/docs/api/storefront/current/queries/search) or [predictiveSearch](https://shopify.dev/docs/api/storefront/current/queries/predictiveSearch) queries, otherwise returns null.
  """
  trackingParameters: String
}

"A search result that matches the search query.\n"
union SearchResultItem = Article | Page | Product

"An auto-generated type for paginating through multiple SearchResultItems.\n"
type SearchResultItemConnection {
  """A list of edges."""
  edges: [SearchResultItemEdge!]!

  """A list of the nodes contained in SearchResultItemEdge."""
  nodes: [SearchResultItem!]!

  """Information to aid in pagination."""
  pageInfo: PageInfo!

  """A list of available filters."""
  productFilters: [Filter!]!

  """The total number of results."""
  totalCount: Int!
}

"An auto-generated type which holds one SearchResultItem and a cursor during pagination.\n"
type SearchResultItemEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of SearchResultItemEdge."""
  node: SearchResultItem!
}

"""The set of valid sort keys for the search query."""
enum SearchSortKeys {
  """Sort by the `price` value."""
  PRICE

  """Sort by relevance to the search terms."""
  RELEVANCE
}

"""The types of search items to perform search within."""
enum SearchType {
  """Returns matching products."""
  PRODUCT

  """Returns matching pages."""
  PAGE

  """Returns matching articles."""
  ARTICLE
}

"""Specifies whether to display results for unavailable products."""
enum SearchUnavailableProductsType {
  """Show unavailable products in the order that they're found."""
  SHOW

  """Exclude unavailable products."""
  HIDE

  """
  Show unavailable products after all other matching results. This is the default.
  """
  LAST
}

"""Specifies the list of resource fields to search."""
enum SearchableField {
  """Author of the page or article."""
  AUTHOR

  """
  Body of the page or article or product description or collection description.
  """
  BODY

  """Product type."""
  PRODUCT_TYPE

  """Tag associated with the product or article."""
  TAG

  """Title of the page or article or product title or collection title."""
  TITLE

  """Variant barcode."""
  VARIANTS_BARCODE

  """Variant SKU."""
  VARIANTS_SKU

  """Variant title."""
  VARIANTS_TITLE

  """Product vendor."""
  VENDOR
}

"Properties used by customers to select a product variant.\nProducts can have multiple options, like different sizes or colors.\n"
type SelectedOption {
  """The product option’s name."""
  name: String!

  """The product option’s value."""
  value: String!
}

"""The input fields required for a selected option."""
input SelectedOptionInput {
  """The product option’s name."""
  name: String!

  """The product option’s value."""
  value: String!
}

"""Represents how products and variants can be sold and purchased."""
type SellingPlan implements HasMetafields {
  """The billing policy for the selling plan."""
  billingPolicy: SellingPlanBillingPolicy

  """The initial payment due for the purchase."""
  checkoutCharge: SellingPlanCheckoutCharge!

  """The delivery policy for the selling plan."""
  deliveryPolicy: SellingPlanDeliveryPolicy

  """The description of the selling plan."""
  description: String

  """A globally-unique ID."""
  id: ID!

  """
  A [custom field](https://shopify.dev/docs/apps/build/custom-data), including its `namespace` and `key`, that's associated with a Shopify resource for the purposes of adding and storing additional information.
  """
  metafield(
    """
    The container the metafield belongs to. If omitted, the app-reserved namespace will be used.
    """
    namespace: String

    """The identifier for the metafield."""
    key: String!
  ): Metafield

  """
  A list of [custom fields](/docs/apps/build/custom-data) that a merchant associates with a Shopify resource.
  """
  metafields(
    """
    The list of metafields to retrieve by namespace and key.
    
    The input must not contain more than `250` values.
    """
    identifiers: [HasMetafieldsIdentifier!]!
  ): [Metafield]!

  """
  The name of the selling plan. For example, '6 weeks of prepaid granola, delivered weekly'.
  """
  name: String!

  """
  The selling plan options available in the drop-down list in the storefront. For example, 'Delivery every week' or 'Delivery every 2 weeks' specifies the delivery frequency options for the product. Individual selling plans contribute their options to the associated selling plan group. For example, a selling plan group might have an option called `option1: Delivery every`. One selling plan in that group could contribute `option1: 2 weeks` with the pricing for that option, and another selling plan could contribute `option1: 4 weeks`, with different pricing.
  """
  options: [SellingPlanOption!]!

  """
  The price adjustments that a selling plan makes when a variant is purchased with a selling plan.
  """
  priceAdjustments: [SellingPlanPriceAdjustment!]!

  """
  Whether purchasing the selling plan will result in multiple deliveries.
  """
  recurringDeliveries: Boolean!
}

"""
Represents an association between a variant and a selling plan. Selling plan allocations describe the options offered for each variant, and the price of the variant when purchased with a selling plan.
"""
type SellingPlanAllocation {
  """The checkout charge amount due for the purchase."""
  checkoutChargeAmount: MoneyV2!

  """
  A list of price adjustments, with a maximum of two. When there are two, the first price adjustment goes into effect at the time of purchase, while the second one starts after a certain number of orders. A price adjustment represents how a selling plan affects pricing when a variant is purchased with a selling plan. Prices display in the customer's currency if the shop is configured for it.
  """
  priceAdjustments: [SellingPlanAllocationPriceAdjustment!]!

  """The remaining balance charge amount due for the purchase."""
  remainingBalanceChargeAmount: MoneyV2!

  """
  A representation of how products and variants can be sold and purchased. For example, an individual selling plan could be '6 weeks of prepaid granola, delivered weekly'.
  """
  sellingPlan: SellingPlan!
}

"An auto-generated type for paginating through multiple SellingPlanAllocations.\n"
type SellingPlanAllocationConnection {
  """A list of edges."""
  edges: [SellingPlanAllocationEdge!]!

  """A list of the nodes contained in SellingPlanAllocationEdge."""
  nodes: [SellingPlanAllocation!]!

  """Information to aid in pagination."""
  pageInfo: PageInfo!
}

"An auto-generated type which holds one SellingPlanAllocation and a cursor during pagination.\n"
type SellingPlanAllocationEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of SellingPlanAllocationEdge."""
  node: SellingPlanAllocation!
}

"""
The resulting prices for variants when they're purchased with a specific selling plan.
"""
type SellingPlanAllocationPriceAdjustment {
  """
  The price of the variant when it's purchased without a selling plan for the same number of deliveries. For example, if a customer purchases 6 deliveries of $10.00 granola separately, then the price is 6 x $10.00 = $60.00.
  """
  compareAtPrice: MoneyV2!

  """
  The effective price for a single delivery. For example, for a prepaid subscription plan that includes 6 deliveries at the price of $48.00, the per delivery price is $8.00.
  """
  perDeliveryPrice: MoneyV2!

  """
  The price of the variant when it's purchased with a selling plan For example, for a prepaid subscription plan that includes 6 deliveries of $10.00 granola, where the customer gets 20% off, the price is 6 x $10.00 x 0.80 = $48.00.
  """
  price: MoneyV2!

  """
  The resulting price per unit for the variant associated with the selling plan. If the variant isn't sold by quantity or measurement, then this field returns `null`.
  """
  unitPrice: MoneyV2
}

"""The selling plan billing policy."""
union SellingPlanBillingPolicy = SellingPlanRecurringBillingPolicy

"""The initial payment due for the purchase."""
type SellingPlanCheckoutCharge {
  """The charge type for the checkout charge."""
  type: SellingPlanCheckoutChargeType!

  """The charge value for the checkout charge."""
  value: SellingPlanCheckoutChargeValue!
}

"""The percentage value of the price used for checkout charge."""
type SellingPlanCheckoutChargePercentageValue {
  """The percentage value of the price used for checkout charge."""
  percentage: Float!
}

"""The checkout charge when the full amount isn't charged at checkout."""
enum SellingPlanCheckoutChargeType {
  """The checkout charge is a percentage of the product or variant price."""
  PERCENTAGE

  """The checkout charge is a fixed price amount."""
  PRICE
}

"""The portion of the price to be charged at checkout."""
union SellingPlanCheckoutChargeValue = MoneyV2 | SellingPlanCheckoutChargePercentageValue

"An auto-generated type for paginating through multiple SellingPlans.\n"
type SellingPlanConnection {
  """A list of edges."""
  edges: [SellingPlanEdge!]!

  """A list of the nodes contained in SellingPlanEdge."""
  nodes: [SellingPlan!]!

  """Information to aid in pagination."""
  pageInfo: PageInfo!
}

"""The selling plan delivery policy."""
union SellingPlanDeliveryPolicy = SellingPlanRecurringDeliveryPolicy

"An auto-generated type which holds one SellingPlan and a cursor during pagination.\n"
type SellingPlanEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of SellingPlanEdge."""
  node: SellingPlan!
}

"""
A fixed amount that's deducted from the original variant price. For example, $10.00 off.
"""
type SellingPlanFixedAmountPriceAdjustment {
  """The money value of the price adjustment."""
  adjustmentAmount: MoneyV2!
}

"""
A fixed price adjustment for a variant that's purchased with a selling plan.
"""
type SellingPlanFixedPriceAdjustment {
  """A new price of the variant when it's purchased with the selling plan."""
  price: MoneyV2!
}

"""
Represents a selling method. For example, 'Subscribe and save' is a selling method where customers pay for goods or services per delivery. A selling plan group contains individual selling plans.
"""
type SellingPlanGroup {
  """
  A display friendly name for the app that created the selling plan group.
  """
  appName: String

  """The name of the selling plan group."""
  name: String!

  """
  Represents the selling plan options available in the drop-down list in the storefront. For example, 'Delivery every week' or 'Delivery every 2 weeks' specifies the delivery frequency options for the product.
  """
  options: [SellingPlanGroupOption!]!

  """
  A list of selling plans in a selling plan group. A selling plan is a representation of how products and variants can be sold and purchased. For example, an individual selling plan could be '6 weeks of prepaid granola, delivered weekly'.
  """
  sellingPlans(
    """Returns up to the first `n` elements from the list."""
    first: Int

    """Returns the elements that come after the specified cursor."""
    after: String

    """Returns up to the last `n` elements from the list."""
    last: Int

    """Returns the elements that come before the specified cursor."""
    before: String

    """Reverse the order of the underlying list."""
    reverse: Boolean = false
  ): SellingPlanConnection!
}

"An auto-generated type for paginating through multiple SellingPlanGroups.\n"
type SellingPlanGroupConnection {
  """A list of edges."""
  edges: [SellingPlanGroupEdge!]!

  """A list of the nodes contained in SellingPlanGroupEdge."""
  nodes: [SellingPlanGroup!]!

  """Information to aid in pagination."""
  pageInfo: PageInfo!
}

"An auto-generated type which holds one SellingPlanGroup and a cursor during pagination.\n"
type SellingPlanGroupEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of SellingPlanGroupEdge."""
  node: SellingPlanGroup!
}

"""
Represents an option on a selling plan group that's available in the drop-down list in the storefront.

Individual selling plans contribute their options to the associated selling plan group. For example, a selling plan group might have an option called `option1: Delivery every`. One selling plan in that group could contribute `option1: 2 weeks` with the pricing for that option, and another selling plan could contribute `option1: 4 weeks`, with different pricing.
"""
type SellingPlanGroupOption {
  """The name of the option. For example, 'Delivery every'."""
  name: String!

  """
  The values for the options specified by the selling plans in the selling plan group. For example, '1 week', '2 weeks', '3 weeks'.
  """
  values: [String!]!
}

"""Represents a valid selling plan interval."""
enum SellingPlanInterval {
  """Day interval."""
  DAY

  """Month interval."""
  MONTH

  """Week interval."""
  WEEK

  """Year interval."""
  YEAR
}

"""An option provided by a Selling Plan."""
type SellingPlanOption {
  """The name of the option (ie "Delivery every")."""
  name: String

  """The value of the option (ie "Month")."""
  value: String
}

"""
A percentage amount that's deducted from the original variant price. For example, 10% off.
"""
type SellingPlanPercentagePriceAdjustment {
  """The percentage value of the price adjustment."""
  adjustmentPercentage: Int!
}

"""
Represents by how much the price of a variant associated with a selling plan is adjusted. Each variant can have up to two price adjustments. If a variant has multiple price adjustments, then the first price adjustment applies when the variant is initially purchased. The second price adjustment applies after a certain number of orders (specified by the `orderCount` field) are made. If a selling plan doesn't have any price adjustments, then the unadjusted price of the variant is the effective price.
"""
type SellingPlanPriceAdjustment {
  """
  The type of price adjustment. An adjustment value can have one of three types: percentage, amount off, or a new price.
  """
  adjustmentValue: SellingPlanPriceAdjustmentValue!

  """
  The number of orders that the price adjustment applies to. If the price adjustment always applies, then this field is `null`.
  """
  orderCount: Int
}

"""
Represents by how much the price of a variant associated with a selling plan is adjusted. Each variant can have up to two price adjustments.
"""
union SellingPlanPriceAdjustmentValue = SellingPlanFixedAmountPriceAdjustment | SellingPlanFixedPriceAdjustment | SellingPlanPercentagePriceAdjustment

"""The recurring billing policy for the selling plan."""
type SellingPlanRecurringBillingPolicy {
  """The billing frequency, it can be either: day, week, month or year."""
  interval: SellingPlanInterval!

  """The number of intervals between billings."""
  intervalCount: Int!
}

"""The recurring delivery policy for the selling plan."""
type SellingPlanRecurringDeliveryPolicy {
  """The delivery frequency, it can be either: day, week, month or year."""
  interval: SellingPlanInterval!

  """The number of intervals between deliveries."""
  intervalCount: Int!
}

"""
Shop represents a collection of the general settings and information about the shop.
"""
type Shop implements HasMetafields & Node {
  """The shop's branding configuration."""
  brand: Brand

  """A description of the shop."""
  description: String

  """A globally-unique ID."""
  id: ID!

  """
  A [custom field](https://shopify.dev/docs/apps/build/custom-data), including its `namespace` and `key`, that's associated with a Shopify resource for the purposes of adding and storing additional information.
  """
  metafield(
    """
    The container the metafield belongs to. If omitted, the app-reserved namespace will be used.
    """
    namespace: String

    """The identifier for the metafield."""
    key: String!
  ): Metafield

  """
  A list of [custom fields](/docs/apps/build/custom-data) that a merchant associates with a Shopify resource.
  """
  metafields(
    """
    The list of metafields to retrieve by namespace and key.
    
    The input must not contain more than `250` values.
    """
    identifiers: [HasMetafieldsIdentifier!]!
  ): [Metafield]!

  """
  A string representing the way currency is formatted when the currency isn’t specified.
  """
  moneyFormat: String!

  """The shop’s name."""
  name: String!

  """Settings related to payments."""
  paymentSettings: PaymentSettings!

  """The primary domain of the shop’s Online Store."""
  primaryDomain: Domain!

  """The shop’s privacy policy."""
  privacyPolicy: ShopPolicy

  """The shop’s refund policy."""
  refundPolicy: ShopPolicy

  """The shop’s shipping policy."""
  shippingPolicy: ShopPolicy

  """Countries that the shop ships to."""
  shipsToCountries: [CountryCode!]!

  """The Shop Pay Installments pricing information for the shop."""
  shopPayInstallmentsPricing: ShopPayInstallmentsPricing

  """The shop’s subscription policy."""
  subscriptionPolicy: ShopPolicyWithDefault

  """The shop’s terms of service."""
  termsOfService: ShopPolicy
}

"""The financing plan in Shop Pay Installments."""
type ShopPayInstallmentsFinancingPlan implements Node {
  """A globally-unique ID."""
  id: ID!

  """The maximum price to qualify for the financing plan."""
  maxPrice: MoneyV2!

  """The minimum price to qualify for the financing plan."""
  minPrice: MoneyV2!

  """The terms of the financing plan."""
  terms: [ShopPayInstallmentsFinancingPlanTerm!]!
}

"""The payment frequency for a Shop Pay Installments Financing Plan."""
enum ShopPayInstallmentsFinancingPlanFrequency {
  """Weekly payment frequency."""
  WEEKLY

  """Monthly payment frequency."""
  MONTHLY
}

"""The terms of the financing plan in Shop Pay Installments."""
type ShopPayInstallmentsFinancingPlanTerm implements Node {
  """The annual percentage rate (APR) of the financing plan."""
  apr: Int!

  """The payment frequency for the financing plan."""
  frequency: ShopPayInstallmentsFinancingPlanFrequency!

  """A globally-unique ID."""
  id: ID!

  """The number of installments for the financing plan."""
  installmentsCount: Count

  """The type of loan for the financing plan."""
  loanType: ShopPayInstallmentsLoan!
}

"""The loan type for a Shop Pay Installments Financing Plan Term."""
enum ShopPayInstallmentsLoan {
  """An interest-bearing loan type."""
  INTEREST

  """A split-pay loan type."""
  SPLIT_PAY

  """A zero-percent loan type."""
  ZERO_PERCENT
}

"""The result for a Shop Pay Installments pricing request."""
type ShopPayInstallmentsPricing {
  """The financing plans available for the given price range."""
  financingPlans: [ShopPayInstallmentsFinancingPlan!]!

  """The maximum price to qualify for financing."""
  maxPrice: MoneyV2!

  """The minimum price to qualify for financing."""
  minPrice: MoneyV2!
}

"""The shop pay installments pricing information for a product variant."""
type ShopPayInstallmentsProductVariantPricing implements Node {
  """Whether the product variant is available."""
  available: Boolean!

  """Whether the product variant is eligible for Shop Pay Installments."""
  eligible: Boolean!

  """The full price of the product variant."""
  fullPrice: MoneyV2!

  """The ID of the product variant."""
  id: ID!

  """The number of payment terms available for the product variant."""
  installmentsCount: Count

  """The price per term for the product variant."""
  pricePerTerm: MoneyV2!
}

"""Represents a Shop Pay payment request."""
type ShopPayPaymentRequest {
  """The delivery methods for the payment request."""
  deliveryMethods: [ShopPayPaymentRequestDeliveryMethod!]!

  """The discount codes for the payment request."""
  discountCodes: [String!]!

  """The discounts for the payment request order."""
  discounts: [ShopPayPaymentRequestDiscount!]

  """The line items for the payment request."""
  lineItems: [ShopPayPaymentRequestLineItem!]!

  """The locale for the payment request."""
  locale: String!

  """The presentment currency for the payment request."""
  presentmentCurrency: CurrencyCode!

  """The delivery method type for the payment request."""
  selectedDeliveryMethodType: ShopPayPaymentRequestDeliveryMethodType!

  """The shipping address for the payment request."""
  shippingAddress: ShopPayPaymentRequestContactField

  """The shipping lines for the payment request."""
  shippingLines: [ShopPayPaymentRequestShippingLine!]!

  """The subtotal amount for the payment request."""
  subtotal: MoneyV2!

  """The total amount for the payment request."""
  total: MoneyV2!

  """The total shipping price for the payment request."""
  totalShippingPrice: ShopPayPaymentRequestTotalShippingPrice

  """The total tax for the payment request."""
  totalTax: MoneyV2
}

"""Represents a contact field for a Shop Pay payment request."""
type ShopPayPaymentRequestContactField {
  """The first address line of the contact field."""
  address1: String!

  """The second address line of the contact field."""
  address2: String

  """The city of the contact field."""
  city: String!

  """The company name of the contact field."""
  companyName: String

  """The country of the contact field."""
  countryCode: String!

  """The email of the contact field."""
  email: String

  """The first name of the contact field."""
  firstName: String!

  """The first name of the contact field."""
  lastName: String!

  """The phone number of the contact field."""
  phone: String

  """The postal code of the contact field."""
  postalCode: String

  """The province of the contact field."""
  provinceCode: String
}

"""Represents a delivery method for a Shop Pay payment request."""
type ShopPayPaymentRequestDeliveryMethod {
  """The amount for the delivery method."""
  amount: MoneyV2!

  """The code of the delivery method."""
  code: String!

  """The detail about when the delivery may be expected."""
  deliveryExpectationLabel: String

  """The detail of the delivery method."""
  detail: String

  """The label of the delivery method."""
  label: String!

  """The maximum delivery date for the delivery method."""
  maxDeliveryDate: ISO8601DateTime

  """The minimum delivery date for the delivery method."""
  minDeliveryDate: ISO8601DateTime
}

"""
The input fields to create a delivery method for a Shop Pay payment request.
"""
input ShopPayPaymentRequestDeliveryMethodInput {
  """The code of the delivery method."""
  code: String

  """The label of the delivery method."""
  label: String

  """The detail of the delivery method."""
  detail: String

  """The amount for the delivery method."""
  amount: MoneyInput

  """The minimum delivery date for the delivery method."""
  minDeliveryDate: ISO8601DateTime

  """The maximum delivery date for the delivery method."""
  maxDeliveryDate: ISO8601DateTime

  """The detail about when the delivery may be expected."""
  deliveryExpectationLabel: String
}

"""Represents the delivery method type for a Shop Pay payment request."""
enum ShopPayPaymentRequestDeliveryMethodType {
  """The delivery method type is shipping."""
  SHIPPING

  """The delivery method type is pickup."""
  PICKUP
}

"""Represents a discount for a Shop Pay payment request."""
type ShopPayPaymentRequestDiscount {
  """The amount of the discount."""
  amount: MoneyV2!

  """The label of the discount."""
  label: String!
}

"""The input fields to create a discount for a Shop Pay payment request."""
input ShopPayPaymentRequestDiscountInput {
  """The label of the discount."""
  label: String

  """The amount of the discount."""
  amount: MoneyInput
}

"""Represents an image for a Shop Pay payment request line item."""
type ShopPayPaymentRequestImage {
  """The alt text of the image."""
  alt: String

  """The source URL of the image."""
  url: String!
}

"""The input fields to create an image for a Shop Pay payment request."""
input ShopPayPaymentRequestImageInput {
  """The source URL of the image."""
  url: String!

  """The alt text of the image."""
  alt: String
}

"""The input fields represent a Shop Pay payment request."""
input ShopPayPaymentRequestInput {
  """
  The discount codes for the payment request.
  
  The input must not contain more than `250` values.
  """
  discountCodes: [String!]

  """
  The line items for the payment request.
  
  The input must not contain more than `250` values.
  """
  lineItems: [ShopPayPaymentRequestLineItemInput!]

  """
  The shipping lines for the payment request.
  
  The input must not contain more than `250` values.
  """
  shippingLines: [ShopPayPaymentRequestShippingLineInput!]

  """The total amount for the payment request."""
  total: MoneyInput!

  """The subtotal amount for the payment request."""
  subtotal: MoneyInput!

  """
  The discounts for the payment request order.
  
  The input must not contain more than `250` values.
  """
  discounts: [ShopPayPaymentRequestDiscountInput!]

  """The total shipping price for the payment request."""
  totalShippingPrice: ShopPayPaymentRequestTotalShippingPriceInput

  """The total tax for the payment request."""
  totalTax: MoneyInput

  """
  The delivery methods for the payment request.
  
  The input must not contain more than `250` values.
  """
  deliveryMethods: [ShopPayPaymentRequestDeliveryMethodInput!]

  """The delivery method type for the payment request."""
  selectedDeliveryMethodType: ShopPayPaymentRequestDeliveryMethodType

  """The locale for the payment request."""
  locale: String!

  """The presentment currency for the payment request."""
  presentmentCurrency: CurrencyCode!

  """The encrypted payment method for the payment request."""
  paymentMethod: String
}

"""Represents a line item for a Shop Pay payment request."""
type ShopPayPaymentRequestLineItem {
  """The final item price for the line item."""
  finalItemPrice: MoneyV2!

  """The final line price for the line item."""
  finalLinePrice: MoneyV2!

  """The image of the line item."""
  image: ShopPayPaymentRequestImage

  """The item discounts for the line item."""
  itemDiscounts: [ShopPayPaymentRequestDiscount!]

  """The label of the line item."""
  label: String!

  """The line discounts for the line item."""
  lineDiscounts: [ShopPayPaymentRequestDiscount!]

  """The original item price for the line item."""
  originalItemPrice: MoneyV2

  """The original line price for the line item."""
  originalLinePrice: MoneyV2

  """The quantity of the line item."""
  quantity: Int!

  """Whether the line item requires shipping."""
  requiresShipping: Boolean

  """The SKU of the line item."""
  sku: String
}

"""The input fields to create a line item for a Shop Pay payment request."""
input ShopPayPaymentRequestLineItemInput {
  """The label of the line item."""
  label: String

  """The quantity of the line item."""
  quantity: Int!

  """The SKU of the line item."""
  sku: String

  """Whether the line item requires shipping."""
  requiresShipping: Boolean

  """The image of the line item."""
  image: ShopPayPaymentRequestImageInput

  """The original line price for the line item."""
  originalLinePrice: MoneyInput

  """The final line price for the line item."""
  finalLinePrice: MoneyInput

  """
  The line discounts for the line item.
  
  The input must not contain more than `250` values.
  """
  lineDiscounts: [ShopPayPaymentRequestDiscountInput!]

  """The original item price for the line item."""
  originalItemPrice: MoneyInput

  """The final item price for the line item."""
  finalItemPrice: MoneyInput

  """
  The item discounts for the line item.
  
  The input must not contain more than `250` values.
  """
  itemDiscounts: [ShopPayPaymentRequestDiscountInput!]
}

"""Represents a receipt for a Shop Pay payment request."""
type ShopPayPaymentRequestReceipt {
  """The payment request object."""
  paymentRequest: ShopPayPaymentRequest!

  """The processing status."""
  processingStatusType: String!

  """The token of the receipt."""
  token: String!
}

"""Represents a Shop Pay payment request session."""
type ShopPayPaymentRequestSession {
  """The checkout URL of the Shop Pay payment request session."""
  checkoutUrl: URL!

  """
  The payment request associated with the Shop Pay payment request session.
  """
  paymentRequest: ShopPayPaymentRequest!

  """The source identifier of the Shop Pay payment request session."""
  sourceIdentifier: String!

  """The token of the Shop Pay payment request session."""
  token: String!
}

"""Return type for `shopPayPaymentRequestSessionCreate` mutation."""
type ShopPayPaymentRequestSessionCreatePayload {
  """The new Shop Pay payment request session object."""
  shopPayPaymentRequestSession: ShopPayPaymentRequestSession

  """Error codes for failed Shop Pay payment request session mutations."""
  userErrors: [UserErrorsShopPayPaymentRequestSessionUserErrors!]!
}

"""Return type for `shopPayPaymentRequestSessionSubmit` mutation."""
type ShopPayPaymentRequestSessionSubmitPayload {
  """The checkout on which the payment was applied."""
  paymentRequestReceipt: ShopPayPaymentRequestReceipt

  """Error codes for failed Shop Pay payment request session mutations."""
  userErrors: [UserErrorsShopPayPaymentRequestSessionUserErrors!]!
}

"""Represents a shipping line for a Shop Pay payment request."""
type ShopPayPaymentRequestShippingLine {
  """The amount for the shipping line."""
  amount: MoneyV2!

  """The code of the shipping line."""
  code: String!

  """The label of the shipping line."""
  label: String!
}

"""
The input fields to create a shipping line for a Shop Pay payment request.
"""
input ShopPayPaymentRequestShippingLineInput {
  """The code of the shipping line."""
  code: String

  """The label of the shipping line."""
  label: String

  """The amount for the shipping line."""
  amount: MoneyInput
}

"""Represents a shipping total for a Shop Pay payment request."""
type ShopPayPaymentRequestTotalShippingPrice {
  """The discounts for the shipping total."""
  discounts: [ShopPayPaymentRequestDiscount!]!

  """The final total for the shipping total."""
  finalTotal: MoneyV2!

  """The original total for the shipping total."""
  originalTotal: MoneyV2
}

"""
The input fields to create a shipping total for a Shop Pay payment request.
"""
input ShopPayPaymentRequestTotalShippingPriceInput {
  """
  The discounts for the shipping total.
  
  The input must not contain more than `250` values.
  """
  discounts: [ShopPayPaymentRequestDiscountInput!]

  """The original total for the shipping total."""
  originalTotal: MoneyInput

  """The final total for the shipping total."""
  finalTotal: MoneyInput
}

"The input fields for submitting Shop Pay payment method information for checkout.\n"
input ShopPayWalletContentInput {
  """The customer's billing address."""
  billingAddress: MailingAddressInput!

  """Session token for transaction."""
  sessionToken: String!
}

"""
Policy that a merchant has configured for their store, such as their refund or privacy policy.
"""
type ShopPolicy implements Node {
  """Policy text, maximum size of 64kb."""
  body: String!

  """Policy’s handle."""
  handle: String!

  """A globally-unique ID."""
  id: ID!

  """Policy’s title."""
  title: String!

  """Public URL to the policy."""
  url: URL!
}

"A policy for the store that comes with a default value, such as a subscription policy.\nIf the merchant hasn't configured a policy for their store, then the policy will return the default value.\nOtherwise, the policy will return the merchant-configured value.\n"
type ShopPolicyWithDefault {
  """The text of the policy. Maximum size: 64KB."""
  body: String!

  """The handle of the policy."""
  handle: String!

  """The unique ID of the policy. A default policy doesn't have an ID."""
  id: ID

  """The title of the policy."""
  title: String!

  """Public URL to the policy."""
  url: URL!
}

"""Contains all fields required to generate sitemaps."""
type Sitemap {
  """The number of sitemap's pages for a given type."""
  pagesCount: Count

  "A list of sitemap's resources for a given type.\n\nImportant Notes:\n  - The number of items per page varies from 0 to 250.\n  - Empty pages (0 items) may occur and do not necessarily indicate the end of results.\n  - Always check `hasNextPage` to determine if more pages are available.\n"
  resources(
    """The page number to fetch."""
    page: Int!
  ): PaginatedSitemapResources
}

"""Represents a sitemap's image."""
type SitemapImage {
  """Image's alt text."""
  alt: String

  """Path to the image."""
  filepath: String

  """The date and time when the image was updated."""
  updatedAt: DateTime!
}

"""Represents a sitemap resource that is not a metaobject."""
type SitemapResource implements SitemapResourceInterface {
  """Resource's handle."""
  handle: String!

  """Resource's image."""
  image: SitemapImage

  """Resource's title."""
  title: String

  """The date and time when the resource was updated."""
  updatedAt: DateTime!
}

"""Represents the common fields for all sitemap resource types."""
interface SitemapResourceInterface {
  """Resource's handle."""
  handle: String!

  """The date and time when the resource was updated."""
  updatedAt: DateTime!
}

"A SitemapResourceMetaobject represents a metaobject with\n[the `renderable` capability](https://shopify.dev/docs/apps/build/custom-data/metaobjects/use-metaobject-capabilities#render-metaobjects-as-web-pages).\n"
type SitemapResourceMetaobject implements SitemapResourceInterface {
  """Resource's handle."""
  handle: String!

  """
  The URL handle for accessing pages of this metaobject type in the Online Store.
  """
  onlineStoreUrlHandle: String

  """
  The type of the metaobject. Defines the namespace of its associated metafields.
  """
  type: String!

  """The date and time when the resource was updated."""
  updatedAt: DateTime!
}

"""The types of resources potentially present in a sitemap."""
enum SitemapType {
  """Products present in the sitemap."""
  PRODUCT

  """Collections present in the sitemap."""
  COLLECTION

  """Pages present in the sitemap."""
  PAGE

  "Metaobjects present in the sitemap. Only metaobject types with the\n[`renderable` capability](https://shopify.dev/docs/apps/build/custom-data/metaobjects/use-metaobject-capabilities#render-metaobjects-as-web-pages)\nare included in sitemap.\n"
  METAOBJECT

  """Blogs present in the sitemap."""
  BLOG

  """Articles present in the sitemap."""
  ARTICLE
}

"The availability of a product variant at a particular location.\nLocal pick-up must be enabled in the  store's shipping settings, otherwise this will return an empty result.\n"
type StoreAvailability {
  """Whether the product variant is in-stock at this location."""
  available: Boolean!

  """The location where this product variant is stocked at."""
  location: Location!

  """
  Returns the estimated amount of time it takes for pickup to be ready (Example: Usually ready in 24 hours).
  """
  pickUpTime: String!

  """The quantity of the product variant in-stock at this location."""
  quantityAvailable: Int!
}

"An auto-generated type for paginating through multiple StoreAvailabilities.\n"
type StoreAvailabilityConnection {
  """A list of edges."""
  edges: [StoreAvailabilityEdge!]!

  """A list of the nodes contained in StoreAvailabilityEdge."""
  nodes: [StoreAvailability!]!

  """Information to aid in pagination."""
  pageInfo: PageInfo!
}

"An auto-generated type which holds one StoreAvailability and a cursor during pagination.\n"
type StoreAvailabilityEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of StoreAvailabilityEdge."""
  node: StoreAvailability!
}

"An auto-generated type for paginating through multiple Strings.\n"
type StringConnection {
  """A list of edges."""
  edges: [StringEdge!]!

  """Information to aid in pagination."""
  pageInfo: PageInfo!
}

"An auto-generated type which holds one String and a cursor during pagination.\n"
type StringEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of StringEdge."""
  node: String!
}

"""An error that occurred during cart submit for completion."""
type SubmissionError {
  """The error code."""
  code: SubmissionErrorCode!

  """The error message."""
  message: String
}

"""The code of the error that occurred during cart submit for completion."""
enum SubmissionErrorCode {
  ERROR
  NO_DELIVERY_GROUP_SELECTED
  BUYER_IDENTITY_EMAIL_IS_INVALID
  BUYER_IDENTITY_EMAIL_REQUIRED
  BUYER_IDENTITY_PHONE_IS_INVALID
  DELIVERY_ADDRESS1_INVALID
  DELIVERY_ADDRESS1_REQUIRED
  DELIVERY_ADDRESS1_TOO_LONG
  DELIVERY_ADDRESS2_INVALID
  DELIVERY_ADDRESS2_REQUIRED
  DELIVERY_ADDRESS2_TOO_LONG
  DELIVERY_CITY_INVALID
  DELIVERY_CITY_REQUIRED
  DELIVERY_CITY_TOO_LONG
  DELIVERY_COMPANY_INVALID
  DELIVERY_COMPANY_REQUIRED
  DELIVERY_COMPANY_TOO_LONG
  DELIVERY_COUNTRY_REQUIRED
  DELIVERY_FIRST_NAME_INVALID
  DELIVERY_FIRST_NAME_REQUIRED
  DELIVERY_FIRST_NAME_TOO_LONG
  DELIVERY_INVALID_POSTAL_CODE_FOR_COUNTRY
  DELIVERY_INVALID_POSTAL_CODE_FOR_ZONE
  DELIVERY_LAST_NAME_INVALID
  DELIVERY_LAST_NAME_REQUIRED
  DELIVERY_LAST_NAME_TOO_LONG
  DELIVERY_NO_DELIVERY_AVAILABLE
  DELIVERY_NO_DELIVERY_AVAILABLE_FOR_MERCHANDISE_LINE
  DELIVERY_OPTIONS_PHONE_NUMBER_INVALID
  DELIVERY_OPTIONS_PHONE_NUMBER_REQUIRED
  DELIVERY_PHONE_NUMBER_INVALID
  DELIVERY_PHONE_NUMBER_REQUIRED
  DELIVERY_POSTAL_CODE_INVALID
  DELIVERY_POSTAL_CODE_REQUIRED
  DELIVERY_ZONE_NOT_FOUND
  DELIVERY_ZONE_REQUIRED_FOR_COUNTRY
  DELIVERY_ADDRESS_REQUIRED
  MERCHANDISE_NOT_APPLICABLE
  MERCHANDISE_LINE_LIMIT_REACHED
  MERCHANDISE_NOT_ENOUGH_STOCK_AVAILABLE
  MERCHANDISE_OUT_OF_STOCK
  MERCHANDISE_PRODUCT_NOT_PUBLISHED
  PAYMENTS_ADDRESS1_INVALID
  PAYMENTS_ADDRESS1_REQUIRED
  PAYMENTS_ADDRESS1_TOO_LONG
  PAYMENTS_ADDRESS2_INVALID
  PAYMENTS_ADDRESS2_REQUIRED
  PAYMENTS_ADDRESS2_TOO_LONG
  PAYMENTS_CITY_INVALID
  PAYMENTS_CITY_REQUIRED
  PAYMENTS_CITY_TOO_LONG
  PAYMENTS_COMPANY_INVALID
  PAYMENTS_COMPANY_REQUIRED
  PAYMENTS_COMPANY_TOO_LONG
  PAYMENTS_COUNTRY_REQUIRED
  PAYMENTS_CREDIT_CARD_BASE_EXPIRED
  PAYMENTS_CREDIT_CARD_BASE_GATEWAY_NOT_SUPPORTED
  PAYMENTS_CREDIT_CARD_BASE_INVALID_START_DATE_OR_ISSUE_NUMBER_FOR_DEBIT
  PAYMENTS_CREDIT_CARD_BRAND_NOT_SUPPORTED
  PAYMENTS_CREDIT_CARD_FIRST_NAME_BLANK
  PAYMENTS_CREDIT_CARD_GENERIC
  PAYMENTS_CREDIT_CARD_LAST_NAME_BLANK
  PAYMENTS_CREDIT_CARD_MONTH_INCLUSION
  PAYMENTS_CREDIT_CARD_NAME_INVALID
  PAYMENTS_CREDIT_CARD_NUMBER_INVALID
  PAYMENTS_CREDIT_CARD_NUMBER_INVALID_FORMAT
  PAYMENTS_CREDIT_CARD_SESSION_ID
  PAYMENTS_CREDIT_CARD_VERIFICATION_VALUE_BLANK
  PAYMENTS_CREDIT_CARD_VERIFICATION_VALUE_INVALID_FOR_CARD_TYPE
  PAYMENTS_CREDIT_CARD_YEAR_EXPIRED
  PAYMENTS_CREDIT_CARD_YEAR_INVALID_EXPIRY_YEAR
  PAYMENTS_FIRST_NAME_INVALID
  PAYMENTS_FIRST_NAME_REQUIRED
  PAYMENTS_FIRST_NAME_TOO_LONG
  PAYMENTS_INVALID_POSTAL_CODE_FOR_COUNTRY
  PAYMENTS_INVALID_POSTAL_CODE_FOR_ZONE
  PAYMENTS_LAST_NAME_INVALID
  PAYMENTS_LAST_NAME_REQUIRED
  PAYMENTS_LAST_NAME_TOO_LONG
  PAYMENTS_METHOD_UNAVAILABLE
  PAYMENTS_METHOD_REQUIRED
  PAYMENTS_UNACCEPTABLE_PAYMENT_AMOUNT
  PAYMENTS_PHONE_NUMBER_INVALID
  PAYMENTS_PHONE_NUMBER_REQUIRED
  PAYMENTS_POSTAL_CODE_INVALID
  PAYMENTS_POSTAL_CODE_REQUIRED
  PAYMENTS_SHOPIFY_PAYMENTS_REQUIRED
  PAYMENTS_WALLET_CONTENT_MISSING
  PAYMENTS_BILLING_ADDRESS_ZONE_NOT_FOUND
  PAYMENTS_BILLING_ADDRESS_ZONE_REQUIRED_FOR_COUNTRY
  TAXES_MUST_BE_DEFINED
  TAXES_LINE_ID_NOT_FOUND
  TAXES_DELIVERY_GROUP_ID_NOT_FOUND
}

"""Cart submit for checkout completion is successful."""
type SubmitAlreadyAccepted {
  """
  The ID of the cart completion attempt that will be used for polling for the result.
  """
  attemptId: String!
}

"""Cart submit for checkout completion failed."""
type SubmitFailed {
  """The URL of the checkout for the cart."""
  checkoutUrl: URL

  """The list of errors that occurred from executing the mutation."""
  errors: [SubmissionError!]!
}

"""Cart submit for checkout completion is already accepted."""
type SubmitSuccess {
  """
  The ID of the cart completion attempt that will be used for polling for the result.
  """
  attemptId: String!
}

"""Cart submit for checkout completion is throttled."""
type SubmitThrottled {
  "UTC date time string that indicates the time after which clients should make their next\npoll request. Any poll requests sent before this time will be ignored. Use this value to schedule the\nnext poll request.\n"
  pollAfter: DateTime!
}

"""Color and image for visual representation."""
type Swatch {
  """The swatch color."""
  color: Color

  """The swatch image."""
  image: MediaImage
}

"The taxonomy category for the product.\n"
type TaxonomyCategory implements Node {
  """All parent nodes of the current taxonomy category."""
  ancestors: [TaxonomyCategory!]!

  """A static identifier for the taxonomy category."""
  id: ID!

  """The localized name of the taxonomy category."""
  name: String!
}

"""
Represents a resource that you can track the origin of the search traffic.
"""
interface Trackable {
  """
  URL parameters to be added to a page URL to track the origin of on-site search traffic for [analytics reporting](https://help.shopify.com/manual/reports-and-analytics/shopify-reports/report-types/default-reports/behaviour-reports). Returns a result when accessed through the [search](https://shopify.dev/docs/api/storefront/current/queries/search) or [predictiveSearch](https://shopify.dev/docs/api/storefront/current/queries/predictiveSearch) queries, otherwise returns null.
  """
  trackingParameters: String
}

"Represents an [RFC 3986](https://datatracker.ietf.org/doc/html/rfc3986) and\n[RFC 3987](https://datatracker.ietf.org/doc/html/rfc3987)-compliant URI string.\n\nFor example, `\"https://example.myshopify.com\"` is a valid URL. It includes a scheme (`https`) and a host\n(`example.myshopify.com`).\n"
scalar URL

"The measurement used to calculate a unit price for a product variant (e.g. $9.99 / 100ml).\n"
type UnitPriceMeasurement {
  """The type of unit of measurement for the unit price measurement."""
  measuredType: UnitPriceMeasurementMeasuredType

  """The quantity unit for the unit price measurement."""
  quantityUnit: UnitPriceMeasurementMeasuredUnit

  """The quantity value for the unit price measurement."""
  quantityValue: Float!

  """The reference unit for the unit price measurement."""
  referenceUnit: UnitPriceMeasurementMeasuredUnit

  """The reference value for the unit price measurement."""
  referenceValue: Int!
}

"""The accepted types of unit of measurement."""
enum UnitPriceMeasurementMeasuredType {
  """Unit of measurements representing volumes."""
  VOLUME

  """Unit of measurements representing weights."""
  WEIGHT

  """Unit of measurements representing lengths."""
  LENGTH

  """Unit of measurements representing areas."""
  AREA
}

"""The valid units of measurement for a unit price measurement."""
enum UnitPriceMeasurementMeasuredUnit {
  """1000 milliliters equals 1 liter."""
  ML

  """100 centiliters equals 1 liter."""
  CL

  """Metric system unit of volume."""
  L

  """1 cubic meter equals 1000 liters."""
  M3

  """1000 milligrams equals 1 gram."""
  MG

  """Metric system unit of weight."""
  G

  """1 kilogram equals 1000 grams."""
  KG

  """1000 millimeters equals 1 meter."""
  MM

  """100 centimeters equals 1 meter."""
  CM

  """Metric system unit of length."""
  M

  """Metric system unit of area."""
  M2
}

"""Systems of weights and measures."""
enum UnitSystem {
  """Imperial system of weights and measures."""
  IMPERIAL_SYSTEM

  """Metric system of weights and measures."""
  METRIC_SYSTEM
}

"An unsigned 64-bit integer. Represents whole numeric values between 0 and 2^64 - 1 encoded as a string of base-10 digits.\n\nExample value: `\"50\"`.\n"
scalar UnsignedInt64

"""A redirect on the online store."""
type UrlRedirect implements Node {
  """The ID of the URL redirect."""
  id: ID!

  """
  The old path to be redirected from. When the user visits this path, they'll be redirected to the target location.
  """
  path: String!

  """The target location where the user will be redirected to."""
  target: String!
}

"An auto-generated type for paginating through multiple UrlRedirects.\n"
type UrlRedirectConnection {
  """A list of edges."""
  edges: [UrlRedirectEdge!]!

  """A list of the nodes contained in UrlRedirectEdge."""
  nodes: [UrlRedirect!]!

  """Information to aid in pagination."""
  pageInfo: PageInfo!
}

"An auto-generated type which holds one UrlRedirect and a cursor during pagination.\n"
type UrlRedirectEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of UrlRedirectEdge."""
  node: UrlRedirect!
}

"""Represents an error in the input of a mutation."""
type UserError implements DisplayableError {
  """The path to the input field that caused the error."""
  field: [String!]

  """The error message."""
  message: String!
}

"""Error codes for failed Shop Pay payment request session mutations."""
type UserErrorsShopPayPaymentRequestSessionUserErrors implements DisplayableError {
  """The error code."""
  code: UserErrorsShopPayPaymentRequestSessionUserErrorsCode

  """The path to the input field that caused the error."""
  field: [String!]

  """The error message."""
  message: String!
}

"""
Possible error codes that can be returned by `ShopPayPaymentRequestSessionUserErrors`.
"""
enum UserErrorsShopPayPaymentRequestSessionUserErrorsCode {
  """Payment request input is invalid."""
  PAYMENT_REQUEST_INVALID_INPUT

  """Payment request not found."""
  PAYMENT_REQUEST_NOT_FOUND

  """Idempotency key has already been used."""
  IDEMPOTENCY_KEY_ALREADY_USED
}

"""
The input fields for a filter used to view a subset of products in a collection matching a specific variant option.
"""
input VariantOptionFilter {
  """The name of the variant option to filter on."""
  name: String!

  """The value of the variant option to filter on."""
  value: String!
}

"""Represents a Shopify hosted video."""
type Video implements Media & Node {
  """A word or phrase to share the nature or contents of a media."""
  alt: String

  """A globally-unique ID."""
  id: ID!

  """The media content type."""
  mediaContentType: MediaContentType!

  """The presentation for a media."""
  presentation: MediaPresentation

  """The preview image for the media."""
  previewImage: Image

  """The sources for a video."""
  sources: [VideoSource!]!
}

"""Represents a source for a Shopify hosted video."""
type VideoSource {
  """The format of the video source."""
  format: String!

  """The height of the video."""
  height: Int!

  """The video MIME type."""
  mimeType: String!

  """The URL of the video."""
  url: String!

  """The width of the video."""
  width: Int!
}

"""Units of measurement for weight."""
enum WeightUnit {
  """1 kilogram equals 1000 grams."""
  KILOGRAMS

  """Metric system unit of mass."""
  GRAMS

  """1 pound equals 16 ounces."""
  POUNDS

  """Imperial system unit of mass."""
  OUNCES
}