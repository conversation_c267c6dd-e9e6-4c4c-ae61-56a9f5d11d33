{"version": 3, "file": "static/css/main.5ec30cdf.css", "mappings": "AAAA,mDAAc,CAAd,uBAAc,CAAd,kBAAc,CAAd,kBAAc,CAAd,aAAc,CAAd,aAAc,CAAd,aAAc,CAAd,cAAc,CAAd,cAAc,CAAd,YAAc,CAAd,YAAc,CAAd,iBAAc,CAAd,qCAAc,CAAd,6BAAc,CAAd,4BAAc,CAAd,2BAAc,CAAd,cAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,iBAAc,CAAd,0BAAc,CAAd,2BAAc,CAAd,yBAAc,CAAd,iCAAc,CAAd,0BAAc,CAAd,qBAAc,CAAd,6BAAc,CAAd,WAAc,CAAd,iBAAc,CAAd,eAAc,CAAd,gBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,eAAc,CAAd,YAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,0BAAc,CAAd,wBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,oBAAc,CAAd,oBAAc,CAAd;;CAAc,CAAd,uCAAc,CAAd,qBAAc,CAAd,8BAAc,CAAd,wCAAc,CAAd,4BAAc,CAAd,uCAAc,CAAd,gHAAc,CAAd,8BAAc,CAAd,eAAc,CAAd,UAAc,CAAd,wBAAc,CAAd,QAAc,CAAd,uBAAc,CAAd,aAAc,CAAd,QAAc,CAAd,4DAAc,CAAd,gCAAc,CAAd,mCAAc,CAAd,mBAAc,CAAd,eAAc,CAAd,uBAAc,CAAd,2BAAc,CAAd,8CAAc,CAAd,mGAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,aAAc,CAAd,iBAAc,CAAd,sBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,oBAAc,CAAd,aAAc,CAAd,mEAAc,CAAd,aAAc,CAAd,mBAAc,CAAd,cAAc,CAAd,+BAAc,CAAd,mBAAc,CAAd,sBAAc,CAAd,mBAAc,CAAd,QAAc,CAAd,SAAc,CAAd,iCAAc,CAAd,gHAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,4BAAc,CAAd,gCAAc,CAAd,+BAAc,CAAd,mEAAc,CAAd,0CAAc,CAAd,mBAAc,CAAd,mDAAc,CAAd,sDAAc,CAAd,YAAc,CAAd,yBAAc,CAAd,2DAAc,CAAd,iBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,QAAc,CAAd,SAAc,CAAd,gBAAc,CAAd,wBAAc,CAAd,sDAAc,CAAd,SAAc,CAAd,mCAAc,CAAd,wBAAc,CAAd,4DAAc,CAAd,qBAAc,CAAd,qBAAc,CAAd,cAAc,CAAd,uDAAc,CAEd,kBAAmB,CAAnB,iBAAmB,CAAnB,yBAAmB,CAAnB,yBAAmB,CAAnB,uBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CAAnB,kBAAmB,CAAnB,0BAAmB,CAAnB,mBAAmB,CAAnB,mBAAmB,CAAnB,yBAAmB,CAAnB,iBAAmB,CAAnB,kBAAmB,CAAnB,kBAAmB,CAAnB,kBAAmB,CAAnB,eAAmB,CAAnB,iBAAmB,CAAnB,kBAAmB,CAAnB,gBAAmB,CAAnB,8BAAmB,CAAnB,wCAAmB,CAAnB,eAAmB,CAAnB,oEAAmB,CAAnB,4BAAmB,CAAnB,+BAAmB,CAAnB,yBAAmB,CAAnB,mCAAmB,CAAnB,gCAAmB,CAAnB,qCAAmB,CAAnB,sCAAmB,CAAnB,8CAAmB,CAAnB,iBAAmB,CAAnB,gBAAmB,CAAnB,8BAAmB,CAAnB,kCAAmB,CAAnB,gCAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,0BAAmB,CAAnB,gCAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,uDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,uCAAmB,CAAnB,oBAAmB,CAAnB,sDAAmB,CAAnB,wCAAmB,CAAnB,oBAAmB,CAAnB,uDAAmB,CAAnB,wCAAmB,CAAnB,oBAAmB,CAAnB,uDAAmB,CAAnB,qCAAmB,CAAnB,oBAAmB,CAAnB,sDAAmB,CAAnB,wCAAmB,CAAnB,wBAAmB,CAAnB,2DAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,mBAAmB,CAAnB,kBAAmB,CAAnB,iBAAmB,CAAnB,wBAAmB,CAAnB,mBAAmB,CAAnB,8CAAmB,CAAnB,0BAAmB,CAAnB,8BAAmB,CAAnB,mDAAmB,CAAnB,0BAAmB,CAAnB,gBAAmB,CAAnB,0BAAmB,CAAnB,mBAAmB,CAAnB,yBAAmB,CAAnB,gBAAmB,CAAnB,+BAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,+BAAmB,CAAnB,UAAmB,CAAnB,+CAAmB,CAAnB,kEAAmB,CAAnB,4FAAmB,CAAnB,+CAAmB,CAAnB,kGAAmB,CAAnB,wLAAmB,CAFnB,kDAEoB,CAFpB,kBAEoB,CAFpB,yCAEoB,CCFpB,EACE,oBAAqB,CACrB,oBAAqB,CACrB,gBAAiB,CACjB,qBAAsB,CACtB,iBAAkB,CAClB,qBAAsB,CACtB,eAAgB,CAChB,cAAe,CACf,qBAAsB,CACtB,gBAAiB,CACjB,kBAAmB,CACnB,qCACF,CAEA,KACE,kCAAmC,CACnC,uBACF", "sources": ["index.css", "App.css"], "sourcesContent": ["@tailwind base;\n@tailwind components;\n@tailwind utilities;", "* {\n  --background: #1f2335;\n  --foreground: #c0caf5;\n  --orange: #ff9e64;\n  --deep-orange: #ff757f;\n  --magenta: #ff007c;\n  --light-green: #4fd6be;\n  --green: #c3e88d;\n  --cyan: #41a6b5;\n  --deep-purple: #9d7cd8;\n  --purple: #bb9af7;\n  --deep-red: #c53b53;\n  font-family: \"JetBrains Mono\", sans-serif;\n}\n\nbody {\n  background-color: var(--background);\n  color: var(--foreground);\n}\n"], "names": [], "sourceRoot": ""}