// import path from "path";
// import { readFile } from "node:fs/promises";
// import { getAP<PERSON><PERSON>ey } from "../../../../database/cliconfig";
// import { ingestSupabaseClientResponseStream } from "../../../messageparsing";
// import Anthropic from "@anthropic-ai/sdk";
// import { Client } from "@modelcontextprotocol/sdk/client/index.js";
// import { StdioClientTransport } from "@modelcontextprotocol/sdk/client/stdio.js";
// import chalk from "chalk";
// import { SupabaseActionType } from "./supabase-client-mcp-server";
// import {Logger} from '../../../../utils/logger';
// import { nodejsRoot } from "../../../../projectrootpath";

// let claude: Anthropic | null = null;
// let mcp: { supabase: null | Client } = {
//   supabase: null,
// };
// const logger = new Logger({
//   filePath: __dirname + "/logs/supabase-agent-logs.txt"
// });

// function transformReturnResponse(response: Anthropic.MessageParam | undefined) {
//   if (!response) {
//     return "No response from supabase agent!";
//   }
  
//   if (Array.isArray(response.content)) {
//     // If it's an array, check the first item
//     if (response.content.length > 0) {
//       const firstItem = response.content[0];
      
//       // If it's a text item, return its text content
//       if (typeof firstItem === 'object' && firstItem !== null && 
//           'type' in firstItem && firstItem.type === 'text' && 
//           'text' in firstItem && typeof firstItem.text === 'string') {
//         return firstItem.text;
//       }
      
//       // Otherwise, stringify the entire content array
//       return JSON.stringify(response.content);
//     }
//     // Empty array case
//     return "[]";
//   } else {
//     // If it's not an array, return as string
//     return typeof response.content === 'string' 
//       ? response.content 
//       : JSON.stringify(response.content);
//   }
// }

// async function initialize() {
//   if (!claude) {
//     const rec = getAPIKey("claude");
//     if (rec) {
//       claude = new Anthropic({ apiKey: rec.apikey });
//     }
//   }

//   if (!mcp.supabase) {
//     mcp.supabase = new Client({
//       name: "supabase-mcp-server",
//       version: "1.0.0",
//     });

//     await connectToMCPServer(
//       path.resolve(nodejsRoot, "agents/mcp-servers/supabase/supabase-mcp-server.ts"),
//       mcp.supabase
//     );
//   }
//   return claude;
// }

// const transports = new Map();
// const tools: Anthropic.Tool[] = [];
// const toolsMCPMapping: {
//   [toolName: string]: Client;
// } = {};

// async function connectToMCPServer(
//   serverScriptPath: string,
//   mcp: Client,
//   env?: Record<string, string>
// ) {
//   try {
//     const isJS = serverScriptPath.endsWith(".js");
//     const isPy = serverScriptPath.endsWith(".py");
//     const isTS = serverScriptPath.endsWith(".ts");
//     // if (!isJS && !isPy && !isTS) {
//     //   throw new Error("Server script must be .js, .py or .ts file");
//     // }

//     let command;
//     if (isJS) {
//       command = process.execPath;
//     } else if (isPy) {
//       command = "python3";
//     } else if (isTS) {
//       command = path.resolve(
//         nodejsRoot,
//         "node_modules",
//         ".bin",
//         "ts-node"
//       );
//     } else {
//       command = serverScriptPath;
//     }

//     let transport;
//     if (isJS || isPy || isTS) {
//       transport = new StdioClientTransport({
//         command,
//         args: [serverScriptPath],
//         env,
//       });
//     } else {
//       transport = new StdioClientTransport({
//         command,
//         env,
//       });
//     }

//     transports.set(serverScriptPath, transport);

//     await mcp.connect(transport);

//     // TODO: Hack because supabase mcp takes time to start. @yashwanth take a look
//     // await new Promise((resolve) => {
//     //   setTimeout(() => {
//     //     resolve({});
//     //   }, 25000);
//     // });

//     const toolResult = await mcp.listTools();
//     for (let i = 0; i < toolResult.tools.length; ++i) {
//       const tool = toolResult.tools[i];
//       tools.push({
//         name: tool.name,
//         description: tool.description,
//         input_schema: tool.inputSchema,
//       });
//       toolsMCPMapping[tool.name] = mcp;
//     }

//     console.log(
//       chalk.green(
//         "Connected to mcp server with tools: " +
//           tools.map((it) => it.name).join(",")
//       )
//     );
//   } catch (err) {
//     console.error(
//       chalk.red("Failed to connect to mcp server: " + serverScriptPath),
//       err
//     );
//   }
// }

// export async function prompt(userPrompt: SupabaseActionType) {
//   const apptile_context = userPrompt.apptile_context;
//   delete (userPrompt as any).apptile_context;

//   const claude = await initialize();
//   const messages: Anthropic.MessageParam[] = [
//     {
//       role: "user",
//       content: [{ 
//         type: "text", 
//         text: JSON.stringify(userPrompt) 
//       }],
//     },
//   ];
//   let result: string = "";
//   if (!claude || !mcp) {
//     result = "No anthropic client found!";
//     console.log("[SUPABASE AGENT] Failed because no agent was found");
//     if (!mcp) {
//       console.log("[SUPABASE AGENT] Failed to connect to mcp client");
//     }
//   } else {
//     let toolRun = false;
//     const initialPrompt = await readFile(
//       path.resolve(nodejsRoot, "systemPrompts/supabase.html"),
//       { encoding: "utf8" }
//     );

//     try {
//       do {
//         console.log("[SUPABASE AGENT] Triggering completion");
//         const responseStream = claude.messages.stream({
//           system: initialPrompt,
//           model: "claude-3-7-sonnet-20250219",
//           messages,
//           max_tokens: 16000,
//           tool_choice: {
//             type: "auto",
//             disable_parallel_tool_use: true,
//           },
//           tools: tools,
//         });

//         console.log("[SUPABASE AGENT] starting ingestion of response stream");
//         const ingestedResponse = await ingestSupabaseClientResponseStream(
//           responseStream
//         );
//         if (ingestedResponse?.messageText) {
//           messages.push({
//             role: "assistant",
//             content: [{ type: "text", text: ingestedResponse.messageText }],
//           });
//         }

//         if (ingestedResponse.status === "MaxTokensUsed") {
//           console.log(
//             "Claude stopped in the middle of generation. Waiting a few seconds and asking to continue"
//           );
//           await new Promise((resolve) => {
//             setTimeout(() => {
//               resolve({});
//             }, 10000);
//           });

//           messages.push({
//             role: "user",
//             content: [
//               {
//                 type: "text",
//                 text: "you stopped in the middle of generation. Please continue.",
//               },
//             ],
//           });
//           continue;
//         }

//         toolRun = !!ingestedResponse?.toolInput;
//         if (toolRun) {
//           let inputVars: any = undefined;
//           if (ingestedResponse.toolInput.partialInput) {
//             logger.info("Injected reponse parsing ");
//             logger.info(
//               "Injected reponse parsed: ",
//               JSON.parse(ingestedResponse.toolInput.partialInput)
//             );
//             inputVars = JSON.parse(ingestedResponse.toolInput.partialInput);
//             logger.info("Call tool message: " + JSON.stringify(inputVars, null, 2));
//           } else {
//             inputVars = {};
//             console.log(
//               "[SUPABASE AGENT] Starting tool run with no arguments for: ",
//               ingestedResponse.toolInput.tool
//             );
//           }
          
//           messages.push({
//             role: "assistant",
//             content: [
//               {
//                 id: ingestedResponse.toolInput.id,
//                 input: inputVars,
//                 name: ingestedResponse.toolInput.tool,
//                 type: "tool_use",
//               },
//             ],
//           });

//           logger.info("Messages till now: ", messages);
//           let result;

//           if (toolsMCPMapping[ingestedResponse.toolInput.tool]) {
//             logger.info("Call tool message: ", {
//               name: ingestedResponse.toolInput.tool,
//               arguments: { ...inputVars, apptile_context },
//             });
//             result = await toolsMCPMapping[
//               ingestedResponse.toolInput.tool
//             ].callTool({
//               name: ingestedResponse.toolInput.tool,
//               arguments: { ...inputVars, apptile_context },
//             });
//             logger.info("Call tool response: ", result);
//           } else {
//             console.error(chalk.red("Could not run tool!"));
//             result = { content: ["Tool unavailable! Try again later!"] };
//           }

//           let typedResult: string | Array<{ type: "text"; text: string }>;

//           if (typeof result.content === "string") {
//             typedResult = result.content;
//           } else if (Array.isArray(result.content)) {
//             // Ensure each item in the array has the correct structure
//             typedResult = result.content.map(item => {
//               if (typeof item === 'object' && item !== null && 'type' in item && 'text' in item) {
//                 return item;
//               } else {
//                 return {
//                   type: "text",
//                   text: typeof item === 'string' ? item : JSON.stringify(item)
//                 };
//               }
//             });
//           } else {
//             console.log(
//               chalk.red("tool returned unexpected response: "),
//               result
//             );
//             typedResult = "tool failed!";
//           }
//           // if (stringifiedResult.length > 40000) {
//           //   stringifiedResult = stringifiedResult.slice(0, 40000) + "--------rest of the response was truncated due to token limit--------";
//           // }

//           messages.push({
//             role: "user",
//             content: [
//               {
//                 type: "tool_result",
//                 tool_use_id: ingestedResponse.toolInput.id,
//                 content: typedResult,
//               },
//             ],
//           });
//         }
//       } while (toolRun);
//       return transformReturnResponse(messages.pop());
//     } catch (err: any) {
//       console.error("[SUPABASE AGENT] Something failed", err);
//       return `Error from supabase agent: ${err.message || JSON.stringify(err)}`;
//     }
//   }
// }
