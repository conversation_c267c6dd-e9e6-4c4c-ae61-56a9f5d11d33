export function handlePathOpen(path: string, program: 'code'|'finder') {
  if (program === 'code') {
    fetch(
      `${process.env.REACT_APP_SERVER_URL}/plugin-server/cli/operation/opencode/${encodeURIComponent(path)}`,
        {
        method: 'POST'
      }
    )
  } else {
    fetch(
      `${process.env.REACT_APP_SERVER_URL}/plugin-server/cli/operation/openfinder/${encodeURIComponent(path)}`,
        {
        method: 'POST'
      }
    )
  }
};
