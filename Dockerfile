# Use an official Node.js runtime as a parent image
# FROM node:18-bullseye
FROM 723464704088.dkr.ecr.us-east-1.amazonaws.com/node:18-bullseye

# Set the working directory in the container
WORKDIR /app

# Install pm2
RUN npm i -g pm2

# Copy package.json and package-lock.json first
COPY package.json package-lock.json ./

# make new folder inside ui and copy package json
RUN mkdir ui
COPY ui/package.json ui/package-lock.json ./ui/

# Install dependencies
RUN npm install && cd ui && npm install && cd ..

# Copy the rest of your application code
COPY . .

# build
RUN npm run build

# Start the application - when the .env ENABLE_POSTGRES is false, migration command will fail and pm2 command will run
CMD npm run migrate || true && pm2-runtime process.yaml