'use strict';
import { Model, Sequelize, DataTypes, ModelStatic, Optional } from 'sequelize';
import AppDatabase from '../database';

export interface ChatMessageAttributes {
  id?: number;
  chatId: number;
  htmlcontent?: string;
  tool_name?: string;
  role?: string;
  content?: string;
  tool_call_id?: string;
  content_type?: string;
  temp_id?: string;
}

interface ChatMessageCreationAttributes extends Optional<ChatMessageAttributes, 'id'> {}

export default class ChatMessage extends Model<ChatMessageAttributes, ChatMessageCreationAttributes> implements ChatMessageAttributes {
  declare id?: number;
  declare chatId: number;
  declare htmlcontent?: string;
  declare tool_name?: string;
  declare role?: string;
  declare content?: string;
  declare tool_call_id?: string;
  declare content_type?: string;
  declare temp_id?: string;
  declare readonly createdAt: Date;
  declare readonly updatedAt: Date;
  declare readonly deletedAt: Date;
}
ChatMessage.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement:true,
      primaryKey: true
    },
    chatId: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    htmlcontent: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    tool_name: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    content_type: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    role: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    tool_call_id: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    temp_id: {
      type: DataTypes.TEXT,
      allowNull: true
    }
  }, {
    tableName: 'chat_message',
    sequelize: AppDatabase.sequelize,
    timestamps: true,
    paranoid: true
  });