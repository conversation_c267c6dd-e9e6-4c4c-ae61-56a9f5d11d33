"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.exec = void 0;
exports.toKebabCase = toKebabCase;
exports.toCamelCase = toCamelCase;
exports.makeHeadersWithCookie = makeHeadersWithCookie;
const node_util_1 = __importDefault(require("node:util"));
const child_process_1 = require("child_process");
exports.exec = node_util_1.default.promisify(child_process_1.exec);
function toKebabCase(anyCaseName) {
    const spacesRemoved = anyCaseName.replace(/\s/g, "-");
    const lettersOnly = spacesRemoved.replace(/[^a-zA-Z\-]/g, "");
    const lowerCased = lettersOnly.toLowerCase();
    return lowerCased;
}
function toCamelCase(kebabCaseName) {
    const words = kebabCaseName.split("-");
    for (let i = 1; i < words.length; ++i) {
        words[i] = words[i][0].toUpperCase() + words[i].slice(1);
    }
    return words.join("");
}
exports.default = {
    comment: "This is only exported beacuse otherwise node does not recognize this file as a module",
};
function makeHeadersWithCookie(cookie, extraHeaders) {
    //Check if this is working in production environment
    const header = Object.assign({ Accept: "application/json, text/plain, */*", "Accept-Language": "en-GB,en-US;q=0.9,en;q=0.8", Connection: "keep-alive", "If-None-Match": 'W/"5b1-kyqqkRyAnA0NO0WgKmBhdQQi7Qo"', Origin: "https://app.apptile.io", Referer: "https://app.apptile.io", "Sec-Fetch-Dest": "empty", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Site": "cross-site", "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36", "sec-ch-ua": '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"', "sec-ch-ua-mobile": "?0", "sec-ch-ua-platform": '"macOS"' }, extraHeaders);
    if (process.env.NODE_ENV === "production") {
        console.log("This is a production environment");
        return {
            headers: header,
        };
    }
    else {
        console.log("This is a development environment");
        if (!cookie) {
            throw new Error("Cookie not provided");
        }
        return {
            headers: Object.assign(Object.assign({}, header), { Cookie: cookie }),
        };
    }
}
