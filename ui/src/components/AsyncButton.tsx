import React, {FC, useState} from 'react';

type ButtonProps = {
  onClick: (() => Promise<any>);
  label: string;
};
const Button: FC<ButtonProps> = function ({onClick, label}) {
  const [showLoader, setShowLoader] = useState(false);
  let loader = null;
  if (showLoader) {
    loader = <img alt="loading" src={require('../assets/loading.gif')} style={{height: 15, marginRight: 4}} />
  }

  const handleClick = async () => {
    setShowLoader(true);
    await onClick();
    setShowLoader(false);
  }

  return (
    <button
      className="flex flex-row p-1 rounded text-blue-500 border border-blue-500"
      onClick={handleClick}
    >
      {loader}
      {label}
    </button>
  );
}

export default Button;
