export function extractCodeFromLLMResponse(response: string): string {
    let startOfCodeMarker = response.indexOf('```json');
    if (startOfCodeMarker >= 0) {
        startOfCodeMarker += '```json'.length
    } else if (response.indexOf('```jsx') >= 0) {
        startOfCodeMarker = response.indexOf('```jsx') + '```jsx'.length;
    } else {
        startOfCodeMarker = 0;
    }

    let endOfCodeBlock = response.indexOf('```', startOfCodeMarker);
    if (endOfCodeBlock < 0) {
        endOfCodeBlock = response.length;
    }
    const code = response.slice(startOfCodeMarker, endOfCodeBlock);
    return code
}