import path from 'path';
import {Configuration} from 'webpack';

const webpackConfig: Configuration = {
  entry: '__ADDED_AT_RUNTIME__',
  output: {
    filename: 'bundle.js',
    path: '__ADDED_AT_RUNTIME__',  
    libraryTarget: 'umd',  // Universal Module Definition
    globalObject: 'apptileSDK',  // Necessary for UMD to work in Node and browser environments
  },
  stats: {
    colors: false
  },
  mode: 'development',
  externals: {
    'react': {
      commonjs: 'react',
      commonjs2: 'react',
      amd: 'react',
      root: 'React',  // Global variable in non-module environments
    },
    'react-native': {
      commonjs: 'react-native',
      commonjs2: 'react-native',
      amd: 'react-native',
      root: 'ReactNative',  // Global variable in non-module environments
    },
    'apptile-core': {
      commonjs: 'apptile-core',
      commonjs2: 'apptile-core',
      amd: 'apptile-core',
      root: 'Apptile<PERSON>ore',  // Global variable in non-module environments
    },
    'react-native-svg': {
      commonjs: 'react-native-svg',
      commonjs2: 'react-native-svg',
      amd: 'react-native-svg',
      root: 'ReactNativeSvg',  // Global variable in non-module environments
    },
    'react-native-linear-gradient': {
      commonjs: 'react-native-linear-gradient',
      commonjs2: 'react-native-linear-gradient',
      amd: 'react-native-linear-gradient',
      root: 'ReactNativeLinearGradient',  // Global variable in non-module environments
    },
    'react-native-fast-image': {
      commonjs: 'react-native-fast-image',
      commonjs2: 'react-native-fast-image',
      amd: 'react-native-fast-image',
      root: 'ReactNativeFastImage'
    },
    '@react-navigation/native': {
      commonjs: '@react-navigation/native',
      commonjs2: '@react-navigation/native',
      amd: '@react-navigation/native',
      root: 'ReactNavNative',  // Global variable in non-module environments
    },
    '@react-navigation/stack': {
      commonjs:  '@react-navigation/stack',
      commonjs2: '@react-navigation/stack',
      amd: '@react-navigation/stack',
      root: 'ReactNavStack',  // Global variable in non-module environments
    },
    'graphql-tag': {
      commonjs: 'graphql-tag',
      commonjs2: 'graphql-tag',
      amd: 'graphql-tag',
      root: 'GraphqlTag'
    },
    '@react-navigation/bottom-tabs': {
      commonjs: '@react-navigation/bottom-tabs',
      commonjs2: '@react-navigation/bottom-tabs',
      amd: '@react-navigation/bottom-tabs',
      root: 'ReactNavBottomTabs',  // Global variable in non-module environments
    },
    'lottie-react-native': {
      commonjs: 'lottie-react-native',
      commonjs2: 'lottie-react-native',
      amd: 'lottie-react-native',
      root: 'Lottie',  // Global variable in non-module environments
    },
    'react-redux': {
      commonjs: 'react-redux',
      commonjs2: 'react-redux',
      amd: 'react-redux',
      root: 'ReactRedux',
    },
    'react-native-safe-area-context': {
      commonjs: 'react-native-safe-area-context',
      commonjs2: 'react-native-safe-area-context',
      amd: 'react-native-safe-area-context',
      root: 'ReactNativeSafeAreaContext',
    },
    'react-native-joi': {
      commonjs: 'react-native-joi',
      commonjs2: 'react-native-joi',
      amd: 'react-native-joi',
      root: 'Joi'
    },
    'apptile-shopify': {
      commonjs: 'apptile-shopify',
      commonjs2: 'apptile-shopify',
      amd: 'apptile-shopify',
      root: 'apptileShopify'
    },
    'react-native-reanimated': {
      commonjs: 'react-native-reanimated',
      commonjs2: 'react-native-reanimated',
      amd: 'react-native-reanimated',
      root: 'ReactNativeReanimated',
    },
    'react-native-gesture-handler': {
      commonjs: 'react-native-gesture-handler',
      commonjs2: 'react-native-gesture-handler',
      amd: 'react-native-gesture-handler',
      root: 'ReactNativeGestureHandler'
    },
    'react-native-webview': {
      commonjs: 'react-native-webview',
      commonjs2: 'react-native-webview',
      amd: 'react-native-webview',
      root: 'ReactNativeWebview'
    },
    '@gorhom/portal': {
      commonjs: '@gorhom/portal',
      commonjs2: '@gorhom/portal',
      amd: '@gorhom/portal',
      root: 'GorhomPortal'
    },
    'moment': {
      commonjs: 'moment',
      commonjs2: 'moment',
      amd: 'moment',
      root: 'moment'
    }, 
    'lodash': {
      commonjs: 'lodash',
      commonjs2: 'lodash',
      amd: 'lodash',
      root: 'lodash'
    }
  },
  module: {
    rules: [
      {
        test: /\.[jt]sx?$/, // Include both .js, .jsx, .ts, .tsx
        exclude: modulePath => (
          /node_modules/.test(modulePath) && !/node_modules\/sdk-plugin-example/.test(modulePath)
        ),
        use: {
          loader: 'babel-loader',
          options: {
            cacheDirectory: true,
            // The 'metro-react-native-babel-preset' preset is recommended to match React Native's packager
            presets: ['module:@react-native/babel-preset', "@babel/preset-typescript"],
            // Re-write paths to import only the modules needed by the app
            // plugins: ['react-native-web']
          },
        },
      },
    ],
  },
  resolve: {
    extensions: ['.web.tsx', '.web.ts', '.tsx', '.ts', '.web.jsx', '.web.js', '.jsx', '.js', '.css'],
    modules: ['__ADDED_AT_RUNTIME__']
  },
};
export default webpackConfig