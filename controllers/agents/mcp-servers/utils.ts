import {z, Zod<PERSON><PERSON><PERSON><PERSON><PERSON>, Zod<PERSON>ypeAny} from "zod";
import { createWinstonLogger } from "../../../utils/logger";

export const logger = createWinstonLogger(false, "tools");
export type ToolResponse = {
  content: Array<{type: "text"; text: string;}>;
};

export type ToolSpec<T extends ZodRawShape> = {
  name: string;
  description: string;
  inputSchema: T;
  cb: (args: z.objectOutputType<T, ZodTypeAny>) => Promise<ToolResponse>;
};

export function defineToolSpec<T extends ZodRawShape>(toolSpec: ToolSpec<T>): ToolSpec<T> {
  return toolSpec;
}

export function prefixWithLineNumbers(content: string, firstLine = 1) {
  const lines = content.split(/\r?\n/);
  const maxDigitsInLineNumbers = (firstLine + lines.length).toString().length;
  let result = "";
  for (let i = 0; i < lines.length; ++i) {
    result += `${(firstLine++).toString().padStart(maxDigitsInLineNumbers, '0')}: ${lines[i]}\n`;
  }
  return result;
}