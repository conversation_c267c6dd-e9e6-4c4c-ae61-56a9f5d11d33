import { DataTypes, Model, Optional } from 'sequelize';
import AppDatabase from '../database';

export interface IChatAttributes {
  id: number;
  outputFile: string;
  type: string;
  llm_provider: string;
  llm_model: string;
  appId: string;
}

interface ChatCreationAttributes extends Optional<IChatAttributes, 'id'> {}

export default class Chat
  extends Model<IChatAttributes, ChatCreationAttributes>
  implements IChatAttributes
{
  declare id: number;
  declare outputFile: string;
  declare type: string;
  declare llm_provider: string;
  declare llm_model: string;
  declare appId: string;

  declare readonly createdAt: Date;
  declare readonly updatedAt: Date;
  declare readonly deletedAt: Date;
}

Chat.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true
    },
    outputFile: {
      type: DataTypes.STRING,
      allowNull: true
    },
    type: {
      type: DataTypes.STRING,
      allowNull: true
    },
    llm_provider: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    llm_model: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    appId: {
      type: DataTypes.TEXT,
      allowNull: true
    }
  },
  {
    tableName: 'chat',
    sequelize: AppDatabase.sequelize,
    timestamps: true,
    paranoid: true
  }
);