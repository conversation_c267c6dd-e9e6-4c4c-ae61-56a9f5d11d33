CREATE TABLE IF NOT EXISTS workspaces (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  location VARCHAR(255) NOT NULL
);

CREATE TABLE IF NOT EXISTS openapps (
  appId VARCHAR(255) PRIMARY KEY,
  workspaceId INTEGER,
  repoPath VARCHAR(255),
  apptilecookie TEXT DEFAULT ''
);

CREATE TABLE IF NOT EXISTS metro (
  pid INTEGER PRIMARY KEY,
  repoPath VARCHAR(255)
);

CREATE TABLE IF NOT EXISTS apikey (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  type VA<PERSON>HA<PERSON>(255),
  apikey VARCHAR(255)
);

CREATE TABLE IF NOT EXISTS chat (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  outputFile VARCHAR(255),
  type VARCHAR(255),
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS chat_message (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  chatId INTEGER,
  role VARCHAR(255),
  name VARCHAR(255),
  content TEXT,
  tool_call_id VARCHAR(255),
  createdAt DATETIME DEFAULT CURRENT_TIMESTAMP
);