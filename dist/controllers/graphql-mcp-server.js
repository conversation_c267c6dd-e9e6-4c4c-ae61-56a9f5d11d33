"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const mcp_js_1 = require("@modelcontextprotocol/sdk/server/mcp.js");
const stdio_js_1 = require("@modelcontextprotocol/sdk/server/stdio.js");
const zod_1 = require("zod");
const toolsForAgents_1 = require("./toolsForAgents");
const server = new mcp_js_1.McpServer({
    name: "graphql-schema-explorer",
    version: "1.0.0",
}, {
    capabilities: {
        tools: {}
    }
});
server.tool("enumerate_fields_at_path", "Enumerate the fields for an object in the graphql schema inside the query field. Mutations are not available throught this tool. To get the entire set of top level fields in the queries section pass path as a blank string i.e. ''", {
    path: zod_1.z.string().describe("A dot separated string specifying the field in the schema whose fields are to be enumerated. If a blank string is passed the top level of the schema will be enumerated."),
    includeDescriptions: zod_1.z.boolean().describe("Include the descriptions for the fields in the result"),
    includeArgumentsInfo: zod_1.z.boolean().describe("Include all available information about the arguments applicable to the fields being returned. Set this to true sparingly to save tokens.")
}, (inputVars) => __awaiter(void 0, void 0, void 0, function* () {
    const result = yield (0, toolsForAgents_1.enumerateFields)(inputVars.path, inputVars.includeDescriptions, inputVars.includeArgumentsInfo);
    return {
        content: [{
                type: "text",
                text: JSON.stringify(result)
            }]
    };
}));
function main() {
    return __awaiter(this, void 0, void 0, function* () {
        const transport = new stdio_js_1.StdioServerTransport();
        yield server.connect(transport);
        console.error("Apptile Graphql MCP Server running on stdio");
    });
}
main().catch((error) => {
    console.error("Fatal error in main():", error);
    process.exit(1);
});
