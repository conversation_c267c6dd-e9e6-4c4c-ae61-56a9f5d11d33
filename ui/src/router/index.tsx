import React from 'react';
import {createBrowserRouter} from 'react-router-dom';
import App from '../App';
// import Explorer from './Explorer';
import Workspaces, {WorkspaceListing} from './Workspaces';
import OpenedApp from './OpenedApp';
import SocketClients from './SocketClients';
import CurrentApp from './CurrentApp';
import WebSDK from './WebSDK';

export const router = createBrowserRouter(
  [
    {
      path: '/',
      element: <App />,
      children: [
        {
          path: 'workspaces',
          element: <Workspaces />,
          children: [
            {
              path: '',
              element: <WorkspaceListing />
            },
            {
              path: ':workspaceId/app/:appId',
              element: <OpenedApp />
            }
          ]
        },
        {
          path: 'currentApp',
          element: <CurrentApp />,
          children: [
            {
              path: '',
              element: <div>publishing</div>
            },
            {
              path: 'actions',
              element: <div>development</div>
            },
          ]
        },
        {
          path: 'socketClients',
          element: <SocketClients />
        },
        {
          path: 'webSdk',
          element: <WebSDK />
        }
        // {
        //   path: 'explore/:path/:depth',
        //   element: <Explorer />
        // }
      ]
    }
  ], 
  {
    basename: '/plugin-server/public/ui'
  }
);

