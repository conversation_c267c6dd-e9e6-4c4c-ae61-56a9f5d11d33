"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = default_1;
exports.ensurePlugins = ensurePlugins;
const promises_1 = require("node:fs/promises");
function default_1(req, res, next) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            // ensure that workspaces table has an entry linking path to ~/apptile-cli-home
            // ensure that ~/apptile-cli-home directory exists and has ReactNativeTSProjeect
            // ensure that the ReactNativeTSProjeect has a git repo and the remote is pointed to correct github
            next();
        }
        catch (err) {
            next(err);
        }
    });
}
function ensurePlugins(path) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            yield (0, promises_1.stat)(path);
        }
        catch (err) {
            if ((err === null || err === void 0 ? void 0 : err.code) === "ENOENT") {
                yield (0, promises_1.mkdir)(path, { recursive: true });
            }
        }
    });
}
