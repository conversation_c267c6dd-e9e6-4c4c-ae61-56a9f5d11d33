"use strict";
// Shared types
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileSystem = exports.DataBase = void 0;
var DataBase;
(function (DataBase) {
    function narrowWorkspace(workspace) {
        let W = workspace;
        try {
            return !!(W &&
                (typeof (W.id) === "number") &&
                (typeof (W.location) === "string"));
        }
        catch (err) {
            console.error("Failed to narrow workspace", err);
            return false;
        }
    }
    DataBase.narrowWorkspace = narrowWorkspace;
    function narrowWorkspaceArray(workspaces) {
        try {
            let result = true;
            if (!Array.isArray(workspaces)) {
                return false;
            }
            for (let workspace of workspaces) {
                result = result && narrowWorkspace(workspace);
                if (!result) {
                    break;
                }
            }
            return result;
        }
        catch (err) {
            console.error("Failed to narrow workspace array", err);
            return false;
        }
    }
    DataBase.narrowWorkspaceArray = narrowWorkspaceArray;
})(DataBase || (exports.DataBase = DataBase = {}));
var FileSystem;
(function (FileSystem) {
    function narrowMobileDevice(raw) {
        try {
            return (typeof raw.id === "string") &&
                (typeof raw.name === "string") &&
                (typeof raw.type === "string");
        }
        catch (err) {
            console.error("Failed to validate mobile device", err);
            return false;
        }
    }
    FileSystem.narrowMobileDevice = narrowMobileDevice;
    function narrowMobileDeviceArray(raw) {
        try {
            let result = true;
            for (let i = 0; i < raw.length; ++i) {
                result = result && narrowMobileDevice(raw[i]);
                if (!result) {
                    break;
                }
            }
            return result;
        }
        catch (err) {
            console.error("Failed to validate mobile device", err);
            return false;
        }
    }
    FileSystem.narrowMobileDeviceArray = narrowMobileDeviceArray;
    function narrowRepo(raw) {
        let result = (typeof raw.name === "string") &&
            (typeof raw.fullPath === "string") &&
            ((typeof raw.gitRepo === "string") || (raw.gitRepo === null)) &&
            (typeof raw.appId === "string") &&
            (typeof raw.apptileServer === "string") &&
            (typeof raw.appconfigServer === "string") &&
            (typeof raw.isOpen === "boolean");
        for (let i = 0; i < raw.codePushBundles.ios.length; ++i) {
            const bundle = raw.codePushBundles.ios[i];
            result = result && (typeof bundle.fullPath === "string") && (typeof bundle.timestamp === "number");
            if (!result)
                break;
        }
        for (let i = 0; i < raw.codePushBundles.android.length; ++i) {
            const bundle = raw.codePushBundles.android[i];
            result = result && (typeof bundle.fullPath === "string") && (typeof bundle.timestamp === "number");
            if (!result)
                break;
        }
        return result;
    }
    FileSystem.narrowRepo = narrowRepo;
    function narrowRepoArray(raw) {
        try {
            let result = true;
            for (let i = 0; i < raw.length; ++i) {
                result = result && narrowRepo(raw[i]);
                if (!result) {
                    break;
                }
            }
            if (!result) {
                console.error("Failed to narrow repo data");
            }
            return result;
        }
        catch (err) {
            console.error("Validation error: ", err);
            return false;
        }
    }
    FileSystem.narrowRepoArray = narrowRepoArray;
    function narrowWorkspaceData(raw) {
        try {
            let result = (typeof raw.id === "number") &&
                (typeof raw.sdkHash === "string") &&
                (typeof raw.location === "string") &&
                narrowRepoArray(raw.repos);
            return result;
        }
        catch (err) {
            console.error("Validation Error: ", err);
            return false;
        }
    }
    FileSystem.narrowWorkspaceData = narrowWorkspaceData;
    function narrowWorkspaceDataArray(raw) {
        try {
            let result = true;
            for (let i = 0; i < raw.length; ++i) {
                result = result && narrowWorkspaceData(raw[i]);
                if (!result) {
                    break;
                }
            }
            return result;
        }
        catch (err) {
            console.error("Validation Error: ", err);
            return false;
        }
    }
    FileSystem.narrowWorkspaceDataArray = narrowWorkspaceDataArray;
})(FileSystem || (exports.FileSystem = FileSystem = {}));
