import Anthropic from "@anthropic-ai/sdk";

export const tools: Anthropic.Tool[] = [
  // NOTE(gaurav): This was an idea I didn't explore where I could just get an entire
  // set of ops, just like the apply_file_ops tool and then run them on the frontend.
  // The problem is that tool calls don't stream well so it might actually be worse 
  // to wait for the agent to generate all the prompts before anything gets done.
  // {
  //   "name": "nocodelayer_appcreation_ops",
  //   "description": "This tool performs actions in the apptile nocode platform to build \
  //   up an app. These actions include creation of globalPlugins, screens, plugins, \
  //   code generation for plugins using an agent and dragging and dropping plugins \
  //   to screens.",
  //   "input_schema": {
  //     "type": "object",
  //     "properties": {
  //       "actions": {
  //         "type": "array",
  //         "items": {
  //         }
  //       }
  //     }
  //   }
  // },
  {
    "name": "nocodelayer_create_global_plugin",
    "description": "Create a globalPlugin in the apptile nocode platform",
    "input_schema": {
      "type": "object",
      "properties": {
        "name": {
          "type": "string",
          "description": "name of the globalPlugin. Must be a valid javascript name."
        },
        "pluginType": {
          "type": "string",
          "enum": ["LocalStoragePlugin", "StatePlugin"],
          "description": "The type of globalPlugin. StatePlugin stores values ephemerally, i.e. values are lost once the app is closed. LocalStoragePlugin automatically persists its values in LocalStorage and restores from there on startup."
        },
        "value": {
          "type": "string",
          "description": "The inital value exposed by the global plugin"
        }
      },
      "required": [
        "name",
        "value",
        "pluginType"
      ]
    }
  },
  {
    "name": "nocodelayer_create_screen",
    "description": "Create a @react-navigation screen in the nocode platform. All screens are added in a stack navigator.",
    "input_schema": {
      "type": "object",
      "required": [
        "name"
      ],
      "properties": {
        "name": {
          "type": "string",
          "description": "name of the screen to create"
        }
      }
    }
  },
  {
    "name": "nocodelayer_create_plugin",
    "description": "Create a plugin in the apptile nocode platform that can be dragged and dropped into a screen.",
    "input_schema": {
      "type": "object",
      "properties": {
        "name": {
          "type": "string",
          "description": "Name of the plugin. This must be a single word with all lowercase letters and no numbers."
        },
        "apptile_context": {
          "type": "object",
          "description": "Apptile context setup by the tool runner. Provide the object, but set null values.",
          "properties": {
            "appId": {
              "type": ["string", "null"],
              "description": "Must always be set to null. The tool will insert the correct value before running."
            }
          },
          "required": []
        }
      },
      "required": [
        "name",
        "apptile_context"
      ]
    }
  },
  {
    "name": "nocodelayer_generate_code_for_plugin",
    "description": "Genrate the code for a plugin in the nocode platform using an agent.",
    "input_schema": {
      "type": "object",
      "properties": {
        "name": {
          "type": "string",
          "description": "name of the plugin for which react-native code has to be generated"
        },
        "screen": {
          "type": "string",
          "description": "The nocode platform will navigate to this screen after the code generation is finished.\
          Specify the screen where you added this plugin"
        },
        "prompt": {
          "type": "string",
          "description": "The prompt to send to the agent for generating the code for the plugin."
        },
        "include_image": {
          "type": "boolean",
          "description": "If you are working off of an image i.e. an image was sent to you and you would want that image to be included with the prompt to the agent, you can set this to true."
        },
        "apptile_context": {
          "type": "object",
          "description": "Apptile context setup by the tool runner. Provide the object, but set null values.",
          "properties": {
            "appId": {
              "type": ["string", "null"],
              "description": "Must always be set to null. The tool will insert the correct value before running."
            }
          }
        }
      },
      "required": [
        "name",
        "prompt"
      ]
    }
  },
  {
    "name": "nocodelayer_add_plugin_to_screen",
    "description": "This will drag and drop the specified plugin at the bottom of the specified screen.",
    "input_schema": {
      "type": "object",
      "required": [
        "screen_name",
        "plugin_name"
      ],
      "properties": {
        "screen_name": {
          "type": "string",
          "description": "name of the screen to which the plugin should be added"
        },
        "plugin_name": {
          "type": "string",
          "description": "name of the plugin that should be added to the screen"
        }
      }
    }
  }
];
