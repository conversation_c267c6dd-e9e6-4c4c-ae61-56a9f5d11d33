/*! For license information please see main.ad1a955c.js.LICENSE.txt */
(()=>{"use strict";var e={43:(e,t,n)=>{e.exports=n(202)},67:(e,t,n)=>{e.exports=n.p+"static/media/code.2a279c3bb299ac98ccda.png"},153:(e,t,n)=>{var r=n(43),a=Symbol.for("react.element"),o=Symbol.for("react.fragment"),l=Object.prototype.hasOwnProperty,i=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,s={key:!0,ref:!0,__self:!0,__source:!0};function u(e,t,n){var r,o={},u=null,c=null;for(r in void 0!==n&&(u=""+n),void 0!==t.key&&(u=""+t.key),void 0!==t.ref&&(c=t.ref),t)l.call(t,r)&&!s.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===o[r]&&(o[r]=t[r]);return{$$typeof:a,type:e,key:u,ref:c,props:o,_owner:i.current}}t.Fragment=o,t.jsx=u,t.jsxs=u},192:(e,t,n)=>{e.exports=n.p+"static/media/finder.caba54ea88137ee694dc.png"},202:(e,t)=>{var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),l=Symbol.for("react.profiler"),i=Symbol.for("react.provider"),s=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),p=Symbol.iterator;var h={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,v={};function g(e,t,n){this.props=e,this.context=t,this.refs=v,this.updater=n||h}function y(){}function b(e,t,n){this.props=e,this.context=t,this.refs=v,this.updater=n||h}g.prototype.isReactComponent={},g.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},g.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},y.prototype=g.prototype;var w=b.prototype=new y;w.constructor=b,m(w,g.prototype),w.isPureReactComponent=!0;var x=Array.isArray,k=Object.prototype.hasOwnProperty,S={current:null},E={key:!0,ref:!0,__self:!0,__source:!0};function C(e,t,r){var a,o={},l=null,i=null;if(null!=t)for(a in void 0!==t.ref&&(i=t.ref),void 0!==t.key&&(l=""+t.key),t)k.call(t,a)&&!E.hasOwnProperty(a)&&(o[a]=t[a]);var s=arguments.length-2;if(1===s)o.children=r;else if(1<s){for(var u=Array(s),c=0;c<s;c++)u[c]=arguments[c+2];o.children=u}if(e&&e.defaultProps)for(a in s=e.defaultProps)void 0===o[a]&&(o[a]=s[a]);return{$$typeof:n,type:e,key:l,ref:i,props:o,_owner:S.current}}function _(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var j=/\/+/g;function P(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,(function(e){return t[e]}))}(""+e.key):t.toString(36)}function D(e,t,a,o,l){var i=typeof e;"undefined"!==i&&"boolean"!==i||(e=null);var s=!1;if(null===e)s=!0;else switch(i){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case n:case r:s=!0}}if(s)return l=l(s=e),e=""===o?"."+P(s,0):o,x(l)?(a="",null!=e&&(a=e.replace(j,"$&/")+"/"),D(l,t,a,"",(function(e){return e}))):null!=l&&(_(l)&&(l=function(e,t){return{$$typeof:n,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(l,a+(!l.key||s&&s.key===l.key?"":(""+l.key).replace(j,"$&/")+"/")+e)),t.push(l)),1;if(s=0,o=""===o?".":o+":",x(e))for(var u=0;u<e.length;u++){var c=o+P(i=e[u],u);s+=D(i,t,a,c,l)}else if(c=function(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=p&&e[p]||e["@@iterator"])?e:null}(e),"function"===typeof c)for(e=c.call(e),u=0;!(i=e.next()).done;)s+=D(i=i.value,t,a,c=o+P(i,u++),l);else if("object"===i)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function N(e,t,n){if(null==e)return e;var r=[],a=0;return D(e,r,"","",(function(e){return t.call(n,e,a++)})),r}function R(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var T={current:null},L={transition:null},I={ReactCurrentDispatcher:T,ReactCurrentBatchConfig:L,ReactCurrentOwner:S};function O(){throw Error("act(...) is not supported in production builds of React.")}t.Children={map:N,forEach:function(e,t,n){N(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return N(e,(function(){t++})),t},toArray:function(e){return N(e,(function(e){return e}))||[]},only:function(e){if(!_(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=g,t.Fragment=a,t.Profiler=l,t.PureComponent=b,t.StrictMode=o,t.Suspense=c,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=I,t.act=O,t.cloneElement=function(e,t,r){if(null===e||void 0===e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var a=m({},e.props),o=e.key,l=e.ref,i=e._owner;if(null!=t){if(void 0!==t.ref&&(l=t.ref,i=S.current),void 0!==t.key&&(o=""+t.key),e.type&&e.type.defaultProps)var s=e.type.defaultProps;for(u in t)k.call(t,u)&&!E.hasOwnProperty(u)&&(a[u]=void 0===t[u]&&void 0!==s?s[u]:t[u])}var u=arguments.length-2;if(1===u)a.children=r;else if(1<u){s=Array(u);for(var c=0;c<u;c++)s[c]=arguments[c+2];a.children=s}return{$$typeof:n,type:e.type,key:o,ref:l,props:a,_owner:i}},t.createContext=function(e){return(e={$$typeof:s,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:i,_context:e},e.Consumer=e},t.createElement=C,t.createFactory=function(e){var t=C.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:u,render:e}},t.isValidElement=_,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:R}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=L.transition;L.transition={};try{e()}finally{L.transition=t}},t.unstable_act=O,t.useCallback=function(e,t){return T.current.useCallback(e,t)},t.useContext=function(e){return T.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return T.current.useDeferredValue(e)},t.useEffect=function(e,t){return T.current.useEffect(e,t)},t.useId=function(){return T.current.useId()},t.useImperativeHandle=function(e,t,n){return T.current.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return T.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return T.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return T.current.useMemo(e,t)},t.useReducer=function(e,t,n){return T.current.useReducer(e,t,n)},t.useRef=function(e){return T.current.useRef(e)},t.useState=function(e){return T.current.useState(e)},t.useSyncExternalStore=function(e,t,n){return T.current.useSyncExternalStore(e,t,n)},t.useTransition=function(){return T.current.useTransition()},t.version="18.3.1"},234:(e,t)=>{function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,a=e[r];if(!(0<o(a,t)))break e;e[r]=t,e[n]=a,n=r}}function r(e){return 0===e.length?null:e[0]}function a(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,a=e.length,l=a>>>1;r<l;){var i=2*(r+1)-1,s=e[i],u=i+1,c=e[u];if(0>o(s,n))u<a&&0>o(c,s)?(e[r]=c,e[u]=n,r=u):(e[r]=s,e[i]=n,r=i);else{if(!(u<a&&0>o(c,n)))break e;e[r]=c,e[u]=n,r=u}}}return t}function o(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"===typeof performance&&"function"===typeof performance.now){var l=performance;t.unstable_now=function(){return l.now()}}else{var i=Date,s=i.now();t.unstable_now=function(){return i.now()-s}}var u=[],c=[],d=1,f=null,p=3,h=!1,m=!1,v=!1,g="function"===typeof setTimeout?setTimeout:null,y="function"===typeof clearTimeout?clearTimeout:null,b="undefined"!==typeof setImmediate?setImmediate:null;function w(e){for(var t=r(c);null!==t;){if(null===t.callback)a(c);else{if(!(t.startTime<=e))break;a(c),t.sortIndex=t.expirationTime,n(u,t)}t=r(c)}}function x(e){if(v=!1,w(e),!m)if(null!==r(u))m=!0,L(k);else{var t=r(c);null!==t&&I(x,t.startTime-e)}}function k(e,n){m=!1,v&&(v=!1,y(_),_=-1),h=!0;var o=p;try{for(w(n),f=r(u);null!==f&&(!(f.expirationTime>n)||e&&!D());){var l=f.callback;if("function"===typeof l){f.callback=null,p=f.priorityLevel;var i=l(f.expirationTime<=n);n=t.unstable_now(),"function"===typeof i?f.callback=i:f===r(u)&&a(u),w(n)}else a(u);f=r(u)}if(null!==f)var s=!0;else{var d=r(c);null!==d&&I(x,d.startTime-n),s=!1}return s}finally{f=null,p=o,h=!1}}"undefined"!==typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var S,E=!1,C=null,_=-1,j=5,P=-1;function D(){return!(t.unstable_now()-P<j)}function N(){if(null!==C){var e=t.unstable_now();P=e;var n=!0;try{n=C(!0,e)}finally{n?S():(E=!1,C=null)}}else E=!1}if("function"===typeof b)S=function(){b(N)};else if("undefined"!==typeof MessageChannel){var R=new MessageChannel,T=R.port2;R.port1.onmessage=N,S=function(){T.postMessage(null)}}else S=function(){g(N,0)};function L(e){C=e,E||(E=!0,S())}function I(e,n){_=g((function(){e(t.unstable_now())}),n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){m||h||(m=!0,L(k))},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):j=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return p},t.unstable_getFirstCallbackNode=function(){return r(u)},t.unstable_next=function(e){switch(p){case 1:case 2:case 3:var t=3;break;default:t=p}var n=p;p=t;try{return e()}finally{p=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=p;p=e;try{return t()}finally{p=n}},t.unstable_scheduleCallback=function(e,a,o){var l=t.unstable_now();switch("object"===typeof o&&null!==o?o="number"===typeof(o=o.delay)&&0<o?l+o:l:o=l,e){case 1:var i=-1;break;case 2:i=250;break;case 5:i=1073741823;break;case 4:i=1e4;break;default:i=5e3}return e={id:d++,callback:a,priorityLevel:e,startTime:o,expirationTime:i=o+i,sortIndex:-1},o>l?(e.sortIndex=o,n(c,e),null===r(u)&&e===r(c)&&(v?(y(_),_=-1):v=!0,I(x,o-l))):(e.sortIndex=i,n(u,e),m||h||(m=!0,L(k))),e},t.unstable_shouldYield=D,t.unstable_wrapCallback=function(e){var t=p;return function(){var n=p;p=t;try{return e.apply(this,arguments)}finally{p=n}}}},391:(e,t,n)=>{var r=n(950);t.createRoot=r.createRoot,t.hydrateRoot=r.hydrateRoot},449:e=>{e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAHdSURBVHgBvVeLbYNADDVdoHSDG4ENwgjpBMkG6QbtBkknIBtEnaDdADoBbEA6gWuXQ3GR7wOc8qSnSPhvkzuTQSQQMaefLXFDLIiGmFvxldgRG+IX8SPLsiukAAU2xDOxx3mo2BaWgismHnE9jrZ7s6tuMR1ajO0GKRaYNrhMorh35VoSBjwzbxWDCy5Lil/ailgrPnMtAe2F2wn5Hm+J9Nbxp2AvZNKuVPwetdZrKJQuGfCPMI/0baTSOai0EJ4EKlmVC3tYCdRHwOBR5Q8wHK8aOhiO1bVorK8phqPd0/4dJIKnCxULa4dw3vEZTkK7S2oegVH0u2S3mfCpPDOcQNJKPdAK+nsJ7wW10Ixn4xA+pRwDD1x5fOUOdA6bDaQLXjpEHSfw7RC+QDrsHc+b8ZJxYXUS5OPg8b8bj2LfvvcKCxEIzshHRXkavhG3+H8HaK2zMiJoYXXrQPBKGplJsK3tjObk4gl+wXiYqfFJCHublLailZ4EysjgJ814upKdRHc4uYp4gPAIQmjRdc8oFRcwExHBjdTPFAcclOc8Kp5huNN/iI90Or5DIAGHqCM+k30DEVU41/MI26jKo4DDX3JtAidcs1vg7eM0NoEeb98EBlIBAyu50DNzKv4F8Mg9kvfoSM0AAAAASUVORK5CYII="},579:(e,t,n)=>{e.exports=n(153)},730:(e,t,n)=>{var r=n(43),a=n(853);function o(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var l=new Set,i={};function s(e,t){u(e,t),u(e+"Capture",t)}function u(e,t){for(i[e]=t,e=0;e<t.length;e++)l.add(t[e])}var c=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),d=Object.prototype.hasOwnProperty,f=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,p={},h={};function m(e,t,n,r,a,o,l){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=a,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=l}var v={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach((function(e){v[e]=new m(e,0,!1,e,null,!1,!1)})),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach((function(e){var t=e[0];v[t]=new m(t,1,!1,e[1],null,!1,!1)})),["contentEditable","draggable","spellCheck","value"].forEach((function(e){v[e]=new m(e,2,!1,e.toLowerCase(),null,!1,!1)})),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach((function(e){v[e]=new m(e,2,!1,e,null,!1,!1)})),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach((function(e){v[e]=new m(e,3,!1,e.toLowerCase(),null,!1,!1)})),["checked","multiple","muted","selected"].forEach((function(e){v[e]=new m(e,3,!0,e,null,!1,!1)})),["capture","download"].forEach((function(e){v[e]=new m(e,4,!1,e,null,!1,!1)})),["cols","rows","size","span"].forEach((function(e){v[e]=new m(e,6,!1,e,null,!1,!1)})),["rowSpan","start"].forEach((function(e){v[e]=new m(e,5,!1,e.toLowerCase(),null,!1,!1)}));var g=/[\-:]([a-z])/g;function y(e){return e[1].toUpperCase()}function b(e,t,n,r){var a=v.hasOwnProperty(t)?v[t]:null;(null!==a?0!==a.type:r||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,n,r){if(null===t||"undefined"===typeof t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,a,r)&&(n=null),r||null===a?function(e){return!!d.call(h,e)||!d.call(p,e)&&(f.test(e)?h[e]=!0:(p[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):a.mustUseProperty?e[a.propertyName]=null===n?3!==a.type&&"":n:(t=a.attributeName,r=a.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(a=a.type)||4===a&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach((function(e){var t=e.replace(g,y);v[t]=new m(t,1,!1,e,null,!1,!1)})),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach((function(e){var t=e.replace(g,y);v[t]=new m(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)})),["xml:base","xml:lang","xml:space"].forEach((function(e){var t=e.replace(g,y);v[t]=new m(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)})),["tabIndex","crossOrigin"].forEach((function(e){v[e]=new m(e,1,!1,e.toLowerCase(),null,!1,!1)})),v.xlinkHref=new m("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach((function(e){v[e]=new m(e,1,!1,e.toLowerCase(),null,!0,!0)}));var w=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,x=Symbol.for("react.element"),k=Symbol.for("react.portal"),S=Symbol.for("react.fragment"),E=Symbol.for("react.strict_mode"),C=Symbol.for("react.profiler"),_=Symbol.for("react.provider"),j=Symbol.for("react.context"),P=Symbol.for("react.forward_ref"),D=Symbol.for("react.suspense"),N=Symbol.for("react.suspense_list"),R=Symbol.for("react.memo"),T=Symbol.for("react.lazy");Symbol.for("react.scope"),Symbol.for("react.debug_trace_mode");var L=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden"),Symbol.for("react.cache"),Symbol.for("react.tracing_marker");var I=Symbol.iterator;function O(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=I&&e[I]||e["@@iterator"])?e:null}var A,z=Object.assign;function M(e){if(void 0===A)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);A=t&&t[1]||""}return"\n"+A+e}var F=!1;function U(e,t){if(!e||F)return"";F=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&"string"===typeof u.stack){for(var a=u.stack.split("\n"),o=r.stack.split("\n"),l=a.length-1,i=o.length-1;1<=l&&0<=i&&a[l]!==o[i];)i--;for(;1<=l&&0<=i;l--,i--)if(a[l]!==o[i]){if(1!==l||1!==i)do{if(l--,0>--i||a[l]!==o[i]){var s="\n"+a[l].replace(" at new "," at ");return e.displayName&&s.includes("<anonymous>")&&(s=s.replace("<anonymous>",e.displayName)),s}}while(1<=l&&0<=i);break}}}finally{F=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?M(e):""}function B(e){switch(e.tag){case 5:return M(e.type);case 16:return M("Lazy");case 13:return M("Suspense");case 19:return M("SuspenseList");case 0:case 2:case 15:return e=U(e.type,!1);case 11:return e=U(e.type.render,!1);case 1:return e=U(e.type,!0);default:return""}}function $(e){if(null==e)return null;if("function"===typeof e)return e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case S:return"Fragment";case k:return"Portal";case C:return"Profiler";case E:return"StrictMode";case D:return"Suspense";case N:return"SuspenseList"}if("object"===typeof e)switch(e.$$typeof){case j:return(e.displayName||"Context")+".Consumer";case _:return(e._context.displayName||"Context")+".Provider";case P:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case R:return null!==(t=e.displayName||null)?t:$(e.type)||"Memo";case T:t=e._payload,e=e._init;try{return $(e(t))}catch(n){}}return null}function W(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return $(t);case 8:return t===E?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"===typeof t)return t.displayName||t.name||null;if("string"===typeof t)return t}return null}function H(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function V(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function Q(e){e._valueTracker||(e._valueTracker=function(e){var t=V(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var a=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(e){r=""+e,o.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function K(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=V(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function q(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function Y(e,t){var n=t.checked;return z({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function J(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=H(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function X(e,t){null!=(t=t.checked)&&b(e,"checked",t,!1)}function G(e,t){X(e,t);var n=H(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?ee(e,t.type,n):t.hasOwnProperty("defaultValue")&&ee(e,t.type,H(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function Z(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function ee(e,t,n){"number"===t&&q(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var te=Array.isArray;function ne(e,t,n,r){if(e=e.options,t){t={};for(var a=0;a<n.length;a++)t["$"+n[a]]=!0;for(n=0;n<e.length;n++)a=t.hasOwnProperty("$"+e[n].value),e[n].selected!==a&&(e[n].selected=a),a&&r&&(e[n].defaultSelected=!0)}else{for(n=""+H(n),t=null,a=0;a<e.length;a++){if(e[a].value===n)return e[a].selected=!0,void(r&&(e[a].defaultSelected=!0));null!==t||e[a].disabled||(t=e[a])}null!==t&&(t.selected=!0)}}function re(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(o(91));return z({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ae(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(o(92));if(te(n)){if(1<n.length)throw Error(o(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:H(n)}}function oe(e,t){var n=H(t.value),r=H(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function le(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function ie(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function se(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?ie(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var ue,ce,de=(ce=function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((ue=ue||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ue.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}},"undefined"!==typeof MSApp&&MSApp.execUnsafeLocalFunction?function(e,t,n,r){MSApp.execUnsafeLocalFunction((function(){return ce(e,t)}))}:ce);function fe(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var pe={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},he=["Webkit","ms","Moz","O"];function me(e,t,n){return null==t||"boolean"===typeof t||""===t?"":n||"number"!==typeof t||0===t||pe.hasOwnProperty(e)&&pe[e]?(""+t).trim():t+"px"}function ve(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),a=me(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,a):e[n]=a}}Object.keys(pe).forEach((function(e){he.forEach((function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),pe[t]=pe[e]}))}));var ge=z({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ye(e,t){if(t){if(ge[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(o(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(o(60));if("object"!==typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(o(61))}if(null!=t.style&&"object"!==typeof t.style)throw Error(o(62))}}function be(e,t){if(-1===e.indexOf("-"))return"string"===typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var we=null;function xe(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var ke=null,Se=null,Ee=null;function Ce(e){if(e=ba(e)){if("function"!==typeof ke)throw Error(o(280));var t=e.stateNode;t&&(t=xa(t),ke(e.stateNode,e.type,t))}}function _e(e){Se?Ee?Ee.push(e):Ee=[e]:Se=e}function je(){if(Se){var e=Se,t=Ee;if(Ee=Se=null,Ce(e),t)for(e=0;e<t.length;e++)Ce(t[e])}}function Pe(e,t){return e(t)}function De(){}var Ne=!1;function Re(e,t,n){if(Ne)return e(t,n);Ne=!0;try{return Pe(e,t,n)}finally{Ne=!1,(null!==Se||null!==Ee)&&(De(),je())}}function Te(e,t){var n=e.stateNode;if(null===n)return null;var r=xa(n);if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(o(231,t,typeof n));return n}var Le=!1;if(c)try{var Ie={};Object.defineProperty(Ie,"passive",{get:function(){Le=!0}}),window.addEventListener("test",Ie,Ie),window.removeEventListener("test",Ie,Ie)}catch(ce){Le=!1}function Oe(e,t,n,r,a,o,l,i,s){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var Ae=!1,ze=null,Me=!1,Fe=null,Ue={onError:function(e){Ae=!0,ze=e}};function Be(e,t,n,r,a,o,l,i,s){Ae=!1,ze=null,Oe.apply(Ue,arguments)}function $e(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function We(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function He(e){if($e(e)!==e)throw Error(o(188))}function Ve(e){return null!==(e=function(e){var t=e.alternate;if(!t){if(null===(t=$e(e)))throw Error(o(188));return t!==e?null:e}for(var n=e,r=t;;){var a=n.return;if(null===a)break;var l=a.alternate;if(null===l){if(null!==(r=a.return)){n=r;continue}break}if(a.child===l.child){for(l=a.child;l;){if(l===n)return He(a),e;if(l===r)return He(a),t;l=l.sibling}throw Error(o(188))}if(n.return!==r.return)n=a,r=l;else{for(var i=!1,s=a.child;s;){if(s===n){i=!0,n=a,r=l;break}if(s===r){i=!0,r=a,n=l;break}s=s.sibling}if(!i){for(s=l.child;s;){if(s===n){i=!0,n=l,r=a;break}if(s===r){i=!0,r=l,n=a;break}s=s.sibling}if(!i)throw Error(o(189))}}if(n.alternate!==r)throw Error(o(190))}if(3!==n.tag)throw Error(o(188));return n.stateNode.current===n?e:t}(e))?Qe(e):null}function Qe(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=Qe(e);if(null!==t)return t;e=e.sibling}return null}var Ke=a.unstable_scheduleCallback,qe=a.unstable_cancelCallback,Ye=a.unstable_shouldYield,Je=a.unstable_requestPaint,Xe=a.unstable_now,Ge=a.unstable_getCurrentPriorityLevel,Ze=a.unstable_ImmediatePriority,et=a.unstable_UserBlockingPriority,tt=a.unstable_NormalPriority,nt=a.unstable_LowPriority,rt=a.unstable_IdlePriority,at=null,ot=null;var lt=Math.clz32?Math.clz32:function(e){return e>>>=0,0===e?32:31-(it(e)/st|0)|0},it=Math.log,st=Math.LN2;var ut=64,ct=4194304;function dt(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ft(e,t){var n=e.pendingLanes;if(0===n)return 0;var r=0,a=e.suspendedLanes,o=e.pingedLanes,l=268435455&n;if(0!==l){var i=l&~a;0!==i?r=dt(i):0!==(o&=l)&&(r=dt(o))}else 0!==(l=n&~a)?r=dt(l):0!==o&&(r=dt(o));if(0===r)return 0;if(0!==t&&t!==r&&0===(t&a)&&((a=r&-r)>=(o=t&-t)||16===a&&0!==(4194240&o)))return t;if(0!==(4&r)&&(r|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=r;0<t;)a=1<<(n=31-lt(t)),r|=e[n],t&=~a;return r}function pt(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function ht(e){return 0!==(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function mt(){var e=ut;return 0===(4194240&(ut<<=1))&&(ut=64),e}function vt(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function gt(e,t,n){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-lt(t)]=n}function yt(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-lt(n),a=1<<r;a&t|e[r]&t&&(e[r]|=t),n&=~a}}var bt=0;function wt(e){return 1<(e&=-e)?4<e?0!==(268435455&e)?16:536870912:4:1}var xt,kt,St,Et,Ct,_t=!1,jt=[],Pt=null,Dt=null,Nt=null,Rt=new Map,Tt=new Map,Lt=[],It="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Ot(e,t){switch(e){case"focusin":case"focusout":Pt=null;break;case"dragenter":case"dragleave":Dt=null;break;case"mouseover":case"mouseout":Nt=null;break;case"pointerover":case"pointerout":Rt.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Tt.delete(t.pointerId)}}function At(e,t,n,r,a,o){return null===e||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[a]},null!==t&&(null!==(t=ba(t))&&kt(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==a&&-1===t.indexOf(a)&&t.push(a),e)}function zt(e){var t=ya(e.target);if(null!==t){var n=$e(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=We(n)))return e.blockedOn=t,void Ct(e.priority,(function(){St(n)}))}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function Mt(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=Yt(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=ba(n))&&kt(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);we=r,n.target.dispatchEvent(r),we=null,t.shift()}return!0}function Ft(e,t,n){Mt(e)&&n.delete(t)}function Ut(){_t=!1,null!==Pt&&Mt(Pt)&&(Pt=null),null!==Dt&&Mt(Dt)&&(Dt=null),null!==Nt&&Mt(Nt)&&(Nt=null),Rt.forEach(Ft),Tt.forEach(Ft)}function Bt(e,t){e.blockedOn===t&&(e.blockedOn=null,_t||(_t=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,Ut)))}function $t(e){function t(t){return Bt(t,e)}if(0<jt.length){Bt(jt[0],e);for(var n=1;n<jt.length;n++){var r=jt[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==Pt&&Bt(Pt,e),null!==Dt&&Bt(Dt,e),null!==Nt&&Bt(Nt,e),Rt.forEach(t),Tt.forEach(t),n=0;n<Lt.length;n++)(r=Lt[n]).blockedOn===e&&(r.blockedOn=null);for(;0<Lt.length&&null===(n=Lt[0]).blockedOn;)zt(n),null===n.blockedOn&&Lt.shift()}var Wt=w.ReactCurrentBatchConfig,Ht=!0;function Vt(e,t,n,r){var a=bt,o=Wt.transition;Wt.transition=null;try{bt=1,Kt(e,t,n,r)}finally{bt=a,Wt.transition=o}}function Qt(e,t,n,r){var a=bt,o=Wt.transition;Wt.transition=null;try{bt=4,Kt(e,t,n,r)}finally{bt=a,Wt.transition=o}}function Kt(e,t,n,r){if(Ht){var a=Yt(e,t,n,r);if(null===a)Hr(e,t,r,qt,n),Ot(e,r);else if(function(e,t,n,r,a){switch(t){case"focusin":return Pt=At(Pt,e,t,n,r,a),!0;case"dragenter":return Dt=At(Dt,e,t,n,r,a),!0;case"mouseover":return Nt=At(Nt,e,t,n,r,a),!0;case"pointerover":var o=a.pointerId;return Rt.set(o,At(Rt.get(o)||null,e,t,n,r,a)),!0;case"gotpointercapture":return o=a.pointerId,Tt.set(o,At(Tt.get(o)||null,e,t,n,r,a)),!0}return!1}(a,e,t,n,r))r.stopPropagation();else if(Ot(e,r),4&t&&-1<It.indexOf(e)){for(;null!==a;){var o=ba(a);if(null!==o&&xt(o),null===(o=Yt(e,t,n,r))&&Hr(e,t,r,qt,n),o===a)break;a=o}null!==a&&r.stopPropagation()}else Hr(e,t,r,null,n)}}var qt=null;function Yt(e,t,n,r){if(qt=null,null!==(e=ya(e=xe(r))))if(null===(t=$e(e)))e=null;else if(13===(n=t.tag)){if(null!==(e=We(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return qt=e,null}function Jt(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Ge()){case Ze:return 1;case et:return 4;case tt:case nt:return 16;case rt:return 536870912;default:return 16}default:return 16}}var Xt=null,Gt=null,Zt=null;function en(){if(Zt)return Zt;var e,t,n=Gt,r=n.length,a="value"in Xt?Xt.value:Xt.textContent,o=a.length;for(e=0;e<r&&n[e]===a[e];e++);var l=r-e;for(t=1;t<=l&&n[r-t]===a[o-t];t++);return Zt=a.slice(e,1<t?1-t:void 0)}function tn(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function nn(){return!0}function rn(){return!1}function an(e){function t(t,n,r,a,o){for(var l in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=a,this.target=o,this.currentTarget=null,e)e.hasOwnProperty(l)&&(t=e[l],this[l]=t?t(a):a[l]);return this.isDefaultPrevented=(null!=a.defaultPrevented?a.defaultPrevented:!1===a.returnValue)?nn:rn,this.isPropagationStopped=rn,this}return z(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=nn)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=nn)},persist:function(){},isPersistent:nn}),t}var on,ln,sn,un={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},cn=an(un),dn=z({},un,{view:0,detail:0}),fn=an(dn),pn=z({},dn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Cn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==sn&&(sn&&"mousemove"===e.type?(on=e.screenX-sn.screenX,ln=e.screenY-sn.screenY):ln=on=0,sn=e),on)},movementY:function(e){return"movementY"in e?e.movementY:ln}}),hn=an(pn),mn=an(z({},pn,{dataTransfer:0})),vn=an(z({},dn,{relatedTarget:0})),gn=an(z({},un,{animationName:0,elapsedTime:0,pseudoElement:0})),yn=z({},un,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),bn=an(yn),wn=an(z({},un,{data:0})),xn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},kn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Sn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function En(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=Sn[e])&&!!t[e]}function Cn(){return En}var _n=z({},dn,{key:function(e){if(e.key){var t=xn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=tn(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?kn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Cn,charCode:function(e){return"keypress"===e.type?tn(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?tn(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),jn=an(_n),Pn=an(z({},pn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),Dn=an(z({},dn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Cn})),Nn=an(z({},un,{propertyName:0,elapsedTime:0,pseudoElement:0})),Rn=z({},pn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Tn=an(Rn),Ln=[9,13,27,32],In=c&&"CompositionEvent"in window,On=null;c&&"documentMode"in document&&(On=document.documentMode);var An=c&&"TextEvent"in window&&!On,zn=c&&(!In||On&&8<On&&11>=On),Mn=String.fromCharCode(32),Fn=!1;function Un(e,t){switch(e){case"keyup":return-1!==Ln.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Bn(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var $n=!1;var Wn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Hn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Wn[e.type]:"textarea"===t}function Vn(e,t,n,r){_e(r),0<(t=Qr(t,"onChange")).length&&(n=new cn("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Qn=null,Kn=null;function qn(e){Mr(e,0)}function Yn(e){if(K(wa(e)))return e}function Jn(e,t){if("change"===e)return t}var Xn=!1;if(c){var Gn;if(c){var Zn="oninput"in document;if(!Zn){var er=document.createElement("div");er.setAttribute("oninput","return;"),Zn="function"===typeof er.oninput}Gn=Zn}else Gn=!1;Xn=Gn&&(!document.documentMode||9<document.documentMode)}function tr(){Qn&&(Qn.detachEvent("onpropertychange",nr),Kn=Qn=null)}function nr(e){if("value"===e.propertyName&&Yn(Kn)){var t=[];Vn(t,Kn,e,xe(e)),Re(qn,t)}}function rr(e,t,n){"focusin"===e?(tr(),Kn=n,(Qn=t).attachEvent("onpropertychange",nr)):"focusout"===e&&tr()}function ar(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Yn(Kn)}function or(e,t){if("click"===e)return Yn(t)}function lr(e,t){if("input"===e||"change"===e)return Yn(t)}var ir="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function sr(e,t){if(ir(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var a=n[r];if(!d.call(t,a)||!ir(e[a],t[a]))return!1}return!0}function ur(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function cr(e,t){var n,r=ur(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=ur(r)}}function dr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?dr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function fr(){for(var e=window,t=q();t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=q((e=t.contentWindow).document)}return t}function pr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function hr(e){var t=fr(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&dr(n.ownerDocument.documentElement,n)){if(null!==r&&pr(n))if(t=r.start,void 0===(e=r.end)&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if((e=(t=n.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var a=n.textContent.length,o=Math.min(r.start,a);r=void 0===r.end?o:Math.min(r.end,a),!e.extend&&o>r&&(a=r,r=o,o=a),a=cr(n,o);var l=cr(n,r);a&&l&&(1!==e.rangeCount||e.anchorNode!==a.node||e.anchorOffset!==a.offset||e.focusNode!==l.node||e.focusOffset!==l.offset)&&((t=t.createRange()).setStart(a.node,a.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(l.node,l.offset)):(t.setEnd(l.node,l.offset),e.addRange(t)))}for(t=[],e=n;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"===typeof n.focus&&n.focus(),n=0;n<t.length;n++)(e=t[n]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var mr=c&&"documentMode"in document&&11>=document.documentMode,vr=null,gr=null,yr=null,br=!1;function wr(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;br||null==vr||vr!==q(r)||("selectionStart"in(r=vr)&&pr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},yr&&sr(yr,r)||(yr=r,0<(r=Qr(gr,"onSelect")).length&&(t=new cn("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=vr)))}function xr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var kr={animationend:xr("Animation","AnimationEnd"),animationiteration:xr("Animation","AnimationIteration"),animationstart:xr("Animation","AnimationStart"),transitionend:xr("Transition","TransitionEnd")},Sr={},Er={};function Cr(e){if(Sr[e])return Sr[e];if(!kr[e])return e;var t,n=kr[e];for(t in n)if(n.hasOwnProperty(t)&&t in Er)return Sr[e]=n[t];return e}c&&(Er=document.createElement("div").style,"AnimationEvent"in window||(delete kr.animationend.animation,delete kr.animationiteration.animation,delete kr.animationstart.animation),"TransitionEvent"in window||delete kr.transitionend.transition);var _r=Cr("animationend"),jr=Cr("animationiteration"),Pr=Cr("animationstart"),Dr=Cr("transitionend"),Nr=new Map,Rr="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Tr(e,t){Nr.set(e,t),s(t,[e])}for(var Lr=0;Lr<Rr.length;Lr++){var Ir=Rr[Lr];Tr(Ir.toLowerCase(),"on"+(Ir[0].toUpperCase()+Ir.slice(1)))}Tr(_r,"onAnimationEnd"),Tr(jr,"onAnimationIteration"),Tr(Pr,"onAnimationStart"),Tr("dblclick","onDoubleClick"),Tr("focusin","onFocus"),Tr("focusout","onBlur"),Tr(Dr,"onTransitionEnd"),u("onMouseEnter",["mouseout","mouseover"]),u("onMouseLeave",["mouseout","mouseover"]),u("onPointerEnter",["pointerout","pointerover"]),u("onPointerLeave",["pointerout","pointerover"]),s("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),s("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),s("onBeforeInput",["compositionend","keypress","textInput","paste"]),s("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),s("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),s("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Or="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Ar=new Set("cancel close invalid load scroll toggle".split(" ").concat(Or));function zr(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,function(e,t,n,r,a,l,i,s,u){if(Be.apply(this,arguments),Ae){if(!Ae)throw Error(o(198));var c=ze;Ae=!1,ze=null,Me||(Me=!0,Fe=c)}}(r,t,void 0,e),e.currentTarget=null}function Mr(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],a=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var l=r.length-1;0<=l;l--){var i=r[l],s=i.instance,u=i.currentTarget;if(i=i.listener,s!==o&&a.isPropagationStopped())break e;zr(a,i,u),o=s}else for(l=0;l<r.length;l++){if(s=(i=r[l]).instance,u=i.currentTarget,i=i.listener,s!==o&&a.isPropagationStopped())break e;zr(a,i,u),o=s}}}if(Me)throw e=Fe,Me=!1,Fe=null,e}function Fr(e,t){var n=t[ma];void 0===n&&(n=t[ma]=new Set);var r=e+"__bubble";n.has(r)||(Wr(t,e,2,!1),n.add(r))}function Ur(e,t,n){var r=0;t&&(r|=4),Wr(n,e,r,t)}var Br="_reactListening"+Math.random().toString(36).slice(2);function $r(e){if(!e[Br]){e[Br]=!0,l.forEach((function(t){"selectionchange"!==t&&(Ar.has(t)||Ur(t,!1,e),Ur(t,!0,e))}));var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Br]||(t[Br]=!0,Ur("selectionchange",!1,t))}}function Wr(e,t,n,r){switch(Jt(t)){case 1:var a=Vt;break;case 4:a=Qt;break;default:a=Kt}n=a.bind(null,t,n,e),a=void 0,!Le||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(a=!0),r?void 0!==a?e.addEventListener(t,n,{capture:!0,passive:a}):e.addEventListener(t,n,!0):void 0!==a?e.addEventListener(t,n,{passive:a}):e.addEventListener(t,n,!1)}function Hr(e,t,n,r,a){var o=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var l=r.tag;if(3===l||4===l){var i=r.stateNode.containerInfo;if(i===a||8===i.nodeType&&i.parentNode===a)break;if(4===l)for(l=r.return;null!==l;){var s=l.tag;if((3===s||4===s)&&((s=l.stateNode.containerInfo)===a||8===s.nodeType&&s.parentNode===a))return;l=l.return}for(;null!==i;){if(null===(l=ya(i)))return;if(5===(s=l.tag)||6===s){r=o=l;continue e}i=i.parentNode}}r=r.return}Re((function(){var r=o,a=xe(n),l=[];e:{var i=Nr.get(e);if(void 0!==i){var s=cn,u=e;switch(e){case"keypress":if(0===tn(n))break e;case"keydown":case"keyup":s=jn;break;case"focusin":u="focus",s=vn;break;case"focusout":u="blur",s=vn;break;case"beforeblur":case"afterblur":s=vn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":s=hn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":s=mn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":s=Dn;break;case _r:case jr:case Pr:s=gn;break;case Dr:s=Nn;break;case"scroll":s=fn;break;case"wheel":s=Tn;break;case"copy":case"cut":case"paste":s=bn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":s=Pn}var c=0!==(4&t),d=!c&&"scroll"===e,f=c?null!==i?i+"Capture":null:i;c=[];for(var p,h=r;null!==h;){var m=(p=h).stateNode;if(5===p.tag&&null!==m&&(p=m,null!==f&&(null!=(m=Te(h,f))&&c.push(Vr(h,m,p)))),d)break;h=h.return}0<c.length&&(i=new s(i,u,null,n,a),l.push({event:i,listeners:c}))}}if(0===(7&t)){if(s="mouseout"===e||"pointerout"===e,(!(i="mouseover"===e||"pointerover"===e)||n===we||!(u=n.relatedTarget||n.fromElement)||!ya(u)&&!u[ha])&&(s||i)&&(i=a.window===a?a:(i=a.ownerDocument)?i.defaultView||i.parentWindow:window,s?(s=r,null!==(u=(u=n.relatedTarget||n.toElement)?ya(u):null)&&(u!==(d=$e(u))||5!==u.tag&&6!==u.tag)&&(u=null)):(s=null,u=r),s!==u)){if(c=hn,m="onMouseLeave",f="onMouseEnter",h="mouse","pointerout"!==e&&"pointerover"!==e||(c=Pn,m="onPointerLeave",f="onPointerEnter",h="pointer"),d=null==s?i:wa(s),p=null==u?i:wa(u),(i=new c(m,h+"leave",s,n,a)).target=d,i.relatedTarget=p,m=null,ya(a)===r&&((c=new c(f,h+"enter",u,n,a)).target=p,c.relatedTarget=d,m=c),d=m,s&&u)e:{for(f=u,h=0,p=c=s;p;p=Kr(p))h++;for(p=0,m=f;m;m=Kr(m))p++;for(;0<h-p;)c=Kr(c),h--;for(;0<p-h;)f=Kr(f),p--;for(;h--;){if(c===f||null!==f&&c===f.alternate)break e;c=Kr(c),f=Kr(f)}c=null}else c=null;null!==s&&qr(l,i,s,c,!1),null!==u&&null!==d&&qr(l,d,u,c,!0)}if("select"===(s=(i=r?wa(r):window).nodeName&&i.nodeName.toLowerCase())||"input"===s&&"file"===i.type)var v=Jn;else if(Hn(i))if(Xn)v=lr;else{v=ar;var g=rr}else(s=i.nodeName)&&"input"===s.toLowerCase()&&("checkbox"===i.type||"radio"===i.type)&&(v=or);switch(v&&(v=v(e,r))?Vn(l,v,n,a):(g&&g(e,i,r),"focusout"===e&&(g=i._wrapperState)&&g.controlled&&"number"===i.type&&ee(i,"number",i.value)),g=r?wa(r):window,e){case"focusin":(Hn(g)||"true"===g.contentEditable)&&(vr=g,gr=r,yr=null);break;case"focusout":yr=gr=vr=null;break;case"mousedown":br=!0;break;case"contextmenu":case"mouseup":case"dragend":br=!1,wr(l,n,a);break;case"selectionchange":if(mr)break;case"keydown":case"keyup":wr(l,n,a)}var y;if(In)e:{switch(e){case"compositionstart":var b="onCompositionStart";break e;case"compositionend":b="onCompositionEnd";break e;case"compositionupdate":b="onCompositionUpdate";break e}b=void 0}else $n?Un(e,n)&&(b="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(b="onCompositionStart");b&&(zn&&"ko"!==n.locale&&($n||"onCompositionStart"!==b?"onCompositionEnd"===b&&$n&&(y=en()):(Gt="value"in(Xt=a)?Xt.value:Xt.textContent,$n=!0)),0<(g=Qr(r,b)).length&&(b=new wn(b,e,null,n,a),l.push({event:b,listeners:g}),y?b.data=y:null!==(y=Bn(n))&&(b.data=y))),(y=An?function(e,t){switch(e){case"compositionend":return Bn(t);case"keypress":return 32!==t.which?null:(Fn=!0,Mn);case"textInput":return(e=t.data)===Mn&&Fn?null:e;default:return null}}(e,n):function(e,t){if($n)return"compositionend"===e||!In&&Un(e,t)?(e=en(),Zt=Gt=Xt=null,$n=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return zn&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(r=Qr(r,"onBeforeInput")).length&&(a=new wn("onBeforeInput","beforeinput",null,n,a),l.push({event:a,listeners:r}),a.data=y))}Mr(l,t)}))}function Vr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Qr(e,t){for(var n=t+"Capture",r=[];null!==e;){var a=e,o=a.stateNode;5===a.tag&&null!==o&&(a=o,null!=(o=Te(e,n))&&r.unshift(Vr(e,o,a)),null!=(o=Te(e,t))&&r.push(Vr(e,o,a))),e=e.return}return r}function Kr(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function qr(e,t,n,r,a){for(var o=t._reactName,l=[];null!==n&&n!==r;){var i=n,s=i.alternate,u=i.stateNode;if(null!==s&&s===r)break;5===i.tag&&null!==u&&(i=u,a?null!=(s=Te(n,o))&&l.unshift(Vr(n,s,i)):a||null!=(s=Te(n,o))&&l.push(Vr(n,s,i))),n=n.return}0!==l.length&&e.push({event:t,listeners:l})}var Yr=/\r\n?/g,Jr=/\u0000|\uFFFD/g;function Xr(e){return("string"===typeof e?e:""+e).replace(Yr,"\n").replace(Jr,"")}function Gr(e,t,n){if(t=Xr(t),Xr(e)!==t&&n)throw Error(o(425))}function Zr(){}var ea=null,ta=null;function na(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var ra="function"===typeof setTimeout?setTimeout:void 0,aa="function"===typeof clearTimeout?clearTimeout:void 0,oa="function"===typeof Promise?Promise:void 0,la="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof oa?function(e){return oa.resolve(null).then(e).catch(ia)}:ra;function ia(e){setTimeout((function(){throw e}))}function sa(e,t){var n=t,r=0;do{var a=n.nextSibling;if(e.removeChild(n),a&&8===a.nodeType)if("/$"===(n=a.data)){if(0===r)return e.removeChild(a),void $t(t);r--}else"$"!==n&&"$?"!==n&&"$!"!==n||r++;n=a}while(n);$t(t)}function ua(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function ca(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var da=Math.random().toString(36).slice(2),fa="__reactFiber$"+da,pa="__reactProps$"+da,ha="__reactContainer$"+da,ma="__reactEvents$"+da,va="__reactListeners$"+da,ga="__reactHandles$"+da;function ya(e){var t=e[fa];if(t)return t;for(var n=e.parentNode;n;){if(t=n[ha]||n[fa]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=ca(e);null!==e;){if(n=e[fa])return n;e=ca(e)}return t}n=(e=n).parentNode}return null}function ba(e){return!(e=e[fa]||e[ha])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function wa(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(o(33))}function xa(e){return e[pa]||null}var ka=[],Sa=-1;function Ea(e){return{current:e}}function Ca(e){0>Sa||(e.current=ka[Sa],ka[Sa]=null,Sa--)}function _a(e,t){Sa++,ka[Sa]=e.current,e.current=t}var ja={},Pa=Ea(ja),Da=Ea(!1),Na=ja;function Ra(e,t){var n=e.type.contextTypes;if(!n)return ja;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var a,o={};for(a in n)o[a]=t[a];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function Ta(e){return null!==(e=e.childContextTypes)&&void 0!==e}function La(){Ca(Da),Ca(Pa)}function Ia(e,t,n){if(Pa.current!==ja)throw Error(o(168));_a(Pa,t),_a(Da,n)}function Oa(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,"function"!==typeof r.getChildContext)return n;for(var a in r=r.getChildContext())if(!(a in t))throw Error(o(108,W(e)||"Unknown",a));return z({},n,r)}function Aa(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||ja,Na=Pa.current,_a(Pa,e),_a(Da,Da.current),!0}function za(e,t,n){var r=e.stateNode;if(!r)throw Error(o(169));n?(e=Oa(e,t,Na),r.__reactInternalMemoizedMergedChildContext=e,Ca(Da),Ca(Pa),_a(Pa,e)):Ca(Da),_a(Da,n)}var Ma=null,Fa=!1,Ua=!1;function Ba(e){null===Ma?Ma=[e]:Ma.push(e)}function $a(){if(!Ua&&null!==Ma){Ua=!0;var e=0,t=bt;try{var n=Ma;for(bt=1;e<n.length;e++){var r=n[e];do{r=r(!0)}while(null!==r)}Ma=null,Fa=!1}catch(a){throw null!==Ma&&(Ma=Ma.slice(e+1)),Ke(Ze,$a),a}finally{bt=t,Ua=!1}}return null}var Wa=[],Ha=0,Va=null,Qa=0,Ka=[],qa=0,Ya=null,Ja=1,Xa="";function Ga(e,t){Wa[Ha++]=Qa,Wa[Ha++]=Va,Va=e,Qa=t}function Za(e,t,n){Ka[qa++]=Ja,Ka[qa++]=Xa,Ka[qa++]=Ya,Ya=e;var r=Ja;e=Xa;var a=32-lt(r)-1;r&=~(1<<a),n+=1;var o=32-lt(t)+a;if(30<o){var l=a-a%5;o=(r&(1<<l)-1).toString(32),r>>=l,a-=l,Ja=1<<32-lt(t)+a|n<<a|r,Xa=o+e}else Ja=1<<o|n<<a|r,Xa=e}function eo(e){null!==e.return&&(Ga(e,1),Za(e,1,0))}function to(e){for(;e===Va;)Va=Wa[--Ha],Wa[Ha]=null,Qa=Wa[--Ha],Wa[Ha]=null;for(;e===Ya;)Ya=Ka[--qa],Ka[qa]=null,Xa=Ka[--qa],Ka[qa]=null,Ja=Ka[--qa],Ka[qa]=null}var no=null,ro=null,ao=!1,oo=null;function lo(e,t){var n=Ru(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function io(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,no=e,ro=ua(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,no=e,ro=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(n=null!==Ya?{id:Ja,overflow:Xa}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},(n=Ru(18,null,null,0)).stateNode=t,n.return=e,e.child=n,no=e,ro=null,!0);default:return!1}}function so(e){return 0!==(1&e.mode)&&0===(128&e.flags)}function uo(e){if(ao){var t=ro;if(t){var n=t;if(!io(e,t)){if(so(e))throw Error(o(418));t=ua(n.nextSibling);var r=no;t&&io(e,t)?lo(r,n):(e.flags=-4097&e.flags|2,ao=!1,no=e)}}else{if(so(e))throw Error(o(418));e.flags=-4097&e.flags|2,ao=!1,no=e}}}function co(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;no=e}function fo(e){if(e!==no)return!1;if(!ao)return co(e),ao=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!na(e.type,e.memoizedProps)),t&&(t=ro)){if(so(e))throw po(),Error(o(418));for(;t;)lo(e,t),t=ua(t.nextSibling)}if(co(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(o(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){ro=ua(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}ro=null}}else ro=no?ua(e.stateNode.nextSibling):null;return!0}function po(){for(var e=ro;e;)e=ua(e.nextSibling)}function ho(){ro=no=null,ao=!1}function mo(e){null===oo?oo=[e]:oo.push(e)}var vo=w.ReactCurrentBatchConfig;function go(e,t,n){if(null!==(e=n.ref)&&"function"!==typeof e&&"object"!==typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(o(309));var r=n.stateNode}if(!r)throw Error(o(147,e));var a=r,l=""+e;return null!==t&&null!==t.ref&&"function"===typeof t.ref&&t.ref._stringRef===l?t.ref:(t=function(e){var t=a.refs;null===e?delete t[l]:t[l]=e},t._stringRef=l,t)}if("string"!==typeof e)throw Error(o(284));if(!n._owner)throw Error(o(290,e))}return e}function yo(e,t){throw e=Object.prototype.toString.call(t),Error(o(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function bo(e){return(0,e._init)(e._payload)}function wo(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function a(e,t){return(e=Lu(e,t)).index=0,e.sibling=null,e}function l(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=2,n):r:(t.flags|=2,n):(t.flags|=1048576,n)}function i(t){return e&&null===t.alternate&&(t.flags|=2),t}function s(e,t,n,r){return null===t||6!==t.tag?((t=zu(n,e.mode,r)).return=e,t):((t=a(t,n)).return=e,t)}function u(e,t,n,r){var o=n.type;return o===S?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===o||"object"===typeof o&&null!==o&&o.$$typeof===T&&bo(o)===t.type)?((r=a(t,n.props)).ref=go(e,t,n),r.return=e,r):((r=Iu(n.type,n.key,n.props,null,e.mode,r)).ref=go(e,t,n),r.return=e,r)}function c(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Mu(n,e.mode,r)).return=e,t):((t=a(t,n.children||[])).return=e,t)}function d(e,t,n,r,o){return null===t||7!==t.tag?((t=Ou(n,e.mode,r,o)).return=e,t):((t=a(t,n)).return=e,t)}function f(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t)return(t=zu(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case x:return(n=Iu(t.type,t.key,t.props,null,e.mode,n)).ref=go(e,null,t),n.return=e,n;case k:return(t=Mu(t,e.mode,n)).return=e,t;case T:return f(e,(0,t._init)(t._payload),n)}if(te(t)||O(t))return(t=Ou(t,e.mode,n,null)).return=e,t;yo(e,t)}return null}function p(e,t,n,r){var a=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n)return null!==a?null:s(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case x:return n.key===a?u(e,t,n,r):null;case k:return n.key===a?c(e,t,n,r):null;case T:return p(e,t,(a=n._init)(n._payload),r)}if(te(n)||O(n))return null!==a?null:d(e,t,n,r,null);yo(e,n)}return null}function h(e,t,n,r,a){if("string"===typeof r&&""!==r||"number"===typeof r)return s(t,e=e.get(n)||null,""+r,a);if("object"===typeof r&&null!==r){switch(r.$$typeof){case x:return u(t,e=e.get(null===r.key?n:r.key)||null,r,a);case k:return c(t,e=e.get(null===r.key?n:r.key)||null,r,a);case T:return h(e,t,n,(0,r._init)(r._payload),a)}if(te(r)||O(r))return d(t,e=e.get(n)||null,r,a,null);yo(t,r)}return null}function m(a,o,i,s){for(var u=null,c=null,d=o,m=o=0,v=null;null!==d&&m<i.length;m++){d.index>m?(v=d,d=null):v=d.sibling;var g=p(a,d,i[m],s);if(null===g){null===d&&(d=v);break}e&&d&&null===g.alternate&&t(a,d),o=l(g,o,m),null===c?u=g:c.sibling=g,c=g,d=v}if(m===i.length)return n(a,d),ao&&Ga(a,m),u;if(null===d){for(;m<i.length;m++)null!==(d=f(a,i[m],s))&&(o=l(d,o,m),null===c?u=d:c.sibling=d,c=d);return ao&&Ga(a,m),u}for(d=r(a,d);m<i.length;m++)null!==(v=h(d,a,m,i[m],s))&&(e&&null!==v.alternate&&d.delete(null===v.key?m:v.key),o=l(v,o,m),null===c?u=v:c.sibling=v,c=v);return e&&d.forEach((function(e){return t(a,e)})),ao&&Ga(a,m),u}function v(a,i,s,u){var c=O(s);if("function"!==typeof c)throw Error(o(150));if(null==(s=c.call(s)))throw Error(o(151));for(var d=c=null,m=i,v=i=0,g=null,y=s.next();null!==m&&!y.done;v++,y=s.next()){m.index>v?(g=m,m=null):g=m.sibling;var b=p(a,m,y.value,u);if(null===b){null===m&&(m=g);break}e&&m&&null===b.alternate&&t(a,m),i=l(b,i,v),null===d?c=b:d.sibling=b,d=b,m=g}if(y.done)return n(a,m),ao&&Ga(a,v),c;if(null===m){for(;!y.done;v++,y=s.next())null!==(y=f(a,y.value,u))&&(i=l(y,i,v),null===d?c=y:d.sibling=y,d=y);return ao&&Ga(a,v),c}for(m=r(a,m);!y.done;v++,y=s.next())null!==(y=h(m,a,v,y.value,u))&&(e&&null!==y.alternate&&m.delete(null===y.key?v:y.key),i=l(y,i,v),null===d?c=y:d.sibling=y,d=y);return e&&m.forEach((function(e){return t(a,e)})),ao&&Ga(a,v),c}return function e(r,o,l,s){if("object"===typeof l&&null!==l&&l.type===S&&null===l.key&&(l=l.props.children),"object"===typeof l&&null!==l){switch(l.$$typeof){case x:e:{for(var u=l.key,c=o;null!==c;){if(c.key===u){if((u=l.type)===S){if(7===c.tag){n(r,c.sibling),(o=a(c,l.props.children)).return=r,r=o;break e}}else if(c.elementType===u||"object"===typeof u&&null!==u&&u.$$typeof===T&&bo(u)===c.type){n(r,c.sibling),(o=a(c,l.props)).ref=go(r,c,l),o.return=r,r=o;break e}n(r,c);break}t(r,c),c=c.sibling}l.type===S?((o=Ou(l.props.children,r.mode,s,l.key)).return=r,r=o):((s=Iu(l.type,l.key,l.props,null,r.mode,s)).ref=go(r,o,l),s.return=r,r=s)}return i(r);case k:e:{for(c=l.key;null!==o;){if(o.key===c){if(4===o.tag&&o.stateNode.containerInfo===l.containerInfo&&o.stateNode.implementation===l.implementation){n(r,o.sibling),(o=a(o,l.children||[])).return=r,r=o;break e}n(r,o);break}t(r,o),o=o.sibling}(o=Mu(l,r.mode,s)).return=r,r=o}return i(r);case T:return e(r,o,(c=l._init)(l._payload),s)}if(te(l))return m(r,o,l,s);if(O(l))return v(r,o,l,s);yo(r,l)}return"string"===typeof l&&""!==l||"number"===typeof l?(l=""+l,null!==o&&6===o.tag?(n(r,o.sibling),(o=a(o,l)).return=r,r=o):(n(r,o),(o=zu(l,r.mode,s)).return=r,r=o),i(r)):n(r,o)}}var xo=wo(!0),ko=wo(!1),So=Ea(null),Eo=null,Co=null,_o=null;function jo(){_o=Co=Eo=null}function Po(e){var t=So.current;Ca(So),e._currentValue=t}function Do(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function No(e,t){Eo=e,_o=Co=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!==(e.lanes&t)&&(bi=!0),e.firstContext=null)}function Ro(e){var t=e._currentValue;if(_o!==e)if(e={context:e,memoizedValue:t,next:null},null===Co){if(null===Eo)throw Error(o(308));Co=e,Eo.dependencies={lanes:0,firstContext:e}}else Co=Co.next=e;return t}var To=null;function Lo(e){null===To?To=[e]:To.push(e)}function Io(e,t,n,r){var a=t.interleaved;return null===a?(n.next=n,Lo(t)):(n.next=a.next,a.next=n),t.interleaved=n,Oo(e,r)}function Oo(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}var Ao=!1;function zo(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Mo(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Fo(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Uo(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&Ps)){var a=r.pending;return null===a?t.next=t:(t.next=a.next,a.next=t),r.pending=t,Oo(e,n)}return null===(a=r.interleaved)?(t.next=t,Lo(r)):(t.next=a.next,a.next=t),r.interleaved=t,Oo(e,n)}function Bo(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194240&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,yt(e,n)}}function $o(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var a=null,o=null;if(null!==(n=n.firstBaseUpdate)){do{var l={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===o?a=o=l:o=o.next=l,n=n.next}while(null!==n);null===o?a=o=t:o=o.next=t}else a=o=t;return n={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:o,shared:r.shared,effects:r.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Wo(e,t,n,r){var a=e.updateQueue;Ao=!1;var o=a.firstBaseUpdate,l=a.lastBaseUpdate,i=a.shared.pending;if(null!==i){a.shared.pending=null;var s=i,u=s.next;s.next=null,null===l?o=u:l.next=u,l=s;var c=e.alternate;null!==c&&((i=(c=c.updateQueue).lastBaseUpdate)!==l&&(null===i?c.firstBaseUpdate=u:i.next=u,c.lastBaseUpdate=s))}if(null!==o){var d=a.baseState;for(l=0,c=u=s=null,i=o;;){var f=i.lane,p=i.eventTime;if((r&f)===f){null!==c&&(c=c.next={eventTime:p,lane:0,tag:i.tag,payload:i.payload,callback:i.callback,next:null});e:{var h=e,m=i;switch(f=t,p=n,m.tag){case 1:if("function"===typeof(h=m.payload)){d=h.call(p,d,f);break e}d=h;break e;case 3:h.flags=-65537&h.flags|128;case 0:if(null===(f="function"===typeof(h=m.payload)?h.call(p,d,f):h)||void 0===f)break e;d=z({},d,f);break e;case 2:Ao=!0}}null!==i.callback&&0!==i.lane&&(e.flags|=64,null===(f=a.effects)?a.effects=[i]:f.push(i))}else p={eventTime:p,lane:f,tag:i.tag,payload:i.payload,callback:i.callback,next:null},null===c?(u=c=p,s=d):c=c.next=p,l|=f;if(null===(i=i.next)){if(null===(i=a.shared.pending))break;i=(f=i).next,f.next=null,a.lastBaseUpdate=f,a.shared.pending=null}}if(null===c&&(s=d),a.baseState=s,a.firstBaseUpdate=u,a.lastBaseUpdate=c,null!==(t=a.shared.interleaved)){a=t;do{l|=a.lane,a=a.next}while(a!==t)}else null===o&&(a.shared.lanes=0);As|=l,e.lanes=l,e.memoizedState=d}}function Ho(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],a=r.callback;if(null!==a){if(r.callback=null,r=n,"function"!==typeof a)throw Error(o(191,a));a.call(r)}}}var Vo={},Qo=Ea(Vo),Ko=Ea(Vo),qo=Ea(Vo);function Yo(e){if(e===Vo)throw Error(o(174));return e}function Jo(e,t){switch(_a(qo,t),_a(Ko,e),_a(Qo,Vo),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:se(null,"");break;default:t=se(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}Ca(Qo),_a(Qo,t)}function Xo(){Ca(Qo),Ca(Ko),Ca(qo)}function Go(e){Yo(qo.current);var t=Yo(Qo.current),n=se(t,e.type);t!==n&&(_a(Ko,e),_a(Qo,n))}function Zo(e){Ko.current===e&&(Ca(Qo),Ca(Ko))}var el=Ea(0);function tl(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var nl=[];function rl(){for(var e=0;e<nl.length;e++)nl[e]._workInProgressVersionPrimary=null;nl.length=0}var al=w.ReactCurrentDispatcher,ol=w.ReactCurrentBatchConfig,ll=0,il=null,sl=null,ul=null,cl=!1,dl=!1,fl=0,pl=0;function hl(){throw Error(o(321))}function ml(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!ir(e[n],t[n]))return!1;return!0}function vl(e,t,n,r,a,l){if(ll=l,il=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,al.current=null===e||null===e.memoizedState?Zl:ei,e=n(r,a),dl){l=0;do{if(dl=!1,fl=0,25<=l)throw Error(o(301));l+=1,ul=sl=null,t.updateQueue=null,al.current=ti,e=n(r,a)}while(dl)}if(al.current=Gl,t=null!==sl&&null!==sl.next,ll=0,ul=sl=il=null,cl=!1,t)throw Error(o(300));return e}function gl(){var e=0!==fl;return fl=0,e}function yl(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===ul?il.memoizedState=ul=e:ul=ul.next=e,ul}function bl(){if(null===sl){var e=il.alternate;e=null!==e?e.memoizedState:null}else e=sl.next;var t=null===ul?il.memoizedState:ul.next;if(null!==t)ul=t,sl=e;else{if(null===e)throw Error(o(310));e={memoizedState:(sl=e).memoizedState,baseState:sl.baseState,baseQueue:sl.baseQueue,queue:sl.queue,next:null},null===ul?il.memoizedState=ul=e:ul=ul.next=e}return ul}function wl(e,t){return"function"===typeof t?t(e):t}function xl(e){var t=bl(),n=t.queue;if(null===n)throw Error(o(311));n.lastRenderedReducer=e;var r=sl,a=r.baseQueue,l=n.pending;if(null!==l){if(null!==a){var i=a.next;a.next=l.next,l.next=i}r.baseQueue=a=l,n.pending=null}if(null!==a){l=a.next,r=r.baseState;var s=i=null,u=null,c=l;do{var d=c.lane;if((ll&d)===d)null!==u&&(u=u.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var f={lane:d,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};null===u?(s=u=f,i=r):u=u.next=f,il.lanes|=d,As|=d}c=c.next}while(null!==c&&c!==l);null===u?i=r:u.next=s,ir(r,t.memoizedState)||(bi=!0),t.memoizedState=r,t.baseState=i,t.baseQueue=u,n.lastRenderedState=r}if(null!==(e=n.interleaved)){a=e;do{l=a.lane,il.lanes|=l,As|=l,a=a.next}while(a!==e)}else null===a&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function kl(e){var t=bl(),n=t.queue;if(null===n)throw Error(o(311));n.lastRenderedReducer=e;var r=n.dispatch,a=n.pending,l=t.memoizedState;if(null!==a){n.pending=null;var i=a=a.next;do{l=e(l,i.action),i=i.next}while(i!==a);ir(l,t.memoizedState)||(bi=!0),t.memoizedState=l,null===t.baseQueue&&(t.baseState=l),n.lastRenderedState=l}return[l,r]}function Sl(){}function El(e,t){var n=il,r=bl(),a=t(),l=!ir(r.memoizedState,a);if(l&&(r.memoizedState=a,bi=!0),r=r.queue,Al(jl.bind(null,n,r,e),[e]),r.getSnapshot!==t||l||null!==ul&&1&ul.memoizedState.tag){if(n.flags|=2048,Rl(9,_l.bind(null,n,r,a,t),void 0,null),null===Ds)throw Error(o(349));0!==(30&ll)||Cl(n,t,a)}return a}function Cl(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=il.updateQueue)?(t={lastEffect:null,stores:null},il.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function _l(e,t,n,r){t.value=n,t.getSnapshot=r,Pl(t)&&Dl(e)}function jl(e,t,n){return n((function(){Pl(t)&&Dl(e)}))}function Pl(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!ir(e,n)}catch(r){return!0}}function Dl(e){var t=Oo(e,1);null!==t&&nu(t,e,1,-1)}function Nl(e){var t=yl();return"function"===typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:wl,lastRenderedState:e},t.queue=e,e=e.dispatch=ql.bind(null,il,e),[t.memoizedState,e]}function Rl(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=il.updateQueue)?(t={lastEffect:null,stores:null},il.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function Tl(){return bl().memoizedState}function Ll(e,t,n,r){var a=yl();il.flags|=e,a.memoizedState=Rl(1|t,n,void 0,void 0===r?null:r)}function Il(e,t,n,r){var a=bl();r=void 0===r?null:r;var o=void 0;if(null!==sl){var l=sl.memoizedState;if(o=l.destroy,null!==r&&ml(r,l.deps))return void(a.memoizedState=Rl(t,n,o,r))}il.flags|=e,a.memoizedState=Rl(1|t,n,o,r)}function Ol(e,t){return Ll(8390656,8,e,t)}function Al(e,t){return Il(2048,8,e,t)}function zl(e,t){return Il(4,2,e,t)}function Ml(e,t){return Il(4,4,e,t)}function Fl(e,t){return"function"===typeof t?(e=e(),t(e),function(){t(null)}):null!==t&&void 0!==t?(e=e(),t.current=e,function(){t.current=null}):void 0}function Ul(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Il(4,4,Fl.bind(null,t,e),n)}function Bl(){}function $l(e,t){var n=bl();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&ml(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Wl(e,t){var n=bl();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&ml(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Hl(e,t,n){return 0===(21&ll)?(e.baseState&&(e.baseState=!1,bi=!0),e.memoizedState=n):(ir(n,t)||(n=mt(),il.lanes|=n,As|=n,e.baseState=!0),t)}function Vl(e,t){var n=bt;bt=0!==n&&4>n?n:4,e(!0);var r=ol.transition;ol.transition={};try{e(!1),t()}finally{bt=n,ol.transition=r}}function Ql(){return bl().memoizedState}function Kl(e,t,n){var r=tu(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Yl(e))Jl(t,n);else if(null!==(n=Io(e,t,n,r))){nu(n,e,r,eu()),Xl(n,t,r)}}function ql(e,t,n){var r=tu(e),a={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Yl(e))Jl(t,a);else{var o=e.alternate;if(0===e.lanes&&(null===o||0===o.lanes)&&null!==(o=t.lastRenderedReducer))try{var l=t.lastRenderedState,i=o(l,n);if(a.hasEagerState=!0,a.eagerState=i,ir(i,l)){var s=t.interleaved;return null===s?(a.next=a,Lo(t)):(a.next=s.next,s.next=a),void(t.interleaved=a)}}catch(u){}null!==(n=Io(e,t,a,r))&&(nu(n,e,r,a=eu()),Xl(n,t,r))}}function Yl(e){var t=e.alternate;return e===il||null!==t&&t===il}function Jl(e,t){dl=cl=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Xl(e,t,n){if(0!==(4194240&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,yt(e,n)}}var Gl={readContext:Ro,useCallback:hl,useContext:hl,useEffect:hl,useImperativeHandle:hl,useInsertionEffect:hl,useLayoutEffect:hl,useMemo:hl,useReducer:hl,useRef:hl,useState:hl,useDebugValue:hl,useDeferredValue:hl,useTransition:hl,useMutableSource:hl,useSyncExternalStore:hl,useId:hl,unstable_isNewReconciler:!1},Zl={readContext:Ro,useCallback:function(e,t){return yl().memoizedState=[e,void 0===t?null:t],e},useContext:Ro,useEffect:Ol,useImperativeHandle:function(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Ll(4194308,4,Fl.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Ll(4194308,4,e,t)},useInsertionEffect:function(e,t){return Ll(4,2,e,t)},useMemo:function(e,t){var n=yl();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=yl();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Kl.bind(null,il,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},yl().memoizedState=e},useState:Nl,useDebugValue:Bl,useDeferredValue:function(e){return yl().memoizedState=e},useTransition:function(){var e=Nl(!1),t=e[0];return e=Vl.bind(null,e[1]),yl().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=il,a=yl();if(ao){if(void 0===n)throw Error(o(407));n=n()}else{if(n=t(),null===Ds)throw Error(o(349));0!==(30&ll)||Cl(r,t,n)}a.memoizedState=n;var l={value:n,getSnapshot:t};return a.queue=l,Ol(jl.bind(null,r,l,e),[e]),r.flags|=2048,Rl(9,_l.bind(null,r,l,n,t),void 0,null),n},useId:function(){var e=yl(),t=Ds.identifierPrefix;if(ao){var n=Xa;t=":"+t+"R"+(n=(Ja&~(1<<32-lt(Ja)-1)).toString(32)+n),0<(n=fl++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=pl++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},ei={readContext:Ro,useCallback:$l,useContext:Ro,useEffect:Al,useImperativeHandle:Ul,useInsertionEffect:zl,useLayoutEffect:Ml,useMemo:Wl,useReducer:xl,useRef:Tl,useState:function(){return xl(wl)},useDebugValue:Bl,useDeferredValue:function(e){return Hl(bl(),sl.memoizedState,e)},useTransition:function(){return[xl(wl)[0],bl().memoizedState]},useMutableSource:Sl,useSyncExternalStore:El,useId:Ql,unstable_isNewReconciler:!1},ti={readContext:Ro,useCallback:$l,useContext:Ro,useEffect:Al,useImperativeHandle:Ul,useInsertionEffect:zl,useLayoutEffect:Ml,useMemo:Wl,useReducer:kl,useRef:Tl,useState:function(){return kl(wl)},useDebugValue:Bl,useDeferredValue:function(e){var t=bl();return null===sl?t.memoizedState=e:Hl(t,sl.memoizedState,e)},useTransition:function(){return[kl(wl)[0],bl().memoizedState]},useMutableSource:Sl,useSyncExternalStore:El,useId:Ql,unstable_isNewReconciler:!1};function ni(e,t){if(e&&e.defaultProps){for(var n in t=z({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}function ri(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:z({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var ai={isMounted:function(e){return!!(e=e._reactInternals)&&$e(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=eu(),a=tu(e),o=Fo(r,a);o.payload=t,void 0!==n&&null!==n&&(o.callback=n),null!==(t=Uo(e,o,a))&&(nu(t,e,a,r),Bo(t,e,a))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=eu(),a=tu(e),o=Fo(r,a);o.tag=1,o.payload=t,void 0!==n&&null!==n&&(o.callback=n),null!==(t=Uo(e,o,a))&&(nu(t,e,a,r),Bo(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=eu(),r=tu(e),a=Fo(n,r);a.tag=2,void 0!==t&&null!==t&&(a.callback=t),null!==(t=Uo(e,a,r))&&(nu(t,e,r,n),Bo(t,e,r))}};function oi(e,t,n,r,a,o,l){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,o,l):!t.prototype||!t.prototype.isPureReactComponent||(!sr(n,r)||!sr(a,o))}function li(e,t,n){var r=!1,a=ja,o=t.contextType;return"object"===typeof o&&null!==o?o=Ro(o):(a=Ta(t)?Na:Pa.current,o=(r=null!==(r=t.contextTypes)&&void 0!==r)?Ra(e,a):ja),t=new t(n,o),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=ai,e.stateNode=t,t._reactInternals=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=a,e.__reactInternalMemoizedMaskedChildContext=o),t}function ii(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&ai.enqueueReplaceState(t,t.state,null)}function si(e,t,n,r){var a=e.stateNode;a.props=n,a.state=e.memoizedState,a.refs={},zo(e);var o=t.contextType;"object"===typeof o&&null!==o?a.context=Ro(o):(o=Ta(t)?Na:Pa.current,a.context=Ra(e,o)),a.state=e.memoizedState,"function"===typeof(o=t.getDerivedStateFromProps)&&(ri(e,t,o,n),a.state=e.memoizedState),"function"===typeof t.getDerivedStateFromProps||"function"===typeof a.getSnapshotBeforeUpdate||"function"!==typeof a.UNSAFE_componentWillMount&&"function"!==typeof a.componentWillMount||(t=a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),t!==a.state&&ai.enqueueReplaceState(a,a.state,null),Wo(e,n,a,r),a.state=e.memoizedState),"function"===typeof a.componentDidMount&&(e.flags|=4194308)}function ui(e,t){try{var n="",r=t;do{n+=B(r),r=r.return}while(r);var a=n}catch(o){a="\nError generating stack: "+o.message+"\n"+o.stack}return{value:e,source:t,stack:a,digest:null}}function ci(e,t,n){return{value:e,source:null,stack:null!=n?n:null,digest:null!=t?t:null}}function di(e,t){try{console.error(t.value)}catch(n){setTimeout((function(){throw n}))}}var fi="function"===typeof WeakMap?WeakMap:Map;function pi(e,t,n){(n=Fo(-1,n)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Hs||(Hs=!0,Vs=r),di(0,t)},n}function hi(e,t,n){(n=Fo(-1,n)).tag=3;var r=e.type.getDerivedStateFromError;if("function"===typeof r){var a=t.value;n.payload=function(){return r(a)},n.callback=function(){di(0,t)}}var o=e.stateNode;return null!==o&&"function"===typeof o.componentDidCatch&&(n.callback=function(){di(0,t),"function"!==typeof r&&(null===Qs?Qs=new Set([this]):Qs.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}function mi(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new fi;var a=new Set;r.set(t,a)}else void 0===(a=r.get(t))&&(a=new Set,r.set(t,a));a.has(n)||(a.add(n),e=Cu.bind(null,e,t,n),t.then(e,e))}function vi(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function gi(e,t,n,r,a){return 0===(1&e.mode)?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=Fo(-1,1)).tag=2,Uo(n,t,1))),n.lanes|=1),e):(e.flags|=65536,e.lanes=a,e)}var yi=w.ReactCurrentOwner,bi=!1;function wi(e,t,n,r){t.child=null===e?ko(t,null,n,r):xo(t,e.child,n,r)}function xi(e,t,n,r,a){n=n.render;var o=t.ref;return No(t,a),r=vl(e,t,n,r,o,a),n=gl(),null===e||bi?(ao&&n&&eo(t),t.flags|=1,wi(e,t,r,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,Hi(e,t,a))}function ki(e,t,n,r,a){if(null===e){var o=n.type;return"function"!==typeof o||Tu(o)||void 0!==o.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=Iu(n.type,null,r,t,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=o,Si(e,t,o,r,a))}if(o=e.child,0===(e.lanes&a)){var l=o.memoizedProps;if((n=null!==(n=n.compare)?n:sr)(l,r)&&e.ref===t.ref)return Hi(e,t,a)}return t.flags|=1,(e=Lu(o,r)).ref=t.ref,e.return=t,t.child=e}function Si(e,t,n,r,a){if(null!==e){var o=e.memoizedProps;if(sr(o,r)&&e.ref===t.ref){if(bi=!1,t.pendingProps=r=o,0===(e.lanes&a))return t.lanes=e.lanes,Hi(e,t,a);0!==(131072&e.flags)&&(bi=!0)}}return _i(e,t,n,r,a)}function Ei(e,t,n){var r=t.pendingProps,a=r.children,o=null!==e?e.memoizedState:null;if("hidden"===r.mode)if(0===(1&t.mode))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},_a(Ls,Ts),Ts|=n;else{if(0===(1073741824&n))return e=null!==o?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,_a(Ls,Ts),Ts|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=null!==o?o.baseLanes:n,_a(Ls,Ts),Ts|=r}else null!==o?(r=o.baseLanes|n,t.memoizedState=null):r=n,_a(Ls,Ts),Ts|=r;return wi(e,t,a,n),t.child}function Ci(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function _i(e,t,n,r,a){var o=Ta(n)?Na:Pa.current;return o=Ra(t,o),No(t,a),n=vl(e,t,n,r,o,a),r=gl(),null===e||bi?(ao&&r&&eo(t),t.flags|=1,wi(e,t,n,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,Hi(e,t,a))}function ji(e,t,n,r,a){if(Ta(n)){var o=!0;Aa(t)}else o=!1;if(No(t,a),null===t.stateNode)Wi(e,t),li(t,n,r),si(t,n,r,a),r=!0;else if(null===e){var l=t.stateNode,i=t.memoizedProps;l.props=i;var s=l.context,u=n.contextType;"object"===typeof u&&null!==u?u=Ro(u):u=Ra(t,u=Ta(n)?Na:Pa.current);var c=n.getDerivedStateFromProps,d="function"===typeof c||"function"===typeof l.getSnapshotBeforeUpdate;d||"function"!==typeof l.UNSAFE_componentWillReceiveProps&&"function"!==typeof l.componentWillReceiveProps||(i!==r||s!==u)&&ii(t,l,r,u),Ao=!1;var f=t.memoizedState;l.state=f,Wo(t,r,l,a),s=t.memoizedState,i!==r||f!==s||Da.current||Ao?("function"===typeof c&&(ri(t,n,c,r),s=t.memoizedState),(i=Ao||oi(t,n,i,r,f,s,u))?(d||"function"!==typeof l.UNSAFE_componentWillMount&&"function"!==typeof l.componentWillMount||("function"===typeof l.componentWillMount&&l.componentWillMount(),"function"===typeof l.UNSAFE_componentWillMount&&l.UNSAFE_componentWillMount()),"function"===typeof l.componentDidMount&&(t.flags|=4194308)):("function"===typeof l.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=s),l.props=r,l.state=s,l.context=u,r=i):("function"===typeof l.componentDidMount&&(t.flags|=4194308),r=!1)}else{l=t.stateNode,Mo(e,t),i=t.memoizedProps,u=t.type===t.elementType?i:ni(t.type,i),l.props=u,d=t.pendingProps,f=l.context,"object"===typeof(s=n.contextType)&&null!==s?s=Ro(s):s=Ra(t,s=Ta(n)?Na:Pa.current);var p=n.getDerivedStateFromProps;(c="function"===typeof p||"function"===typeof l.getSnapshotBeforeUpdate)||"function"!==typeof l.UNSAFE_componentWillReceiveProps&&"function"!==typeof l.componentWillReceiveProps||(i!==d||f!==s)&&ii(t,l,r,s),Ao=!1,f=t.memoizedState,l.state=f,Wo(t,r,l,a);var h=t.memoizedState;i!==d||f!==h||Da.current||Ao?("function"===typeof p&&(ri(t,n,p,r),h=t.memoizedState),(u=Ao||oi(t,n,u,r,f,h,s)||!1)?(c||"function"!==typeof l.UNSAFE_componentWillUpdate&&"function"!==typeof l.componentWillUpdate||("function"===typeof l.componentWillUpdate&&l.componentWillUpdate(r,h,s),"function"===typeof l.UNSAFE_componentWillUpdate&&l.UNSAFE_componentWillUpdate(r,h,s)),"function"===typeof l.componentDidUpdate&&(t.flags|=4),"function"===typeof l.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof l.componentDidUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof l.getSnapshotBeforeUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=h),l.props=r,l.state=h,l.context=s,r=u):("function"!==typeof l.componentDidUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof l.getSnapshotBeforeUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return Pi(e,t,n,r,o,a)}function Pi(e,t,n,r,a,o){Ci(e,t);var l=0!==(128&t.flags);if(!r&&!l)return a&&za(t,n,!1),Hi(e,t,o);r=t.stateNode,yi.current=t;var i=l&&"function"!==typeof n.getDerivedStateFromError?null:r.render();return t.flags|=1,null!==e&&l?(t.child=xo(t,e.child,null,o),t.child=xo(t,null,i,o)):wi(e,t,i,o),t.memoizedState=r.state,a&&za(t,n,!0),t.child}function Di(e){var t=e.stateNode;t.pendingContext?Ia(0,t.pendingContext,t.pendingContext!==t.context):t.context&&Ia(0,t.context,!1),Jo(e,t.containerInfo)}function Ni(e,t,n,r,a){return ho(),mo(a),t.flags|=256,wi(e,t,n,r),t.child}var Ri,Ti,Li,Ii,Oi={dehydrated:null,treeContext:null,retryLane:0};function Ai(e){return{baseLanes:e,cachePool:null,transitions:null}}function zi(e,t,n){var r,a=t.pendingProps,l=el.current,i=!1,s=0!==(128&t.flags);if((r=s)||(r=(null===e||null!==e.memoizedState)&&0!==(2&l)),r?(i=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(l|=1),_a(el,1&l),null===e)return uo(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(0===(1&t.mode)?t.lanes=1:"$!"===e.data?t.lanes=8:t.lanes=1073741824,null):(s=a.children,e=a.fallback,i?(a=t.mode,i=t.child,s={mode:"hidden",children:s},0===(1&a)&&null!==i?(i.childLanes=0,i.pendingProps=s):i=Au(s,a,0,null),e=Ou(e,a,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=Ai(n),t.memoizedState=Oi,e):Mi(t,s));if(null!==(l=e.memoizedState)&&null!==(r=l.dehydrated))return function(e,t,n,r,a,l,i){if(n)return 256&t.flags?(t.flags&=-257,Fi(e,t,i,r=ci(Error(o(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(l=r.fallback,a=t.mode,r=Au({mode:"visible",children:r.children},a,0,null),(l=Ou(l,a,i,null)).flags|=2,r.return=t,l.return=t,r.sibling=l,t.child=r,0!==(1&t.mode)&&xo(t,e.child,null,i),t.child.memoizedState=Ai(i),t.memoizedState=Oi,l);if(0===(1&t.mode))return Fi(e,t,i,null);if("$!"===a.data){if(r=a.nextSibling&&a.nextSibling.dataset)var s=r.dgst;return r=s,Fi(e,t,i,r=ci(l=Error(o(419)),r,void 0))}if(s=0!==(i&e.childLanes),bi||s){if(null!==(r=Ds)){switch(i&-i){case 4:a=2;break;case 16:a=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:a=32;break;case 536870912:a=268435456;break;default:a=0}0!==(a=0!==(a&(r.suspendedLanes|i))?0:a)&&a!==l.retryLane&&(l.retryLane=a,Oo(e,a),nu(r,e,a,-1))}return mu(),Fi(e,t,i,r=ci(Error(o(421))))}return"$?"===a.data?(t.flags|=128,t.child=e.child,t=ju.bind(null,e),a._reactRetry=t,null):(e=l.treeContext,ro=ua(a.nextSibling),no=t,ao=!0,oo=null,null!==e&&(Ka[qa++]=Ja,Ka[qa++]=Xa,Ka[qa++]=Ya,Ja=e.id,Xa=e.overflow,Ya=t),t=Mi(t,r.children),t.flags|=4096,t)}(e,t,s,a,r,l,n);if(i){i=a.fallback,s=t.mode,r=(l=e.child).sibling;var u={mode:"hidden",children:a.children};return 0===(1&s)&&t.child!==l?((a=t.child).childLanes=0,a.pendingProps=u,t.deletions=null):(a=Lu(l,u)).subtreeFlags=14680064&l.subtreeFlags,null!==r?i=Lu(r,i):(i=Ou(i,s,n,null)).flags|=2,i.return=t,a.return=t,a.sibling=i,t.child=a,a=i,i=t.child,s=null===(s=e.child.memoizedState)?Ai(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},i.memoizedState=s,i.childLanes=e.childLanes&~n,t.memoizedState=Oi,a}return e=(i=e.child).sibling,a=Lu(i,{mode:"visible",children:a.children}),0===(1&t.mode)&&(a.lanes=n),a.return=t,a.sibling=null,null!==e&&(null===(n=t.deletions)?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=a,t.memoizedState=null,a}function Mi(e,t){return(t=Au({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function Fi(e,t,n,r){return null!==r&&mo(r),xo(t,e.child,null,n),(e=Mi(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Ui(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),Do(e.return,t,n)}function Bi(e,t,n,r,a){var o=e.memoizedState;null===o?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:a}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=a)}function $i(e,t,n){var r=t.pendingProps,a=r.revealOrder,o=r.tail;if(wi(e,t,r.children,n),0!==(2&(r=el.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Ui(e,n,t);else if(19===e.tag)Ui(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(_a(el,r),0===(1&t.mode))t.memoizedState=null;else switch(a){case"forwards":for(n=t.child,a=null;null!==n;)null!==(e=n.alternate)&&null===tl(e)&&(a=n),n=n.sibling;null===(n=a)?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),Bi(t,!1,a,n,o);break;case"backwards":for(n=null,a=t.child,t.child=null;null!==a;){if(null!==(e=a.alternate)&&null===tl(e)){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}Bi(t,!0,n,null,o);break;case"together":Bi(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Wi(e,t){0===(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Hi(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),As|=t.lanes,0===(n&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(o(153));if(null!==t.child){for(n=Lu(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Lu(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Vi(e,t){if(!ao)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Qi(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=14680064&a.subtreeFlags,r|=14680064&a.flags,a.return=e,a=a.sibling;else for(a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Ki(e,t,n){var r=t.pendingProps;switch(to(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Qi(t),null;case 1:case 17:return Ta(t.type)&&La(),Qi(t),null;case 3:return r=t.stateNode,Xo(),Ca(Da),Ca(Pa),rl(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),null!==e&&null!==e.child||(fo(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,null!==oo&&(lu(oo),oo=null))),Ti(e,t),Qi(t),null;case 5:Zo(t);var a=Yo(qo.current);if(n=t.type,null!==e&&null!=t.stateNode)Li(e,t,n,r,a),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(null===t.stateNode)throw Error(o(166));return Qi(t),null}if(e=Yo(Qo.current),fo(t)){r=t.stateNode,n=t.type;var l=t.memoizedProps;switch(r[fa]=t,r[pa]=l,e=0!==(1&t.mode),n){case"dialog":Fr("cancel",r),Fr("close",r);break;case"iframe":case"object":case"embed":Fr("load",r);break;case"video":case"audio":for(a=0;a<Or.length;a++)Fr(Or[a],r);break;case"source":Fr("error",r);break;case"img":case"image":case"link":Fr("error",r),Fr("load",r);break;case"details":Fr("toggle",r);break;case"input":J(r,l),Fr("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!l.multiple},Fr("invalid",r);break;case"textarea":ae(r,l),Fr("invalid",r)}for(var s in ye(n,l),a=null,l)if(l.hasOwnProperty(s)){var u=l[s];"children"===s?"string"===typeof u?r.textContent!==u&&(!0!==l.suppressHydrationWarning&&Gr(r.textContent,u,e),a=["children",u]):"number"===typeof u&&r.textContent!==""+u&&(!0!==l.suppressHydrationWarning&&Gr(r.textContent,u,e),a=["children",""+u]):i.hasOwnProperty(s)&&null!=u&&"onScroll"===s&&Fr("scroll",r)}switch(n){case"input":Q(r),Z(r,l,!0);break;case"textarea":Q(r),le(r);break;case"select":case"option":break;default:"function"===typeof l.onClick&&(r.onclick=Zr)}r=a,t.updateQueue=r,null!==r&&(t.flags|=4)}else{s=9===a.nodeType?a:a.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=ie(n)),"http://www.w3.org/1999/xhtml"===e?"script"===n?((e=s.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"===typeof r.is?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),"select"===n&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[fa]=t,e[pa]=r,Ri(e,t,!1,!1),t.stateNode=e;e:{switch(s=be(n,r),n){case"dialog":Fr("cancel",e),Fr("close",e),a=r;break;case"iframe":case"object":case"embed":Fr("load",e),a=r;break;case"video":case"audio":for(a=0;a<Or.length;a++)Fr(Or[a],e);a=r;break;case"source":Fr("error",e),a=r;break;case"img":case"image":case"link":Fr("error",e),Fr("load",e),a=r;break;case"details":Fr("toggle",e),a=r;break;case"input":J(e,r),a=Y(e,r),Fr("invalid",e);break;case"option":default:a=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},a=z({},r,{value:void 0}),Fr("invalid",e);break;case"textarea":ae(e,r),a=re(e,r),Fr("invalid",e)}for(l in ye(n,a),u=a)if(u.hasOwnProperty(l)){var c=u[l];"style"===l?ve(e,c):"dangerouslySetInnerHTML"===l?null!=(c=c?c.__html:void 0)&&de(e,c):"children"===l?"string"===typeof c?("textarea"!==n||""!==c)&&fe(e,c):"number"===typeof c&&fe(e,""+c):"suppressContentEditableWarning"!==l&&"suppressHydrationWarning"!==l&&"autoFocus"!==l&&(i.hasOwnProperty(l)?null!=c&&"onScroll"===l&&Fr("scroll",e):null!=c&&b(e,l,c,s))}switch(n){case"input":Q(e),Z(e,r,!1);break;case"textarea":Q(e),le(e);break;case"option":null!=r.value&&e.setAttribute("value",""+H(r.value));break;case"select":e.multiple=!!r.multiple,null!=(l=r.value)?ne(e,!!r.multiple,l,!1):null!=r.defaultValue&&ne(e,!!r.multiple,r.defaultValue,!0);break;default:"function"===typeof a.onClick&&(e.onclick=Zr)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return Qi(t),null;case 6:if(e&&null!=t.stateNode)Ii(e,t,e.memoizedProps,r);else{if("string"!==typeof r&&null===t.stateNode)throw Error(o(166));if(n=Yo(qo.current),Yo(Qo.current),fo(t)){if(r=t.stateNode,n=t.memoizedProps,r[fa]=t,(l=r.nodeValue!==n)&&null!==(e=no))switch(e.tag){case 3:Gr(r.nodeValue,n,0!==(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&Gr(r.nodeValue,n,0!==(1&e.mode))}l&&(t.flags|=4)}else(r=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[fa]=t,t.stateNode=r}return Qi(t),null;case 13:if(Ca(el),r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(ao&&null!==ro&&0!==(1&t.mode)&&0===(128&t.flags))po(),ho(),t.flags|=98560,l=!1;else if(l=fo(t),null!==r&&null!==r.dehydrated){if(null===e){if(!l)throw Error(o(318));if(!(l=null!==(l=t.memoizedState)?l.dehydrated:null))throw Error(o(317));l[fa]=t}else ho(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;Qi(t),l=!1}else null!==oo&&(lu(oo),oo=null),l=!0;if(!l)return 65536&t.flags?t:null}return 0!==(128&t.flags)?(t.lanes=n,t):((r=null!==r)!==(null!==e&&null!==e.memoizedState)&&r&&(t.child.flags|=8192,0!==(1&t.mode)&&(null===e||0!==(1&el.current)?0===Is&&(Is=3):mu())),null!==t.updateQueue&&(t.flags|=4),Qi(t),null);case 4:return Xo(),Ti(e,t),null===e&&$r(t.stateNode.containerInfo),Qi(t),null;case 10:return Po(t.type._context),Qi(t),null;case 19:if(Ca(el),null===(l=t.memoizedState))return Qi(t),null;if(r=0!==(128&t.flags),null===(s=l.rendering))if(r)Vi(l,!1);else{if(0!==Is||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(s=tl(e))){for(t.flags|=128,Vi(l,!1),null!==(r=s.updateQueue)&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;null!==n;)e=r,(l=n).flags&=14680066,null===(s=l.alternate)?(l.childLanes=0,l.lanes=e,l.child=null,l.subtreeFlags=0,l.memoizedProps=null,l.memoizedState=null,l.updateQueue=null,l.dependencies=null,l.stateNode=null):(l.childLanes=s.childLanes,l.lanes=s.lanes,l.child=s.child,l.subtreeFlags=0,l.deletions=null,l.memoizedProps=s.memoizedProps,l.memoizedState=s.memoizedState,l.updateQueue=s.updateQueue,l.type=s.type,e=s.dependencies,l.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return _a(el,1&el.current|2),t.child}e=e.sibling}null!==l.tail&&Xe()>$s&&(t.flags|=128,r=!0,Vi(l,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=tl(s))){if(t.flags|=128,r=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.flags|=4),Vi(l,!0),null===l.tail&&"hidden"===l.tailMode&&!s.alternate&&!ao)return Qi(t),null}else 2*Xe()-l.renderingStartTime>$s&&1073741824!==n&&(t.flags|=128,r=!0,Vi(l,!1),t.lanes=4194304);l.isBackwards?(s.sibling=t.child,t.child=s):(null!==(n=l.last)?n.sibling=s:t.child=s,l.last=s)}return null!==l.tail?(t=l.tail,l.rendering=t,l.tail=t.sibling,l.renderingStartTime=Xe(),t.sibling=null,n=el.current,_a(el,r?1&n|2:1&n),t):(Qi(t),null);case 22:case 23:return du(),r=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==r&&(t.flags|=8192),r&&0!==(1&t.mode)?0!==(1073741824&Ts)&&(Qi(t),6&t.subtreeFlags&&(t.flags|=8192)):Qi(t),null;case 24:case 25:return null}throw Error(o(156,t.tag))}function qi(e,t){switch(to(t),t.tag){case 1:return Ta(t.type)&&La(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return Xo(),Ca(Da),Ca(Pa),rl(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 5:return Zo(t),null;case 13:if(Ca(el),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(o(340));ho()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return Ca(el),null;case 4:return Xo(),null;case 10:return Po(t.type._context),null;case 22:case 23:return du(),null;default:return null}}Ri=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Ti=function(){},Li=function(e,t,n,r){var a=e.memoizedProps;if(a!==r){e=t.stateNode,Yo(Qo.current);var o,l=null;switch(n){case"input":a=Y(e,a),r=Y(e,r),l=[];break;case"select":a=z({},a,{value:void 0}),r=z({},r,{value:void 0}),l=[];break;case"textarea":a=re(e,a),r=re(e,r),l=[];break;default:"function"!==typeof a.onClick&&"function"===typeof r.onClick&&(e.onclick=Zr)}for(c in ye(n,r),n=null,a)if(!r.hasOwnProperty(c)&&a.hasOwnProperty(c)&&null!=a[c])if("style"===c){var s=a[c];for(o in s)s.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else"dangerouslySetInnerHTML"!==c&&"children"!==c&&"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&"autoFocus"!==c&&(i.hasOwnProperty(c)?l||(l=[]):(l=l||[]).push(c,null));for(c in r){var u=r[c];if(s=null!=a?a[c]:void 0,r.hasOwnProperty(c)&&u!==s&&(null!=u||null!=s))if("style"===c)if(s){for(o in s)!s.hasOwnProperty(o)||u&&u.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in u)u.hasOwnProperty(o)&&s[o]!==u[o]&&(n||(n={}),n[o]=u[o])}else n||(l||(l=[]),l.push(c,n)),n=u;else"dangerouslySetInnerHTML"===c?(u=u?u.__html:void 0,s=s?s.__html:void 0,null!=u&&s!==u&&(l=l||[]).push(c,u)):"children"===c?"string"!==typeof u&&"number"!==typeof u||(l=l||[]).push(c,""+u):"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&(i.hasOwnProperty(c)?(null!=u&&"onScroll"===c&&Fr("scroll",e),l||s===u||(l=[])):(l=l||[]).push(c,u))}n&&(l=l||[]).push("style",n);var c=l;(t.updateQueue=c)&&(t.flags|=4)}},Ii=function(e,t,n,r){n!==r&&(t.flags|=4)};var Yi=!1,Ji=!1,Xi="function"===typeof WeakSet?WeakSet:Set,Gi=null;function Zi(e,t){var n=e.ref;if(null!==n)if("function"===typeof n)try{n(null)}catch(r){Eu(e,t,r)}else n.current=null}function es(e,t,n){try{n()}catch(r){Eu(e,t,r)}}var ts=!1;function ns(e,t,n){var r=t.updateQueue;if(null!==(r=null!==r?r.lastEffect:null)){var a=r=r.next;do{if((a.tag&e)===e){var o=a.destroy;a.destroy=void 0,void 0!==o&&es(t,n,o)}a=a.next}while(a!==r)}}function rs(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function as(e){var t=e.ref;if(null!==t){var n=e.stateNode;e.tag,e=n,"function"===typeof t?t(e):t.current=e}}function os(e){var t=e.alternate;null!==t&&(e.alternate=null,os(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&(delete t[fa],delete t[pa],delete t[ma],delete t[va],delete t[ga])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function ls(e){return 5===e.tag||3===e.tag||4===e.tag}function is(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||ls(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function ss(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=Zr));else if(4!==r&&null!==(e=e.child))for(ss(e,t,n),e=e.sibling;null!==e;)ss(e,t,n),e=e.sibling}function us(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&null!==(e=e.child))for(us(e,t,n),e=e.sibling;null!==e;)us(e,t,n),e=e.sibling}var cs=null,ds=!1;function fs(e,t,n){for(n=n.child;null!==n;)ps(e,t,n),n=n.sibling}function ps(e,t,n){if(ot&&"function"===typeof ot.onCommitFiberUnmount)try{ot.onCommitFiberUnmount(at,n)}catch(i){}switch(n.tag){case 5:Ji||Zi(n,t);case 6:var r=cs,a=ds;cs=null,fs(e,t,n),ds=a,null!==(cs=r)&&(ds?(e=cs,n=n.stateNode,8===e.nodeType?e.parentNode.removeChild(n):e.removeChild(n)):cs.removeChild(n.stateNode));break;case 18:null!==cs&&(ds?(e=cs,n=n.stateNode,8===e.nodeType?sa(e.parentNode,n):1===e.nodeType&&sa(e,n),$t(e)):sa(cs,n.stateNode));break;case 4:r=cs,a=ds,cs=n.stateNode.containerInfo,ds=!0,fs(e,t,n),cs=r,ds=a;break;case 0:case 11:case 14:case 15:if(!Ji&&(null!==(r=n.updateQueue)&&null!==(r=r.lastEffect))){a=r=r.next;do{var o=a,l=o.destroy;o=o.tag,void 0!==l&&(0!==(2&o)||0!==(4&o))&&es(n,t,l),a=a.next}while(a!==r)}fs(e,t,n);break;case 1:if(!Ji&&(Zi(n,t),"function"===typeof(r=n.stateNode).componentWillUnmount))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(i){Eu(n,t,i)}fs(e,t,n);break;case 21:fs(e,t,n);break;case 22:1&n.mode?(Ji=(r=Ji)||null!==n.memoizedState,fs(e,t,n),Ji=r):fs(e,t,n);break;default:fs(e,t,n)}}function hs(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new Xi),t.forEach((function(t){var r=Pu.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))}))}}function ms(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var a=n[r];try{var l=e,i=t,s=i;e:for(;null!==s;){switch(s.tag){case 5:cs=s.stateNode,ds=!1;break e;case 3:case 4:cs=s.stateNode.containerInfo,ds=!0;break e}s=s.return}if(null===cs)throw Error(o(160));ps(l,i,a),cs=null,ds=!1;var u=a.alternate;null!==u&&(u.return=null),a.return=null}catch(c){Eu(a,t,c)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)vs(t,e),t=t.sibling}function vs(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(ms(t,e),gs(e),4&r){try{ns(3,e,e.return),rs(3,e)}catch(v){Eu(e,e.return,v)}try{ns(5,e,e.return)}catch(v){Eu(e,e.return,v)}}break;case 1:ms(t,e),gs(e),512&r&&null!==n&&Zi(n,n.return);break;case 5:if(ms(t,e),gs(e),512&r&&null!==n&&Zi(n,n.return),32&e.flags){var a=e.stateNode;try{fe(a,"")}catch(v){Eu(e,e.return,v)}}if(4&r&&null!=(a=e.stateNode)){var l=e.memoizedProps,i=null!==n?n.memoizedProps:l,s=e.type,u=e.updateQueue;if(e.updateQueue=null,null!==u)try{"input"===s&&"radio"===l.type&&null!=l.name&&X(a,l),be(s,i);var c=be(s,l);for(i=0;i<u.length;i+=2){var d=u[i],f=u[i+1];"style"===d?ve(a,f):"dangerouslySetInnerHTML"===d?de(a,f):"children"===d?fe(a,f):b(a,d,f,c)}switch(s){case"input":G(a,l);break;case"textarea":oe(a,l);break;case"select":var p=a._wrapperState.wasMultiple;a._wrapperState.wasMultiple=!!l.multiple;var h=l.value;null!=h?ne(a,!!l.multiple,h,!1):p!==!!l.multiple&&(null!=l.defaultValue?ne(a,!!l.multiple,l.defaultValue,!0):ne(a,!!l.multiple,l.multiple?[]:"",!1))}a[pa]=l}catch(v){Eu(e,e.return,v)}}break;case 6:if(ms(t,e),gs(e),4&r){if(null===e.stateNode)throw Error(o(162));a=e.stateNode,l=e.memoizedProps;try{a.nodeValue=l}catch(v){Eu(e,e.return,v)}}break;case 3:if(ms(t,e),gs(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{$t(t.containerInfo)}catch(v){Eu(e,e.return,v)}break;case 4:default:ms(t,e),gs(e);break;case 13:ms(t,e),gs(e),8192&(a=e.child).flags&&(l=null!==a.memoizedState,a.stateNode.isHidden=l,!l||null!==a.alternate&&null!==a.alternate.memoizedState||(Bs=Xe())),4&r&&hs(e);break;case 22:if(d=null!==n&&null!==n.memoizedState,1&e.mode?(Ji=(c=Ji)||d,ms(t,e),Ji=c):ms(t,e),gs(e),8192&r){if(c=null!==e.memoizedState,(e.stateNode.isHidden=c)&&!d&&0!==(1&e.mode))for(Gi=e,d=e.child;null!==d;){for(f=Gi=d;null!==Gi;){switch(h=(p=Gi).child,p.tag){case 0:case 11:case 14:case 15:ns(4,p,p.return);break;case 1:Zi(p,p.return);var m=p.stateNode;if("function"===typeof m.componentWillUnmount){r=p,n=p.return;try{t=r,m.props=t.memoizedProps,m.state=t.memoizedState,m.componentWillUnmount()}catch(v){Eu(r,n,v)}}break;case 5:Zi(p,p.return);break;case 22:if(null!==p.memoizedState){xs(f);continue}}null!==h?(h.return=p,Gi=h):xs(f)}d=d.sibling}e:for(d=null,f=e;;){if(5===f.tag){if(null===d){d=f;try{a=f.stateNode,c?"function"===typeof(l=a.style).setProperty?l.setProperty("display","none","important"):l.display="none":(s=f.stateNode,i=void 0!==(u=f.memoizedProps.style)&&null!==u&&u.hasOwnProperty("display")?u.display:null,s.style.display=me("display",i))}catch(v){Eu(e,e.return,v)}}}else if(6===f.tag){if(null===d)try{f.stateNode.nodeValue=c?"":f.memoizedProps}catch(v){Eu(e,e.return,v)}}else if((22!==f.tag&&23!==f.tag||null===f.memoizedState||f===e)&&null!==f.child){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;null===f.sibling;){if(null===f.return||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:ms(t,e),gs(e),4&r&&hs(e);case 21:}}function gs(e){var t=e.flags;if(2&t){try{e:{for(var n=e.return;null!==n;){if(ls(n)){var r=n;break e}n=n.return}throw Error(o(160))}switch(r.tag){case 5:var a=r.stateNode;32&r.flags&&(fe(a,""),r.flags&=-33),us(e,is(e),a);break;case 3:case 4:var l=r.stateNode.containerInfo;ss(e,is(e),l);break;default:throw Error(o(161))}}catch(i){Eu(e,e.return,i)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function ys(e,t,n){Gi=e,bs(e,t,n)}function bs(e,t,n){for(var r=0!==(1&e.mode);null!==Gi;){var a=Gi,o=a.child;if(22===a.tag&&r){var l=null!==a.memoizedState||Yi;if(!l){var i=a.alternate,s=null!==i&&null!==i.memoizedState||Ji;i=Yi;var u=Ji;if(Yi=l,(Ji=s)&&!u)for(Gi=a;null!==Gi;)s=(l=Gi).child,22===l.tag&&null!==l.memoizedState?ks(a):null!==s?(s.return=l,Gi=s):ks(a);for(;null!==o;)Gi=o,bs(o,t,n),o=o.sibling;Gi=a,Yi=i,Ji=u}ws(e)}else 0!==(8772&a.subtreeFlags)&&null!==o?(o.return=a,Gi=o):ws(e)}}function ws(e){for(;null!==Gi;){var t=Gi;if(0!==(8772&t.flags)){var n=t.alternate;try{if(0!==(8772&t.flags))switch(t.tag){case 0:case 11:case 15:Ji||rs(5,t);break;case 1:var r=t.stateNode;if(4&t.flags&&!Ji)if(null===n)r.componentDidMount();else{var a=t.elementType===t.type?n.memoizedProps:ni(t.type,n.memoizedProps);r.componentDidUpdate(a,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var l=t.updateQueue;null!==l&&Ho(t,l,r);break;case 3:var i=t.updateQueue;if(null!==i){if(n=null,null!==t.child)switch(t.child.tag){case 5:case 1:n=t.child.stateNode}Ho(t,i,n)}break;case 5:var s=t.stateNode;if(null===n&&4&t.flags){n=s;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var c=t.alternate;if(null!==c){var d=c.memoizedState;if(null!==d){var f=d.dehydrated;null!==f&&$t(f)}}}break;default:throw Error(o(163))}Ji||512&t.flags&&as(t)}catch(p){Eu(t,t.return,p)}}if(t===e){Gi=null;break}if(null!==(n=t.sibling)){n.return=t.return,Gi=n;break}Gi=t.return}}function xs(e){for(;null!==Gi;){var t=Gi;if(t===e){Gi=null;break}var n=t.sibling;if(null!==n){n.return=t.return,Gi=n;break}Gi=t.return}}function ks(e){for(;null!==Gi;){var t=Gi;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{rs(4,t)}catch(s){Eu(t,n,s)}break;case 1:var r=t.stateNode;if("function"===typeof r.componentDidMount){var a=t.return;try{r.componentDidMount()}catch(s){Eu(t,a,s)}}var o=t.return;try{as(t)}catch(s){Eu(t,o,s)}break;case 5:var l=t.return;try{as(t)}catch(s){Eu(t,l,s)}}}catch(s){Eu(t,t.return,s)}if(t===e){Gi=null;break}var i=t.sibling;if(null!==i){i.return=t.return,Gi=i;break}Gi=t.return}}var Ss,Es=Math.ceil,Cs=w.ReactCurrentDispatcher,_s=w.ReactCurrentOwner,js=w.ReactCurrentBatchConfig,Ps=0,Ds=null,Ns=null,Rs=0,Ts=0,Ls=Ea(0),Is=0,Os=null,As=0,zs=0,Ms=0,Fs=null,Us=null,Bs=0,$s=1/0,Ws=null,Hs=!1,Vs=null,Qs=null,Ks=!1,qs=null,Ys=0,Js=0,Xs=null,Gs=-1,Zs=0;function eu(){return 0!==(6&Ps)?Xe():-1!==Gs?Gs:Gs=Xe()}function tu(e){return 0===(1&e.mode)?1:0!==(2&Ps)&&0!==Rs?Rs&-Rs:null!==vo.transition?(0===Zs&&(Zs=mt()),Zs):0!==(e=bt)?e:e=void 0===(e=window.event)?16:Jt(e.type)}function nu(e,t,n,r){if(50<Js)throw Js=0,Xs=null,Error(o(185));gt(e,n,r),0!==(2&Ps)&&e===Ds||(e===Ds&&(0===(2&Ps)&&(zs|=n),4===Is&&iu(e,Rs)),ru(e,r),1===n&&0===Ps&&0===(1&t.mode)&&($s=Xe()+500,Fa&&$a()))}function ru(e,t){var n=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,o=e.pendingLanes;0<o;){var l=31-lt(o),i=1<<l,s=a[l];-1===s?0!==(i&n)&&0===(i&r)||(a[l]=pt(i,t)):s<=t&&(e.expiredLanes|=i),o&=~i}}(e,t);var r=ft(e,e===Ds?Rs:0);if(0===r)null!==n&&qe(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(null!=n&&qe(n),1===t)0===e.tag?function(e){Fa=!0,Ba(e)}(su.bind(null,e)):Ba(su.bind(null,e)),la((function(){0===(6&Ps)&&$a()})),n=null;else{switch(wt(r)){case 1:n=Ze;break;case 4:n=et;break;case 16:default:n=tt;break;case 536870912:n=rt}n=Du(n,au.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function au(e,t){if(Gs=-1,Zs=0,0!==(6&Ps))throw Error(o(327));var n=e.callbackNode;if(ku()&&e.callbackNode!==n)return null;var r=ft(e,e===Ds?Rs:0);if(0===r)return null;if(0!==(30&r)||0!==(r&e.expiredLanes)||t)t=vu(e,r);else{t=r;var a=Ps;Ps|=2;var l=hu();for(Ds===e&&Rs===t||(Ws=null,$s=Xe()+500,fu(e,t));;)try{yu();break}catch(s){pu(e,s)}jo(),Cs.current=l,Ps=a,null!==Ns?t=0:(Ds=null,Rs=0,t=Is)}if(0!==t){if(2===t&&(0!==(a=ht(e))&&(r=a,t=ou(e,a))),1===t)throw n=Os,fu(e,0),iu(e,r),ru(e,Xe()),n;if(6===t)iu(e,r);else{if(a=e.current.alternate,0===(30&r)&&!function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var a=n[r],o=a.getSnapshot;a=a.value;try{if(!ir(o(),a))return!1}catch(i){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(a)&&(2===(t=vu(e,r))&&(0!==(l=ht(e))&&(r=l,t=ou(e,l))),1===t))throw n=Os,fu(e,0),iu(e,r),ru(e,Xe()),n;switch(e.finishedWork=a,e.finishedLanes=r,t){case 0:case 1:throw Error(o(345));case 2:case 5:xu(e,Us,Ws);break;case 3:if(iu(e,r),(130023424&r)===r&&10<(t=Bs+500-Xe())){if(0!==ft(e,0))break;if(((a=e.suspendedLanes)&r)!==r){eu(),e.pingedLanes|=e.suspendedLanes&a;break}e.timeoutHandle=ra(xu.bind(null,e,Us,Ws),t);break}xu(e,Us,Ws);break;case 4:if(iu(e,r),(4194240&r)===r)break;for(t=e.eventTimes,a=-1;0<r;){var i=31-lt(r);l=1<<i,(i=t[i])>a&&(a=i),r&=~l}if(r=a,10<(r=(120>(r=Xe()-r)?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Es(r/1960))-r)){e.timeoutHandle=ra(xu.bind(null,e,Us,Ws),r);break}xu(e,Us,Ws);break;default:throw Error(o(329))}}}return ru(e,Xe()),e.callbackNode===n?au.bind(null,e):null}function ou(e,t){var n=Fs;return e.current.memoizedState.isDehydrated&&(fu(e,t).flags|=256),2!==(e=vu(e,t))&&(t=Us,Us=n,null!==t&&lu(t)),e}function lu(e){null===Us?Us=e:Us.push.apply(Us,e)}function iu(e,t){for(t&=~Ms,t&=~zs,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-lt(t),r=1<<n;e[n]=-1,t&=~r}}function su(e){if(0!==(6&Ps))throw Error(o(327));ku();var t=ft(e,0);if(0===(1&t))return ru(e,Xe()),null;var n=vu(e,t);if(0!==e.tag&&2===n){var r=ht(e);0!==r&&(t=r,n=ou(e,r))}if(1===n)throw n=Os,fu(e,0),iu(e,t),ru(e,Xe()),n;if(6===n)throw Error(o(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,xu(e,Us,Ws),ru(e,Xe()),null}function uu(e,t){var n=Ps;Ps|=1;try{return e(t)}finally{0===(Ps=n)&&($s=Xe()+500,Fa&&$a())}}function cu(e){null!==qs&&0===qs.tag&&0===(6&Ps)&&ku();var t=Ps;Ps|=1;var n=js.transition,r=bt;try{if(js.transition=null,bt=1,e)return e()}finally{bt=r,js.transition=n,0===(6&(Ps=t))&&$a()}}function du(){Ts=Ls.current,Ca(Ls)}function fu(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,aa(n)),null!==Ns)for(n=Ns.return;null!==n;){var r=n;switch(to(r),r.tag){case 1:null!==(r=r.type.childContextTypes)&&void 0!==r&&La();break;case 3:Xo(),Ca(Da),Ca(Pa),rl();break;case 5:Zo(r);break;case 4:Xo();break;case 13:case 19:Ca(el);break;case 10:Po(r.type._context);break;case 22:case 23:du()}n=n.return}if(Ds=e,Ns=e=Lu(e.current,null),Rs=Ts=t,Is=0,Os=null,Ms=zs=As=0,Us=Fs=null,null!==To){for(t=0;t<To.length;t++)if(null!==(r=(n=To[t]).interleaved)){n.interleaved=null;var a=r.next,o=n.pending;if(null!==o){var l=o.next;o.next=a,r.next=l}n.pending=r}To=null}return e}function pu(e,t){for(;;){var n=Ns;try{if(jo(),al.current=Gl,cl){for(var r=il.memoizedState;null!==r;){var a=r.queue;null!==a&&(a.pending=null),r=r.next}cl=!1}if(ll=0,ul=sl=il=null,dl=!1,fl=0,_s.current=null,null===n||null===n.return){Is=1,Os=t,Ns=null;break}e:{var l=e,i=n.return,s=n,u=t;if(t=Rs,s.flags|=32768,null!==u&&"object"===typeof u&&"function"===typeof u.then){var c=u,d=s,f=d.tag;if(0===(1&d.mode)&&(0===f||11===f||15===f)){var p=d.alternate;p?(d.updateQueue=p.updateQueue,d.memoizedState=p.memoizedState,d.lanes=p.lanes):(d.updateQueue=null,d.memoizedState=null)}var h=vi(i);if(null!==h){h.flags&=-257,gi(h,i,s,0,t),1&h.mode&&mi(l,c,t),u=c;var m=(t=h).updateQueue;if(null===m){var v=new Set;v.add(u),t.updateQueue=v}else m.add(u);break e}if(0===(1&t)){mi(l,c,t),mu();break e}u=Error(o(426))}else if(ao&&1&s.mode){var g=vi(i);if(null!==g){0===(65536&g.flags)&&(g.flags|=256),gi(g,i,s,0,t),mo(ui(u,s));break e}}l=u=ui(u,s),4!==Is&&(Is=2),null===Fs?Fs=[l]:Fs.push(l),l=i;do{switch(l.tag){case 3:l.flags|=65536,t&=-t,l.lanes|=t,$o(l,pi(0,u,t));break e;case 1:s=u;var y=l.type,b=l.stateNode;if(0===(128&l.flags)&&("function"===typeof y.getDerivedStateFromError||null!==b&&"function"===typeof b.componentDidCatch&&(null===Qs||!Qs.has(b)))){l.flags|=65536,t&=-t,l.lanes|=t,$o(l,hi(l,s,t));break e}}l=l.return}while(null!==l)}wu(n)}catch(w){t=w,Ns===n&&null!==n&&(Ns=n=n.return);continue}break}}function hu(){var e=Cs.current;return Cs.current=Gl,null===e?Gl:e}function mu(){0!==Is&&3!==Is&&2!==Is||(Is=4),null===Ds||0===(268435455&As)&&0===(268435455&zs)||iu(Ds,Rs)}function vu(e,t){var n=Ps;Ps|=2;var r=hu();for(Ds===e&&Rs===t||(Ws=null,fu(e,t));;)try{gu();break}catch(a){pu(e,a)}if(jo(),Ps=n,Cs.current=r,null!==Ns)throw Error(o(261));return Ds=null,Rs=0,Is}function gu(){for(;null!==Ns;)bu(Ns)}function yu(){for(;null!==Ns&&!Ye();)bu(Ns)}function bu(e){var t=Ss(e.alternate,e,Ts);e.memoizedProps=e.pendingProps,null===t?wu(e):Ns=t,_s.current=null}function wu(e){var t=e;do{var n=t.alternate;if(e=t.return,0===(32768&t.flags)){if(null!==(n=Ki(n,t,Ts)))return void(Ns=n)}else{if(null!==(n=qi(n,t)))return n.flags&=32767,void(Ns=n);if(null===e)return Is=6,void(Ns=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}if(null!==(t=t.sibling))return void(Ns=t);Ns=t=e}while(null!==t);0===Is&&(Is=5)}function xu(e,t,n){var r=bt,a=js.transition;try{js.transition=null,bt=1,function(e,t,n,r){do{ku()}while(null!==qs);if(0!==(6&Ps))throw Error(o(327));n=e.finishedWork;var a=e.finishedLanes;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(o(177));e.callbackNode=null,e.callbackPriority=0;var l=n.lanes|n.childLanes;if(function(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var a=31-lt(n),o=1<<a;t[a]=0,r[a]=-1,e[a]=-1,n&=~o}}(e,l),e===Ds&&(Ns=Ds=null,Rs=0),0===(2064&n.subtreeFlags)&&0===(2064&n.flags)||Ks||(Ks=!0,Du(tt,(function(){return ku(),null}))),l=0!==(15990&n.flags),0!==(15990&n.subtreeFlags)||l){l=js.transition,js.transition=null;var i=bt;bt=1;var s=Ps;Ps|=4,_s.current=null,function(e,t){if(ea=Ht,pr(e=fr())){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var a=r.anchorOffset,l=r.focusNode;r=r.focusOffset;try{n.nodeType,l.nodeType}catch(x){n=null;break e}var i=0,s=-1,u=-1,c=0,d=0,f=e,p=null;t:for(;;){for(var h;f!==n||0!==a&&3!==f.nodeType||(s=i+a),f!==l||0!==r&&3!==f.nodeType||(u=i+r),3===f.nodeType&&(i+=f.nodeValue.length),null!==(h=f.firstChild);)p=f,f=h;for(;;){if(f===e)break t;if(p===n&&++c===a&&(s=i),p===l&&++d===r&&(u=i),null!==(h=f.nextSibling))break;p=(f=p).parentNode}f=h}n=-1===s||-1===u?null:{start:s,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(ta={focusedElem:e,selectionRange:n},Ht=!1,Gi=t;null!==Gi;)if(e=(t=Gi).child,0!==(1028&t.subtreeFlags)&&null!==e)e.return=t,Gi=e;else for(;null!==Gi;){t=Gi;try{var m=t.alternate;if(0!==(1024&t.flags))switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==m){var v=m.memoizedProps,g=m.memoizedState,y=t.stateNode,b=y.getSnapshotBeforeUpdate(t.elementType===t.type?v:ni(t.type,v),g);y.__reactInternalSnapshotBeforeUpdate=b}break;case 3:var w=t.stateNode.containerInfo;1===w.nodeType?w.textContent="":9===w.nodeType&&w.documentElement&&w.removeChild(w.documentElement);break;default:throw Error(o(163))}}catch(x){Eu(t,t.return,x)}if(null!==(e=t.sibling)){e.return=t.return,Gi=e;break}Gi=t.return}m=ts,ts=!1}(e,n),vs(n,e),hr(ta),Ht=!!ea,ta=ea=null,e.current=n,ys(n,e,a),Je(),Ps=s,bt=i,js.transition=l}else e.current=n;if(Ks&&(Ks=!1,qs=e,Ys=a),l=e.pendingLanes,0===l&&(Qs=null),function(e){if(ot&&"function"===typeof ot.onCommitFiberRoot)try{ot.onCommitFiberRoot(at,e,void 0,128===(128&e.current.flags))}catch(t){}}(n.stateNode),ru(e,Xe()),null!==t)for(r=e.onRecoverableError,n=0;n<t.length;n++)a=t[n],r(a.value,{componentStack:a.stack,digest:a.digest});if(Hs)throw Hs=!1,e=Vs,Vs=null,e;0!==(1&Ys)&&0!==e.tag&&ku(),l=e.pendingLanes,0!==(1&l)?e===Xs?Js++:(Js=0,Xs=e):Js=0,$a()}(e,t,n,r)}finally{js.transition=a,bt=r}return null}function ku(){if(null!==qs){var e=wt(Ys),t=js.transition,n=bt;try{if(js.transition=null,bt=16>e?16:e,null===qs)var r=!1;else{if(e=qs,qs=null,Ys=0,0!==(6&Ps))throw Error(o(331));var a=Ps;for(Ps|=4,Gi=e.current;null!==Gi;){var l=Gi,i=l.child;if(0!==(16&Gi.flags)){var s=l.deletions;if(null!==s){for(var u=0;u<s.length;u++){var c=s[u];for(Gi=c;null!==Gi;){var d=Gi;switch(d.tag){case 0:case 11:case 15:ns(8,d,l)}var f=d.child;if(null!==f)f.return=d,Gi=f;else for(;null!==Gi;){var p=(d=Gi).sibling,h=d.return;if(os(d),d===c){Gi=null;break}if(null!==p){p.return=h,Gi=p;break}Gi=h}}}var m=l.alternate;if(null!==m){var v=m.child;if(null!==v){m.child=null;do{var g=v.sibling;v.sibling=null,v=g}while(null!==v)}}Gi=l}}if(0!==(2064&l.subtreeFlags)&&null!==i)i.return=l,Gi=i;else e:for(;null!==Gi;){if(0!==(2048&(l=Gi).flags))switch(l.tag){case 0:case 11:case 15:ns(9,l,l.return)}var y=l.sibling;if(null!==y){y.return=l.return,Gi=y;break e}Gi=l.return}}var b=e.current;for(Gi=b;null!==Gi;){var w=(i=Gi).child;if(0!==(2064&i.subtreeFlags)&&null!==w)w.return=i,Gi=w;else e:for(i=b;null!==Gi;){if(0!==(2048&(s=Gi).flags))try{switch(s.tag){case 0:case 11:case 15:rs(9,s)}}catch(k){Eu(s,s.return,k)}if(s===i){Gi=null;break e}var x=s.sibling;if(null!==x){x.return=s.return,Gi=x;break e}Gi=s.return}}if(Ps=a,$a(),ot&&"function"===typeof ot.onPostCommitFiberRoot)try{ot.onPostCommitFiberRoot(at,e)}catch(k){}r=!0}return r}finally{bt=n,js.transition=t}}return!1}function Su(e,t,n){e=Uo(e,t=pi(0,t=ui(n,t),1),1),t=eu(),null!==e&&(gt(e,1,t),ru(e,t))}function Eu(e,t,n){if(3===e.tag)Su(e,e,n);else for(;null!==t;){if(3===t.tag){Su(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===Qs||!Qs.has(r))){t=Uo(t,e=hi(t,e=ui(n,e),1),1),e=eu(),null!==t&&(gt(t,1,e),ru(t,e));break}}t=t.return}}function Cu(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),t=eu(),e.pingedLanes|=e.suspendedLanes&n,Ds===e&&(Rs&n)===n&&(4===Is||3===Is&&(130023424&Rs)===Rs&&500>Xe()-Bs?fu(e,0):Ms|=n),ru(e,t)}function _u(e,t){0===t&&(0===(1&e.mode)?t=1:(t=ct,0===(130023424&(ct<<=1))&&(ct=4194304)));var n=eu();null!==(e=Oo(e,t))&&(gt(e,t,n),ru(e,n))}function ju(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),_u(e,n)}function Pu(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,a=e.memoizedState;null!==a&&(n=a.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(o(314))}null!==r&&r.delete(t),_u(e,n)}function Du(e,t){return Ke(e,t)}function Nu(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ru(e,t,n,r){return new Nu(e,t,n,r)}function Tu(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Lu(e,t){var n=e.alternate;return null===n?((n=Ru(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=14680064&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Iu(e,t,n,r,a,l){var i=2;if(r=e,"function"===typeof e)Tu(e)&&(i=1);else if("string"===typeof e)i=5;else e:switch(e){case S:return Ou(n.children,a,l,t);case E:i=8,a|=8;break;case C:return(e=Ru(12,n,t,2|a)).elementType=C,e.lanes=l,e;case D:return(e=Ru(13,n,t,a)).elementType=D,e.lanes=l,e;case N:return(e=Ru(19,n,t,a)).elementType=N,e.lanes=l,e;case L:return Au(n,a,l,t);default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case _:i=10;break e;case j:i=9;break e;case P:i=11;break e;case R:i=14;break e;case T:i=16,r=null;break e}throw Error(o(130,null==e?e:typeof e,""))}return(t=Ru(i,n,t,a)).elementType=e,t.type=r,t.lanes=l,t}function Ou(e,t,n,r){return(e=Ru(7,e,r,t)).lanes=n,e}function Au(e,t,n,r){return(e=Ru(22,e,r,t)).elementType=L,e.lanes=n,e.stateNode={isHidden:!1},e}function zu(e,t,n){return(e=Ru(6,e,null,t)).lanes=n,e}function Mu(e,t,n){return(t=Ru(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Fu(e,t,n,r,a){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=vt(0),this.expirationTimes=vt(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=vt(0),this.identifierPrefix=r,this.onRecoverableError=a,this.mutableSourceEagerHydrationData=null}function Uu(e,t,n,r,a,o,l,i,s){return e=new Fu(e,t,n,i,s),1===t?(t=1,!0===o&&(t|=8)):t=0,o=Ru(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},zo(o),e}function Bu(e){if(!e)return ja;e:{if($e(e=e._reactInternals)!==e||1!==e.tag)throw Error(o(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Ta(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(o(171))}if(1===e.tag){var n=e.type;if(Ta(n))return Oa(e,n,t)}return t}function $u(e,t,n,r,a,o,l,i,s){return(e=Uu(n,r,!0,e,0,o,0,i,s)).context=Bu(null),n=e.current,(o=Fo(r=eu(),a=tu(n))).callback=void 0!==t&&null!==t?t:null,Uo(n,o,a),e.current.lanes=a,gt(e,a,r),ru(e,r),e}function Wu(e,t,n,r){var a=t.current,o=eu(),l=tu(a);return n=Bu(n),null===t.context?t.context=n:t.pendingContext=n,(t=Fo(o,l)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),null!==(e=Uo(a,t,l))&&(nu(e,a,l,o),Bo(e,a,l)),l}function Hu(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function Vu(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function Qu(e,t){Vu(e,t),(e=e.alternate)&&Vu(e,t)}Ss=function(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps||Da.current)bi=!0;else{if(0===(e.lanes&n)&&0===(128&t.flags))return bi=!1,function(e,t,n){switch(t.tag){case 3:Di(t),ho();break;case 5:Go(t);break;case 1:Ta(t.type)&&Aa(t);break;case 4:Jo(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,a=t.memoizedProps.value;_a(So,r._currentValue),r._currentValue=a;break;case 13:if(null!==(r=t.memoizedState))return null!==r.dehydrated?(_a(el,1&el.current),t.flags|=128,null):0!==(n&t.child.childLanes)?zi(e,t,n):(_a(el,1&el.current),null!==(e=Hi(e,t,n))?e.sibling:null);_a(el,1&el.current);break;case 19:if(r=0!==(n&t.childLanes),0!==(128&e.flags)){if(r)return $i(e,t,n);t.flags|=128}if(null!==(a=t.memoizedState)&&(a.rendering=null,a.tail=null,a.lastEffect=null),_a(el,el.current),r)break;return null;case 22:case 23:return t.lanes=0,Ei(e,t,n)}return Hi(e,t,n)}(e,t,n);bi=0!==(131072&e.flags)}else bi=!1,ao&&0!==(1048576&t.flags)&&Za(t,Qa,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Wi(e,t),e=t.pendingProps;var a=Ra(t,Pa.current);No(t,n),a=vl(null,t,r,e,a,n);var l=gl();return t.flags|=1,"object"===typeof a&&null!==a&&"function"===typeof a.render&&void 0===a.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Ta(r)?(l=!0,Aa(t)):l=!1,t.memoizedState=null!==a.state&&void 0!==a.state?a.state:null,zo(t),a.updater=ai,t.stateNode=a,a._reactInternals=t,si(t,r,e,n),t=Pi(null,t,r,!0,l,n)):(t.tag=0,ao&&l&&eo(t),wi(null,t,a,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Wi(e,t),e=t.pendingProps,r=(a=r._init)(r._payload),t.type=r,a=t.tag=function(e){if("function"===typeof e)return Tu(e)?1:0;if(void 0!==e&&null!==e){if((e=e.$$typeof)===P)return 11;if(e===R)return 14}return 2}(r),e=ni(r,e),a){case 0:t=_i(null,t,r,e,n);break e;case 1:t=ji(null,t,r,e,n);break e;case 11:t=xi(null,t,r,e,n);break e;case 14:t=ki(null,t,r,ni(r.type,e),n);break e}throw Error(o(306,r,""))}return t;case 0:return r=t.type,a=t.pendingProps,_i(e,t,r,a=t.elementType===r?a:ni(r,a),n);case 1:return r=t.type,a=t.pendingProps,ji(e,t,r,a=t.elementType===r?a:ni(r,a),n);case 3:e:{if(Di(t),null===e)throw Error(o(387));r=t.pendingProps,a=(l=t.memoizedState).element,Mo(e,t),Wo(t,r,null,n);var i=t.memoizedState;if(r=i.element,l.isDehydrated){if(l={element:r,isDehydrated:!1,cache:i.cache,pendingSuspenseBoundaries:i.pendingSuspenseBoundaries,transitions:i.transitions},t.updateQueue.baseState=l,t.memoizedState=l,256&t.flags){t=Ni(e,t,r,n,a=ui(Error(o(423)),t));break e}if(r!==a){t=Ni(e,t,r,n,a=ui(Error(o(424)),t));break e}for(ro=ua(t.stateNode.containerInfo.firstChild),no=t,ao=!0,oo=null,n=ko(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(ho(),r===a){t=Hi(e,t,n);break e}wi(e,t,r,n)}t=t.child}return t;case 5:return Go(t),null===e&&uo(t),r=t.type,a=t.pendingProps,l=null!==e?e.memoizedProps:null,i=a.children,na(r,a)?i=null:null!==l&&na(r,l)&&(t.flags|=32),Ci(e,t),wi(e,t,i,n),t.child;case 6:return null===e&&uo(t),null;case 13:return zi(e,t,n);case 4:return Jo(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=xo(t,null,r,n):wi(e,t,r,n),t.child;case 11:return r=t.type,a=t.pendingProps,xi(e,t,r,a=t.elementType===r?a:ni(r,a),n);case 7:return wi(e,t,t.pendingProps,n),t.child;case 8:case 12:return wi(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,a=t.pendingProps,l=t.memoizedProps,i=a.value,_a(So,r._currentValue),r._currentValue=i,null!==l)if(ir(l.value,i)){if(l.children===a.children&&!Da.current){t=Hi(e,t,n);break e}}else for(null!==(l=t.child)&&(l.return=t);null!==l;){var s=l.dependencies;if(null!==s){i=l.child;for(var u=s.firstContext;null!==u;){if(u.context===r){if(1===l.tag){(u=Fo(-1,n&-n)).tag=2;var c=l.updateQueue;if(null!==c){var d=(c=c.shared).pending;null===d?u.next=u:(u.next=d.next,d.next=u),c.pending=u}}l.lanes|=n,null!==(u=l.alternate)&&(u.lanes|=n),Do(l.return,n,t),s.lanes|=n;break}u=u.next}}else if(10===l.tag)i=l.type===t.type?null:l.child;else if(18===l.tag){if(null===(i=l.return))throw Error(o(341));i.lanes|=n,null!==(s=i.alternate)&&(s.lanes|=n),Do(i,n,t),i=l.sibling}else i=l.child;if(null!==i)i.return=l;else for(i=l;null!==i;){if(i===t){i=null;break}if(null!==(l=i.sibling)){l.return=i.return,i=l;break}i=i.return}l=i}wi(e,t,a.children,n),t=t.child}return t;case 9:return a=t.type,r=t.pendingProps.children,No(t,n),r=r(a=Ro(a)),t.flags|=1,wi(e,t,r,n),t.child;case 14:return a=ni(r=t.type,t.pendingProps),ki(e,t,r,a=ni(r.type,a),n);case 15:return Si(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,a=t.pendingProps,a=t.elementType===r?a:ni(r,a),Wi(e,t),t.tag=1,Ta(r)?(e=!0,Aa(t)):e=!1,No(t,n),li(t,r,a),si(t,r,a,n),Pi(null,t,r,!0,e,n);case 19:return $i(e,t,n);case 22:return Ei(e,t,n)}throw Error(o(156,t.tag))};var Ku="function"===typeof reportError?reportError:function(e){console.error(e)};function qu(e){this._internalRoot=e}function Yu(e){this._internalRoot=e}function Ju(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function Xu(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Gu(){}function Zu(e,t,n,r,a){var o=n._reactRootContainer;if(o){var l=o;if("function"===typeof a){var i=a;a=function(){var e=Hu(l);i.call(e)}}Wu(t,l,e,a)}else l=function(e,t,n,r,a){if(a){if("function"===typeof r){var o=r;r=function(){var e=Hu(l);o.call(e)}}var l=$u(t,r,e,0,null,!1,0,"",Gu);return e._reactRootContainer=l,e[ha]=l.current,$r(8===e.nodeType?e.parentNode:e),cu(),l}for(;a=e.lastChild;)e.removeChild(a);if("function"===typeof r){var i=r;r=function(){var e=Hu(s);i.call(e)}}var s=Uu(e,0,!1,null,0,!1,0,"",Gu);return e._reactRootContainer=s,e[ha]=s.current,$r(8===e.nodeType?e.parentNode:e),cu((function(){Wu(t,s,n,r)})),s}(n,t,e,a,r);return Hu(l)}Yu.prototype.render=qu.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(o(409));Wu(e,t,null,null)},Yu.prototype.unmount=qu.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;cu((function(){Wu(null,e,null,null)})),t[ha]=null}},Yu.prototype.unstable_scheduleHydration=function(e){if(e){var t=Et();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Lt.length&&0!==t&&t<Lt[n].priority;n++);Lt.splice(n,0,e),0===n&&zt(e)}},xt=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=dt(t.pendingLanes);0!==n&&(yt(t,1|n),ru(t,Xe()),0===(6&Ps)&&($s=Xe()+500,$a()))}break;case 13:cu((function(){var t=Oo(e,1);if(null!==t){var n=eu();nu(t,e,1,n)}})),Qu(e,1)}},kt=function(e){if(13===e.tag){var t=Oo(e,134217728);if(null!==t)nu(t,e,134217728,eu());Qu(e,134217728)}},St=function(e){if(13===e.tag){var t=tu(e),n=Oo(e,t);if(null!==n)nu(n,e,t,eu());Qu(e,t)}},Et=function(){return bt},Ct=function(e,t){var n=bt;try{return bt=e,t()}finally{bt=n}},ke=function(e,t,n){switch(t){case"input":if(G(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var a=xa(r);if(!a)throw Error(o(90));K(r),G(r,a)}}}break;case"textarea":oe(e,n);break;case"select":null!=(t=n.value)&&ne(e,!!n.multiple,t,!1)}},Pe=uu,De=cu;var ec={usingClientEntryPoint:!1,Events:[ba,wa,xa,_e,je,uu]},tc={findFiberByHostInstance:ya,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},nc={bundleType:tc.bundleType,version:tc.version,rendererPackageName:tc.rendererPackageName,rendererConfig:tc.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:w.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=Ve(e))?null:e.stateNode},findFiberByHostInstance:tc.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var rc=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!rc.isDisabled&&rc.supportsFiber)try{at=rc.inject(nc),ot=rc}catch(ce){}}t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ec,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!Ju(t))throw Error(o(200));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:k,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.createRoot=function(e,t){if(!Ju(e))throw Error(o(299));var n=!1,r="",a=Ku;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onRecoverableError&&(a=t.onRecoverableError)),t=Uu(e,1,!1,null,0,n,0,r,a),e[ha]=t.current,$r(8===e.nodeType?e.parentNode:e),new qu(t)},t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(o(188));throw e=Object.keys(e).join(","),Error(o(268,e))}return e=null===(e=Ve(t))?null:e.stateNode},t.flushSync=function(e){return cu(e)},t.hydrate=function(e,t,n){if(!Xu(t))throw Error(o(200));return Zu(null,e,t,!0,n)},t.hydrateRoot=function(e,t,n){if(!Ju(e))throw Error(o(405));var r=null!=n&&n.hydratedSources||null,a=!1,l="",i=Ku;if(null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(a=!0),void 0!==n.identifierPrefix&&(l=n.identifierPrefix),void 0!==n.onRecoverableError&&(i=n.onRecoverableError)),t=$u(t,null,e,1,null!=n?n:null,a,0,l,i),e[ha]=t.current,$r(e),r)for(e=0;e<r.length;e++)a=(a=(n=r[e])._getVersion)(n._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[n,a]:t.mutableSourceEagerHydrationData.push(n,a);return new Yu(t)},t.render=function(e,t,n){if(!Xu(t))throw Error(o(200));return Zu(null,e,t,!1,n)},t.unmountComponentAtNode=function(e){if(!Xu(e))throw Error(o(40));return!!e._reactRootContainer&&(cu((function(){Zu(null,null,e,!1,(function(){e._reactRootContainer=null,e[ha]=null}))})),!0)},t.unstable_batchedUpdates=uu,t.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Xu(n))throw Error(o(200));if(null==e||void 0===e._reactInternals)throw Error(o(38));return Zu(e,t,n,!1,r)},t.version="18.3.1-next-f1338f8080-20240426"},833:(e,t,n)=>{e.exports=n.p+"static/media/loading.5a153bde215bf12ed86a.gif"},853:(e,t,n)=>{e.exports=n(234)},950:(e,t,n)=>{!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(730)}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var o=t[r]={exports:{}};return e[r](o,o.exports,n),o.exports}n.m=e,(()=>{var e,t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__;n.t=function(r,a){if(1&a&&(r=this(r)),8&a)return r;if("object"===typeof r&&r){if(4&a&&r.__esModule)return r;if(16&a&&"function"===typeof r.then)return r}var o=Object.create(null);n.r(o);var l={};e=e||[null,t({}),t([]),t(t)];for(var i=2&a&&r;"object"==typeof i&&!~e.indexOf(i);i=t(i))Object.getOwnPropertyNames(i).forEach((e=>l[e]=()=>r[e]));return l.default=()=>r,n.d(o,l),o}})(),n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.f={},n.e=e=>Promise.all(Object.keys(n.f).reduce(((t,r)=>(n.f[r](e,t),t)),[])),n.u=e=>"static/js/"+e+".63c4db86.chunk.js",n.miniCssF=e=>{},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={};n.l=(t,r,a,o)=>{if(e[t])e[t].push(r);else{var l,i;if(void 0!==a)for(var s=document.getElementsByTagName("script"),u=0;u<s.length;u++){var c=s[u];if(c.getAttribute("src")==t||c.getAttribute("data-webpack")=="ui:"+a){l=c;break}}l||(i=!0,(l=document.createElement("script")).charset="utf-8",l.timeout=120,n.nc&&l.setAttribute("nonce",n.nc),l.setAttribute("data-webpack","ui:"+a),l.src=t),e[t]=[r];var d=(n,r)=>{l.onerror=l.onload=null,clearTimeout(f);var a=e[t];if(delete e[t],l.parentNode&&l.parentNode.removeChild(l),a&&a.forEach((e=>e(r))),n)return n(r)},f=setTimeout(d.bind(null,void 0,{type:"timeout",target:l}),12e4);l.onerror=d.bind(null,l.onerror),l.onload=d.bind(null,l.onload),i&&document.head.appendChild(l)}}})(),n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.p="/plugin-server/public/ui/",(()=>{var e={792:0};n.f.j=(t,r)=>{var a=n.o(e,t)?e[t]:void 0;if(0!==a)if(a)r.push(a[2]);else{var o=new Promise(((n,r)=>a=e[t]=[n,r]));r.push(a[2]=o);var l=n.p+n.u(t),i=new Error;n.l(l,(r=>{if(n.o(e,t)&&(0!==(a=e[t])&&(e[t]=void 0),a)){var o=r&&("load"===r.type?"missing":r.type),l=r&&r.target&&r.target.src;i.message="Loading chunk "+t+" failed.\n("+o+": "+l+")",i.name="ChunkLoadError",i.type=o,i.request=l,a[1](i)}}),"chunk-"+t,t)}};var t=(t,r)=>{var a,o,l=r[0],i=r[1],s=r[2],u=0;if(l.some((t=>0!==e[t]))){for(a in i)n.o(i,a)&&(n.m[a]=i[a]);if(s)s(n)}for(t&&t(r);u<l.length;u++)o=l[u],n.o(e,o)&&e[o]&&e[o][0](),e[o]=0},r=self.webpackChunkui=self.webpackChunkui||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})();var r,a=n(43),o=n.t(a,2),l=n(391),i=n(950),s=n.t(i,2);function u(){return u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},u.apply(this,arguments)}!function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"}(r||(r={}));const c="popstate";function d(e,t){if(!1===e||null===e||"undefined"===typeof e)throw new Error(t)}function f(e,t){if(!e){"undefined"!==typeof console&&console.warn(t);try{throw new Error(t)}catch(n){}}}function p(e,t){return{usr:e.state,key:e.key,idx:t}}function h(e,t,n,r){return void 0===n&&(n=null),u({pathname:"string"===typeof e?e:e.pathname,search:"",hash:""},"string"===typeof t?v(t):t,{state:n,key:t&&t.key||r||Math.random().toString(36).substr(2,8)})}function m(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),r&&"#"!==r&&(t+="#"===r.charAt(0)?r:"#"+r),t}function v(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function g(e,t,n,a){void 0===a&&(a={});let{window:o=document.defaultView,v5Compat:l=!1}=a,i=o.history,s=r.Pop,f=null,v=g();function g(){return(i.state||{idx:null}).idx}function y(){s=r.Pop;let e=g(),t=null==e?null:e-v;v=e,f&&f({action:s,location:w.location,delta:t})}function b(e){let t="null"!==o.location.origin?o.location.origin:o.location.href,n="string"===typeof e?e:m(e);return n=n.replace(/ $/,"%20"),d(t,"No window.location.(origin|href) available to create URL for href: "+n),new URL(n,t)}null==v&&(v=0,i.replaceState(u({},i.state,{idx:v}),""));let w={get action(){return s},get location(){return e(o,i)},listen(e){if(f)throw new Error("A history only accepts one active listener");return o.addEventListener(c,y),f=e,()=>{o.removeEventListener(c,y),f=null}},createHref:e=>t(o,e),createURL:b,encodeLocation(e){let t=b(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){s=r.Push;let a=h(w.location,e,t);n&&n(a,e),v=g()+1;let u=p(a,v),c=w.createHref(a);try{i.pushState(u,"",c)}catch(d){if(d instanceof DOMException&&"DataCloneError"===d.name)throw d;o.location.assign(c)}l&&f&&f({action:s,location:w.location,delta:1})},replace:function(e,t){s=r.Replace;let a=h(w.location,e,t);n&&n(a,e),v=g();let o=p(a,v),u=w.createHref(a);i.replaceState(o,"",u),l&&f&&f({action:s,location:w.location,delta:0})},go:e=>i.go(e)};return w}var y;!function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"}(y||(y={}));const b=new Set(["lazy","caseSensitive","path","id","index","children"]);function w(e,t,n,r){return void 0===n&&(n=[]),void 0===r&&(r={}),e.map(((e,a)=>{let o=[...n,String(a)],l="string"===typeof e.id?e.id:o.join("-");if(d(!0!==e.index||!e.children,"Cannot specify children on an index route"),d(!r[l],'Found a route id collision on id "'+l+"\".  Route id's must be globally unique within Data Router usages"),function(e){return!0===e.index}(e)){let n=u({},e,t(e),{id:l});return r[l]=n,n}{let n=u({},e,t(e),{id:l,children:void 0});return r[l]=n,e.children&&(n.children=w(e.children,t,o,r)),n}}))}function x(e,t,n){return void 0===n&&(n="/"),k(e,t,n,!1)}function k(e,t,n,r){let a=A(("string"===typeof t?v(t):t).pathname||"/",n);if(null==a)return null;let o=S(e);!function(e){e.sort(((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every(((e,n)=>e===t[n]));return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map((e=>e.childrenIndex)),t.routesMeta.map((e=>e.childrenIndex)))))}(o);let l=null;for(let i=0;null==l&&i<o.length;++i){let e=O(a);l=L(o[i],e,r)}return l}function S(e,t,n,r){void 0===t&&(t=[]),void 0===n&&(n=[]),void 0===r&&(r="");let a=(e,a,o)=>{let l={relativePath:void 0===o?e.path||"":o,caseSensitive:!0===e.caseSensitive,childrenIndex:a,route:e};l.relativePath.startsWith("/")&&(d(l.relativePath.startsWith(r),'Absolute route path "'+l.relativePath+'" nested under path "'+r+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),l.relativePath=l.relativePath.slice(r.length));let i=B([r,l.relativePath]),s=n.concat(l);e.children&&e.children.length>0&&(d(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'+i+'".'),S(e.children,t,s,i)),(null!=e.path||e.index)&&t.push({path:i,score:T(i,e.index),routesMeta:s})};return e.forEach(((e,t)=>{var n;if(""!==e.path&&null!=(n=e.path)&&n.includes("?"))for(let r of E(e.path))a(e,t,r);else a(e,t)})),t}function E(e){let t=e.split("/");if(0===t.length)return[];let[n,...r]=t,a=n.endsWith("?"),o=n.replace(/\?$/,"");if(0===r.length)return a?[o,""]:[o];let l=E(r.join("/")),i=[];return i.push(...l.map((e=>""===e?o:[o,e].join("/")))),a&&i.push(...l),i.map((t=>e.startsWith("/")&&""===t?"/":t))}const C=/^:[\w-]+$/,_=3,j=2,P=1,D=10,N=-2,R=e=>"*"===e;function T(e,t){let n=e.split("/"),r=n.length;return n.some(R)&&(r+=N),t&&(r+=j),n.filter((e=>!R(e))).reduce(((e,t)=>e+(C.test(t)?_:""===t?P:D)),r)}function L(e,t,n){void 0===n&&(n=!1);let{routesMeta:r}=e,a={},o="/",l=[];for(let i=0;i<r.length;++i){let e=r[i],s=i===r.length-1,u="/"===o?t:t.slice(o.length)||"/",c=I({path:e.relativePath,caseSensitive:e.caseSensitive,end:s},u),d=e.route;if(!c&&s&&n&&!r[r.length-1].route.index&&(c=I({path:e.relativePath,caseSensitive:e.caseSensitive,end:!1},u)),!c)return null;Object.assign(a,c.params),l.push({params:a,pathname:B([o,c.pathname]),pathnameBase:$(B([o,c.pathnameBase])),route:d}),"/"!==c.pathnameBase&&(o=B([o,c.pathnameBase]))}return l}function I(e,t){"string"===typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=function(e,t,n){void 0===t&&(t=!1);void 0===n&&(n=!0);f("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were "'+e.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+e.replace(/\*$/,"/*")+'".');let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,((e,t,n)=>(r.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)")));e.endsWith("*")?(r.push({paramName:"*"}),a+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":""!==e&&"/"!==e&&(a+="(?:(?=\\/|$))");let o=new RegExp(a,t?void 0:"i");return[o,r]}(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let o=a[0],l=o.replace(/(.)\/+$/,"$1"),i=a.slice(1);return{params:r.reduce(((e,t,n)=>{let{paramName:r,isOptional:a}=t;if("*"===r){let e=i[n]||"";l=o.slice(0,o.length-e.length).replace(/(.)\/+$/,"$1")}const s=i[n];return e[r]=a&&!s?void 0:(s||"").replace(/%2F/g,"/"),e}),{}),pathname:o,pathnameBase:l,pattern:e}}function O(e){try{return e.split("/").map((e=>decodeURIComponent(e).replace(/\//g,"%2F"))).join("/")}catch(t){return f(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+t+")."),e}}function A(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&"/"!==r?null:e.slice(n)||"/"}function z(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified `to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the `to."+n+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function M(e){return e.filter(((e,t)=>0===t||e.route.path&&e.route.path.length>0))}function F(e,t){let n=M(e);return t?n.map(((e,t)=>t===n.length-1?e.pathname:e.pathnameBase)):n.map((e=>e.pathnameBase))}function U(e,t,n,r){let a;void 0===r&&(r=!1),"string"===typeof e?a=v(e):(a=u({},e),d(!a.pathname||!a.pathname.includes("?"),z("?","pathname","search",a)),d(!a.pathname||!a.pathname.includes("#"),z("#","pathname","hash",a)),d(!a.search||!a.search.includes("#"),z("#","search","hash",a)));let o,l=""===e||""===a.pathname,i=l?"/":a.pathname;if(null==i)o=n;else{let e=t.length-1;if(!r&&i.startsWith("..")){let t=i.split("/");for(;".."===t[0];)t.shift(),e-=1;a.pathname=t.join("/")}o=e>=0?t[e]:"/"}let s=function(e,t){void 0===t&&(t="/");let{pathname:n,search:r="",hash:a=""}="string"===typeof e?v(e):e,o=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach((e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)})),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:o,search:W(r),hash:H(a)}}(a,o),c=i&&"/"!==i&&i.endsWith("/"),f=(l||"."===i)&&n.endsWith("/");return s.pathname.endsWith("/")||!c&&!f||(s.pathname+="/"),s}const B=e=>e.join("/").replace(/\/\/+/g,"/"),$=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),W=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",H=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";Error;class V{constructor(e,t,n,r){void 0===r&&(r=!1),this.status=e,this.statusText=t||"",this.internal=r,n instanceof Error?(this.data=n.toString(),this.error=n):this.data=n}}function Q(e){return null!=e&&"number"===typeof e.status&&"string"===typeof e.statusText&&"boolean"===typeof e.internal&&"data"in e}const K=["post","put","patch","delete"],q=new Set(K),Y=["get",...K],J=new Set(Y),X=new Set([301,302,303,307,308]),G=new Set([307,308]),Z={state:"idle",location:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},ee={state:"idle",data:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},te={state:"unblocked",proceed:void 0,reset:void 0,location:void 0},ne=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,re=e=>({hasErrorBoundary:Boolean(e.hasErrorBoundary)}),ae="remix-router-transitions";function oe(e){const t=e.window?e.window:"undefined"!==typeof window?window:void 0,n="undefined"!==typeof t&&"undefined"!==typeof t.document&&"undefined"!==typeof t.document.createElement,a=!n;let o;if(d(e.routes.length>0,"You must provide a non-empty routes array to createRouter"),e.mapRouteProperties)o=e.mapRouteProperties;else if(e.detectErrorBoundary){let t=e.detectErrorBoundary;o=e=>({hasErrorBoundary:t(e)})}else o=re;let l,i,s,c={},p=w(e.routes,o,void 0,c),m=e.basename||"/",v=e.dataStrategy||me,g=e.patchRoutesOnNavigation,b=u({v7_fetcherPersist:!1,v7_normalizeFormMethod:!1,v7_partialHydration:!1,v7_prependBasename:!1,v7_relativeSplatPath:!1,v7_skipActionErrorRevalidation:!1},e.future),S=null,E=new Set,C=null,_=null,j=null,P=null!=e.hydrationData,D=x(p,e.history.location,m),N=!1,R=null;if(null==D&&!g){let t=De(404,{pathname:e.history.location.pathname}),{matches:n,route:r}=Pe(p);D=n,R={[r.id]:t}}if(D&&!e.hydrationData){ut(D,p,e.history.location.pathname).active&&(D=null)}if(D)if(D.some((e=>e.route.lazy)))i=!1;else if(D.some((e=>e.route.loader)))if(b.v7_partialHydration){let t=e.hydrationData?e.hydrationData.loaderData:null,n=e.hydrationData?e.hydrationData.errors:null;if(n){let e=D.findIndex((e=>void 0!==n[e.route.id]));i=D.slice(0,e+1).every((e=>!ce(e.route,t,n)))}else i=D.every((e=>!ce(e.route,t,n)))}else i=null!=e.hydrationData;else i=!0;else if(i=!1,D=[],b.v7_partialHydration){let t=ut(null,p,e.history.location.pathname);t.active&&t.matches&&(N=!0,D=t.matches)}let T,L,I={historyAction:e.history.action,location:e.history.location,matches:D,initialized:i,navigation:Z,restoreScrollPosition:null==e.hydrationData&&null,preventScrollReset:!1,revalidation:"idle",loaderData:e.hydrationData&&e.hydrationData.loaderData||{},actionData:e.hydrationData&&e.hydrationData.actionData||null,errors:e.hydrationData&&e.hydrationData.errors||R,fetchers:new Map,blockers:new Map},O=r.Pop,z=!1,M=!1,F=new Map,U=null,B=!1,$=!1,W=[],H=new Set,V=new Map,K=0,q=-1,Y=new Map,J=new Set,X=new Map,oe=new Map,se=new Set,de=new Map,fe=new Map;function he(e,t){void 0===t&&(t={}),I=u({},I,e);let n=[],r=[];b.v7_fetcherPersist&&I.fetchers.forEach(((e,t)=>{"idle"===e.state&&(se.has(t)?r.push(t):n.push(t))})),se.forEach((e=>{I.fetchers.has(e)||V.has(e)||r.push(e)})),[...E].forEach((e=>e(I,{deletedFetchers:r,viewTransitionOpts:t.viewTransitionOpts,flushSync:!0===t.flushSync}))),b.v7_fetcherPersist?(n.forEach((e=>I.fetchers.delete(e))),r.forEach((e=>Xe(e)))):r.forEach((e=>se.delete(e)))}function xe(t,n,a){var o,i;let s,{flushSync:c}=void 0===a?{}:a,d=null!=I.actionData&&null!=I.navigation.formMethod&&Fe(I.navigation.formMethod)&&"loading"===I.navigation.state&&!0!==(null==(o=t.state)?void 0:o._isRedirect);s=n.actionData?Object.keys(n.actionData).length>0?n.actionData:null:d?I.actionData:null;let f=n.loaderData?Ce(I.loaderData,n.loaderData,n.matches||[],n.errors):I.loaderData,h=I.blockers;h.size>0&&(h=new Map(h),h.forEach(((e,t)=>h.set(t,te))));let m,v=!0===z||null!=I.navigation.formMethod&&Fe(I.navigation.formMethod)&&!0!==(null==(i=t.state)?void 0:i._isRedirect);if(l&&(p=l,l=void 0),B||O===r.Pop||(O===r.Push?e.history.push(t,t.state):O===r.Replace&&e.history.replace(t,t.state)),O===r.Pop){let e=F.get(I.location.pathname);e&&e.has(t.pathname)?m={currentLocation:I.location,nextLocation:t}:F.has(t.pathname)&&(m={currentLocation:t,nextLocation:I.location})}else if(M){let e=F.get(I.location.pathname);e?e.add(t.pathname):(e=new Set([t.pathname]),F.set(I.location.pathname,e)),m={currentLocation:I.location,nextLocation:t}}he(u({},n,{actionData:s,loaderData:f,historyAction:O,location:t,initialized:!0,navigation:Z,revalidation:"idle",restoreScrollPosition:st(t,n.matches||I.matches),preventScrollReset:v,blockers:h}),{viewTransitionOpts:m,flushSync:!0===c}),O=r.Pop,z=!1,M=!1,B=!1,$=!1,W=[]}async function ke(t,n,a){T&&T.abort(),T=null,O=t,B=!0===(a&&a.startUninterruptedRevalidation),function(e,t){if(C&&j){let n=it(e,t);C[n]=j()}}(I.location,I.matches),z=!0===(a&&a.preventScrollReset),M=!0===(a&&a.enableViewTransition);let o=l||p,i=a&&a.overrideNavigation,s=null!=a&&a.initialHydration&&I.matches&&I.matches.length>0&&!N?I.matches:x(o,n,m),c=!0===(a&&a.flushSync);if(s&&I.initialized&&!$&&function(e,t){if(e.pathname!==t.pathname||e.search!==t.search)return!1;if(""===e.hash)return""!==t.hash;if(e.hash===t.hash)return!0;if(""!==t.hash)return!0;return!1}(I.location,n)&&!(a&&a.submission&&Fe(a.submission.formMethod)))return void xe(n,{matches:s},{flushSync:c});let d=ut(s,o,n.pathname);if(d.active&&d.matches&&(s=d.matches),!s){let{error:e,notFoundMatches:t,route:r}=ot(n.pathname);return void xe(n,{matches:t,loaderData:{},errors:{[r.id]:e}},{flushSync:c})}T=new AbortController;let f,h=we(e.history,n,T.signal,a&&a.submission);if(a&&a.pendingError)f=[je(s).route.id,{type:y.error,error:a.pendingError}];else if(a&&a.submission&&Fe(a.submission.formMethod)){let t=await async function(e,t,n,a,o,l){void 0===l&&(l={});Me();let i,s=function(e,t){let n={state:"submitting",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text};return n}(t,n);if(he({navigation:s},{flushSync:!0===l.flushSync}),o){let n=await ct(a,t.pathname,e.signal);if("aborted"===n.type)return{shortCircuited:!0};if("error"===n.type){let e=je(n.partialMatches).route.id;return{matches:n.partialMatches,pendingActionResult:[e,{type:y.error,error:n.error}]}}if(!n.matches){let{notFoundMatches:e,error:n,route:r}=ot(t.pathname);return{matches:e,pendingActionResult:[r.id,{type:y.error,error:n}]}}a=n.matches}let u=He(a,t);if(u.route.action||u.route.lazy){if(i=(await Ae("action",I,e,[u],a,null))[u.route.id],e.signal.aborted)return{shortCircuited:!0}}else i={type:y.error,error:De(405,{method:e.method,pathname:t.pathname,routeId:u.route.id})};if(Oe(i)){let t;if(l&&null!=l.replace)t=l.replace;else{t=be(i.response.headers.get("Location"),new URL(e.url),m)===I.location.pathname+I.location.search}return await Re(e,i,!0,{submission:n,replace:t}),{shortCircuited:!0}}if(Le(i))throw De(400,{type:"defer-action"});if(Ie(i)){let e=je(a,u.route.id);return!0!==(l&&l.replace)&&(O=r.Push),{matches:a,pendingActionResult:[e.route.id,i]}}return{matches:a,pendingActionResult:[u.route.id,i]}}(h,n,a.submission,s,d.active,{replace:a.replace,flushSync:c});if(t.shortCircuited)return;if(t.pendingActionResult){let[e,r]=t.pendingActionResult;if(Ie(r)&&Q(r.error)&&404===r.error.status)return T=null,void xe(n,{matches:t.matches,loaderData:{},errors:{[e]:r.error}})}s=t.matches||s,f=t.pendingActionResult,i=Qe(n,a.submission),c=!1,d.active=!1,h=we(e.history,h.url,h.signal)}let{shortCircuited:v,matches:g,loaderData:w,errors:k}=await async function(t,n,r,a,o,i,s,c,d,f,h){let v=o||Qe(n,i),g=i||s||Ve(v),y=!B&&(!b.v7_partialHydration||!d);if(a){if(y){let e=Se(h);he(u({navigation:v},void 0!==e?{actionData:e}:{}),{flushSync:f})}let e=await ct(r,n.pathname,t.signal);if("aborted"===e.type)return{shortCircuited:!0};if("error"===e.type){let t=je(e.partialMatches).route.id;return{matches:e.partialMatches,loaderData:{},errors:{[t]:e.error}}}if(!e.matches){let{error:e,notFoundMatches:t,route:r}=ot(n.pathname);return{matches:t,loaderData:{},errors:{[r.id]:e}}}r=e.matches}let w=l||p,[x,k]=ue(e.history,I,r,g,n,b.v7_partialHydration&&!0===d,b.v7_skipActionErrorRevalidation,$,W,H,se,X,J,w,m,h);if(lt((e=>!(r&&r.some((t=>t.route.id===e)))||x&&x.some((t=>t.route.id===e)))),q=++K,0===x.length&&0===k.length){let e=et();return xe(n,u({matches:r,loaderData:{},errors:h&&Ie(h[1])?{[h[0]]:h[1].error}:null},_e(h),e?{fetchers:new Map(I.fetchers)}:{}),{flushSync:f}),{shortCircuited:!0}}if(y){let e={};if(!a){e.navigation=v;let t=Se(h);void 0!==t&&(e.actionData=t)}k.length>0&&(e.fetchers=function(e){return e.forEach((e=>{let t=I.fetchers.get(e.key),n=Ke(void 0,t?t.data:void 0);I.fetchers.set(e.key,n)})),new Map(I.fetchers)}(k)),he(e,{flushSync:f})}k.forEach((e=>{Ge(e.key),e.controller&&V.set(e.key,e.controller)}));let S=()=>k.forEach((e=>Ge(e.key)));T&&T.signal.addEventListener("abort",S);let{loaderResults:E,fetcherResults:C}=await ze(I,r,x,k,t);if(t.signal.aborted)return{shortCircuited:!0};T&&T.signal.removeEventListener("abort",S);k.forEach((e=>V.delete(e.key)));let _=Ne(E);if(_)return await Re(t,_.result,!0,{replace:c}),{shortCircuited:!0};if(_=Ne(C),_)return J.add(_.key),await Re(t,_.result,!0,{replace:c}),{shortCircuited:!0};let{loaderData:j,errors:P}=Ee(I,r,E,h,k,C,de);de.forEach(((e,t)=>{e.subscribe((n=>{(n||e.done)&&de.delete(t)}))})),b.v7_partialHydration&&d&&I.errors&&(P=u({},I.errors,P));let D=et(),N=tt(q),R=D||N||k.length>0;return u({matches:r,loaderData:j,errors:P},R?{fetchers:new Map(I.fetchers)}:{})}(h,n,s,d.active,i,a&&a.submission,a&&a.fetcherSubmission,a&&a.replace,a&&!0===a.initialHydration,c,f);v||(T=null,xe(n,u({matches:g||s},_e(f),{loaderData:w,errors:k})))}function Se(e){return e&&!Ie(e[1])?{[e[0]]:e[1].data}:I.actionData?0===Object.keys(I.actionData).length?null:I.actionData:void 0}async function Re(a,o,l,i){let{submission:s,fetcherSubmission:c,preventScrollReset:f,replace:p}=void 0===i?{}:i;o.response.headers.has("X-Remix-Revalidate")&&($=!0);let v=o.response.headers.get("Location");d(v,"Expected a Location header on the redirect Response"),v=be(v,new URL(a.url),m);let g=h(I.location,v,{_isRedirect:!0});if(n){let n=!1;if(o.response.headers.has("X-Remix-Reload-Document"))n=!0;else if(ne.test(v)){const r=e.history.createURL(v);n=r.origin!==t.location.origin||null==A(r.pathname,m)}if(n)return void(p?t.location.replace(v):t.location.assign(v))}T=null;let y=!0===p||o.response.headers.has("X-Remix-Replace")?r.Replace:r.Push,{formMethod:b,formAction:w,formEncType:x}=I.navigation;!s&&!c&&b&&w&&x&&(s=Ve(I.navigation));let k=s||c;if(G.has(o.response.status)&&k&&Fe(k.formMethod))await ke(y,g,{submission:u({},k,{formAction:v}),preventScrollReset:f||z,enableViewTransition:l?M:void 0});else{let e=Qe(g,s);await ke(y,g,{overrideNavigation:e,fetcherSubmission:c,preventScrollReset:f||z,enableViewTransition:l?M:void 0})}}async function Ae(e,t,n,r,a,l){let i,s={};try{i=await ve(v,e,t,n,r,a,l,c,o)}catch(u){return r.forEach((e=>{s[e.route.id]={type:y.error,error:u}})),s}for(let[o,c]of Object.entries(i))if(Te(c)){let e=c.result;s[o]={type:y.redirect,response:ye(e,n,o,a,m,b.v7_relativeSplatPath)}}else s[o]=await ge(c);return s}async function ze(t,n,r,a,o){let l=t.matches,i=Ae("loader",t,o,r,n,null),s=Promise.all(a.map((async n=>{if(n.matches&&n.match&&n.controller){let r=(await Ae("loader",t,we(e.history,n.path,n.controller.signal),[n.match],n.matches,n.key))[n.match.route.id];return{[n.key]:r}}return Promise.resolve({[n.key]:{type:y.error,error:De(404,{pathname:n.path})}})}))),u=await i,c=(await s).reduce(((e,t)=>Object.assign(e,t)),{});return await Promise.all([Ue(n,u,o.signal,l,t.loaderData),Be(n,c,a)]),{loaderResults:u,fetcherResults:c}}function Me(){$=!0,W.push(...lt()),X.forEach(((e,t)=>{V.has(t)&&H.add(t),Ge(t)}))}function We(e,t,n){void 0===n&&(n={}),I.fetchers.set(e,t),he({fetchers:new Map(I.fetchers)},{flushSync:!0===(n&&n.flushSync)})}function Ye(e,t,n,r){void 0===r&&(r={});let a=je(I.matches,t);Xe(e),he({errors:{[a.route.id]:n},fetchers:new Map(I.fetchers)},{flushSync:!0===(r&&r.flushSync)})}function Je(e){return oe.set(e,(oe.get(e)||0)+1),se.has(e)&&se.delete(e),I.fetchers.get(e)||ee}function Xe(e){let t=I.fetchers.get(e);!V.has(e)||t&&"loading"===t.state&&Y.has(e)||Ge(e),X.delete(e),Y.delete(e),J.delete(e),b.v7_fetcherPersist&&se.delete(e),H.delete(e),I.fetchers.delete(e)}function Ge(e){let t=V.get(e);t&&(t.abort(),V.delete(e))}function Ze(e){for(let t of e){let e=qe(Je(t).data);I.fetchers.set(t,e)}}function et(){let e=[],t=!1;for(let n of J){let r=I.fetchers.get(n);d(r,"Expected fetcher: "+n),"loading"===r.state&&(J.delete(n),e.push(n),t=!0)}return Ze(e),t}function tt(e){let t=[];for(let[n,r]of Y)if(r<e){let e=I.fetchers.get(n);d(e,"Expected fetcher: "+n),"loading"===e.state&&(Ge(n),Y.delete(n),t.push(n))}return Ze(t),t.length>0}function nt(e){I.blockers.delete(e),fe.delete(e)}function rt(e,t){let n=I.blockers.get(e)||te;d("unblocked"===n.state&&"blocked"===t.state||"blocked"===n.state&&"blocked"===t.state||"blocked"===n.state&&"proceeding"===t.state||"blocked"===n.state&&"unblocked"===t.state||"proceeding"===n.state&&"unblocked"===t.state,"Invalid blocker state transition: "+n.state+" -> "+t.state);let r=new Map(I.blockers);r.set(e,t),he({blockers:r})}function at(e){let{currentLocation:t,nextLocation:n,historyAction:r}=e;if(0===fe.size)return;fe.size>1&&f(!1,"A router only supports one blocker at a time");let a=Array.from(fe.entries()),[o,l]=a[a.length-1],i=I.blockers.get(o);return i&&"proceeding"===i.state?void 0:l({currentLocation:t,nextLocation:n,historyAction:r})?o:void 0}function ot(e){let t=De(404,{pathname:e}),n=l||p,{matches:r,route:a}=Pe(n);return lt(),{notFoundMatches:r,route:a,error:t}}function lt(e){let t=[];return de.forEach(((n,r)=>{e&&!e(r)||(n.cancel(),t.push(r),de.delete(r))})),t}function it(e,t){if(_){return _(e,t.map((e=>function(e,t){let{route:n,pathname:r,params:a}=e;return{id:n.id,pathname:r,params:a,data:t[n.id],handle:n.handle}}(e,I.loaderData))))||e.key}return e.key}function st(e,t){if(C){let n=it(e,t),r=C[n];if("number"===typeof r)return r}return null}function ut(e,t,n){if(g){if(!e){return{active:!0,matches:k(t,n,m,!0)||[]}}if(Object.keys(e[0].params).length>0){return{active:!0,matches:k(t,n,m,!0)}}}return{active:!1,matches:null}}async function ct(e,t,n,r){if(!g)return{type:"success",matches:e};let a=e;for(;;){let e=null==l,s=l||p,u=c;try{await g({signal:n,path:t,matches:a,fetcherKey:r,patch:(e,t)=>{n.aborted||pe(e,t,s,u,o)}})}catch(i){return{type:"error",error:i,partialMatches:a}}finally{e&&!n.aborted&&(p=[...p])}if(n.aborted)return{type:"aborted"};let d=x(s,t,m);if(d)return{type:"success",matches:d};let f=k(s,t,m,!0);if(!f||a.length===f.length&&a.every(((e,t)=>e.route.id===f[t].route.id)))return{type:"success",matches:null};a=f}}return s={get basename(){return m},get future(){return b},get state(){return I},get routes(){return p},get window(){return t},initialize:function(){if(S=e.history.listen((t=>{let{action:n,location:r,delta:a}=t;if(L)return L(),void(L=void 0);f(0===fe.size||null!=a,"You are trying to use a blocker on a POP navigation to a location that was not created by @remix-run/router. This will fail silently in production. This can happen if you are navigating outside the router via `window.history.pushState`/`window.location.hash` instead of using router navigation APIs.  This can also happen if you are using createHashRouter and the user manually changes the URL.");let o=at({currentLocation:I.location,nextLocation:r,historyAction:n});if(o&&null!=a){let t=new Promise((e=>{L=e}));return e.history.go(-1*a),void rt(o,{state:"blocked",location:r,proceed(){rt(o,{state:"proceeding",proceed:void 0,reset:void 0,location:r}),t.then((()=>e.history.go(a)))},reset(){let e=new Map(I.blockers);e.set(o,te),he({blockers:e})}})}return ke(n,r)})),n){!function(e,t){try{let n=e.sessionStorage.getItem(ae);if(n){let e=JSON.parse(n);for(let[n,r]of Object.entries(e||{}))r&&Array.isArray(r)&&t.set(n,new Set(r||[]))}}catch(n){}}(t,F);let e=()=>function(e,t){if(t.size>0){let r={};for(let[e,n]of t)r[e]=[...n];try{e.sessionStorage.setItem(ae,JSON.stringify(r))}catch(n){f(!1,"Failed to save applied view transitions in sessionStorage ("+n+").")}}}(t,F);t.addEventListener("pagehide",e),U=()=>t.removeEventListener("pagehide",e)}return I.initialized||ke(r.Pop,I.location,{initialHydration:!0}),s},subscribe:function(e){return E.add(e),()=>E.delete(e)},enableScrollRestoration:function(e,t,n){if(C=e,j=t,_=n||null,!P&&I.navigation===Z){P=!0;let e=st(I.location,I.matches);null!=e&&he({restoreScrollPosition:e})}return()=>{C=null,j=null,_=null}},navigate:async function t(n,a){if("number"===typeof n)return void e.history.go(n);let o=le(I.location,I.matches,m,b.v7_prependBasename,n,b.v7_relativeSplatPath,null==a?void 0:a.fromRouteId,null==a?void 0:a.relative),{path:l,submission:i,error:s}=ie(b.v7_normalizeFormMethod,!1,o,a),c=I.location,d=h(I.location,l,a&&a.state);d=u({},d,e.history.encodeLocation(d));let f=a&&null!=a.replace?a.replace:void 0,p=r.Push;!0===f?p=r.Replace:!1===f||null!=i&&Fe(i.formMethod)&&i.formAction===I.location.pathname+I.location.search&&(p=r.Replace);let v=a&&"preventScrollReset"in a?!0===a.preventScrollReset:void 0,g=!0===(a&&a.flushSync),y=at({currentLocation:c,nextLocation:d,historyAction:p});if(!y)return await ke(p,d,{submission:i,pendingError:s,preventScrollReset:v,replace:a&&a.replace,enableViewTransition:a&&a.viewTransition,flushSync:g});rt(y,{state:"blocked",location:d,proceed(){rt(y,{state:"proceeding",proceed:void 0,reset:void 0,location:d}),t(n,a)},reset(){let e=new Map(I.blockers);e.set(y,te),he({blockers:e})}})},fetch:function(t,n,r,o){if(a)throw new Error("router.fetch() was called during the server render, but it shouldn't be. You are likely calling a useFetcher() method in the body of your component. Try moving it to a useEffect or a callback.");Ge(t);let i=!0===(o&&o.flushSync),s=l||p,u=le(I.location,I.matches,m,b.v7_prependBasename,r,b.v7_relativeSplatPath,n,null==o?void 0:o.relative),c=x(s,u,m),f=ut(c,s,u);if(f.active&&f.matches&&(c=f.matches),!c)return void Ye(t,n,De(404,{pathname:u}),{flushSync:i});let{path:h,submission:v,error:g}=ie(b.v7_normalizeFormMethod,!0,u,o);if(g)return void Ye(t,n,g,{flushSync:i});let y=He(c,h),w=!0===(o&&o.preventScrollReset);v&&Fe(v.formMethod)?async function(t,n,r,a,o,i,s,u,c){function f(e){if(!e.route.action&&!e.route.lazy){let e=De(405,{method:c.formMethod,pathname:r,routeId:n});return Ye(t,n,e,{flushSync:s}),!0}return!1}if(Me(),X.delete(t),!i&&f(a))return;let h=I.fetchers.get(t);We(t,function(e,t){let n={state:"submitting",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t?t.data:void 0};return n}(c,h),{flushSync:s});let v=new AbortController,g=we(e.history,r,v.signal,c);if(i){let e=await ct(o,new URL(g.url).pathname,g.signal,t);if("aborted"===e.type)return;if("error"===e.type)return void Ye(t,n,e.error,{flushSync:s});if(!e.matches)return void Ye(t,n,De(404,{pathname:r}),{flushSync:s});if(f(a=He(o=e.matches,r)))return}V.set(t,v);let y=K,w=await Ae("action",I,g,[a],o,t),k=w[a.route.id];if(g.signal.aborted)return void(V.get(t)===v&&V.delete(t));if(b.v7_fetcherPersist&&se.has(t)){if(Oe(k)||Ie(k))return void We(t,qe(void 0))}else{if(Oe(k))return V.delete(t),q>y?void We(t,qe(void 0)):(J.add(t),We(t,Ke(c)),Re(g,k,!1,{fetcherSubmission:c,preventScrollReset:u}));if(Ie(k))return void Ye(t,n,k.error)}if(Le(k))throw De(400,{type:"defer-action"});let S=I.navigation.location||I.location,E=we(e.history,S,v.signal),C=l||p,_="idle"!==I.navigation.state?x(C,I.navigation.location,m):I.matches;d(_,"Didn't find any matches after fetcher action");let j=++K;Y.set(t,j);let P=Ke(c,k.data);I.fetchers.set(t,P);let[D,N]=ue(e.history,I,_,c,S,!1,b.v7_skipActionErrorRevalidation,$,W,H,se,X,J,C,m,[a.route.id,k]);N.filter((e=>e.key!==t)).forEach((e=>{let t=e.key,n=I.fetchers.get(t),r=Ke(void 0,n?n.data:void 0);I.fetchers.set(t,r),Ge(t),e.controller&&V.set(t,e.controller)})),he({fetchers:new Map(I.fetchers)});let R=()=>N.forEach((e=>Ge(e.key)));v.signal.addEventListener("abort",R);let{loaderResults:L,fetcherResults:A}=await ze(I,_,D,N,E);if(v.signal.aborted)return;v.signal.removeEventListener("abort",R),Y.delete(t),V.delete(t),N.forEach((e=>V.delete(e.key)));let z=Ne(L);if(z)return Re(E,z.result,!1,{preventScrollReset:u});if(z=Ne(A),z)return J.add(z.key),Re(E,z.result,!1,{preventScrollReset:u});let{loaderData:M,errors:F}=Ee(I,_,L,void 0,N,A,de);if(I.fetchers.has(t)){let e=qe(k.data);I.fetchers.set(t,e)}tt(j),"loading"===I.navigation.state&&j>q?(d(O,"Expected pending action"),T&&T.abort(),xe(I.navigation.location,{matches:_,loaderData:M,errors:F,fetchers:new Map(I.fetchers)})):(he({errors:F,loaderData:Ce(I.loaderData,M,_,F),fetchers:new Map(I.fetchers)}),$=!1)}(t,n,h,y,c,f.active,i,w,v):(X.set(t,{routeId:n,path:h}),async function(t,n,r,a,o,l,i,s,u){let c=I.fetchers.get(t);We(t,Ke(u,c?c.data:void 0),{flushSync:i});let f=new AbortController,p=we(e.history,r,f.signal);if(l){let e=await ct(o,new URL(p.url).pathname,p.signal,t);if("aborted"===e.type)return;if("error"===e.type)return void Ye(t,n,e.error,{flushSync:i});if(!e.matches)return void Ye(t,n,De(404,{pathname:r}),{flushSync:i});a=He(o=e.matches,r)}V.set(t,f);let h=K,m=await Ae("loader",I,p,[a],o,t),v=m[a.route.id];Le(v)&&(v=await $e(v,p.signal,!0)||v);V.get(t)===f&&V.delete(t);if(p.signal.aborted)return;if(se.has(t))return void We(t,qe(void 0));if(Oe(v))return q>h?void We(t,qe(void 0)):(J.add(t),void await Re(p,v,!1,{preventScrollReset:s}));if(Ie(v))return void Ye(t,n,v.error);d(!Le(v),"Unhandled fetcher deferred data"),We(t,qe(v.data))}(t,n,h,y,c,f.active,i,w,v))},revalidate:function(){Me(),he({revalidation:"loading"}),"submitting"!==I.navigation.state&&("idle"!==I.navigation.state?ke(O||I.historyAction,I.navigation.location,{overrideNavigation:I.navigation,enableViewTransition:!0===M}):ke(I.historyAction,I.location,{startUninterruptedRevalidation:!0}))},createHref:t=>e.history.createHref(t),encodeLocation:t=>e.history.encodeLocation(t),getFetcher:Je,deleteFetcher:function(e){let t=(oe.get(e)||0)-1;t<=0?(oe.delete(e),se.add(e),b.v7_fetcherPersist||Xe(e)):oe.set(e,t),he({fetchers:new Map(I.fetchers)})},dispose:function(){S&&S(),U&&U(),E.clear(),T&&T.abort(),I.fetchers.forEach(((e,t)=>Xe(t))),I.blockers.forEach(((e,t)=>nt(t)))},getBlocker:function(e,t){let n=I.blockers.get(e)||te;return fe.get(e)!==t&&fe.set(e,t),n},deleteBlocker:nt,patchRoutes:function(e,t){let n=null==l;pe(e,t,l||p,c,o),n&&(p=[...p],he({}))},_internalFetchControllers:V,_internalActiveDeferreds:de,_internalSetRoutes:function(e){c={},l=w(e,o,void 0,c)}},s}Symbol("deferred");function le(e,t,n,r,a,o,l,i){let s,u;if(l){s=[];for(let e of t)if(s.push(e),e.route.id===l){u=e;break}}else s=t,u=t[t.length-1];let c=U(a||".",F(s,o),A(e.pathname,n)||e.pathname,"path"===i);if(null==a&&(c.search=e.search,c.hash=e.hash),(null==a||""===a||"."===a)&&u){let e=We(c.search);if(u.route.index&&!e)c.search=c.search?c.search.replace(/^\?/,"?index&"):"?index";else if(!u.route.index&&e){let e=new URLSearchParams(c.search),t=e.getAll("index");e.delete("index"),t.filter((e=>e)).forEach((t=>e.append("index",t)));let n=e.toString();c.search=n?"?"+n:""}}return r&&"/"!==n&&(c.pathname="/"===c.pathname?n:B([n,c.pathname])),m(c)}function ie(e,t,n,r){if(!r||!function(e){return null!=e&&("formData"in e&&null!=e.formData||"body"in e&&void 0!==e.body)}(r))return{path:n};if(r.formMethod&&!Me(r.formMethod))return{path:n,error:De(405,{method:r.formMethod})};let a,o,l=()=>({path:n,error:De(400,{type:"invalid-body"})}),i=r.formMethod||"get",s=e?i.toUpperCase():i.toLowerCase(),u=Re(n);if(void 0!==r.body){if("text/plain"===r.formEncType){if(!Fe(s))return l();let e="string"===typeof r.body?r.body:r.body instanceof FormData||r.body instanceof URLSearchParams?Array.from(r.body.entries()).reduce(((e,t)=>{let[n,r]=t;return""+e+n+"="+r+"\n"}),""):String(r.body);return{path:n,submission:{formMethod:s,formAction:u,formEncType:r.formEncType,formData:void 0,json:void 0,text:e}}}if("application/json"===r.formEncType){if(!Fe(s))return l();try{let e="string"===typeof r.body?JSON.parse(r.body):r.body;return{path:n,submission:{formMethod:s,formAction:u,formEncType:r.formEncType,formData:void 0,json:e,text:void 0}}}catch(p){return l()}}}if(d("function"===typeof FormData,"FormData is not available in this environment"),r.formData)a=xe(r.formData),o=r.formData;else if(r.body instanceof FormData)a=xe(r.body),o=r.body;else if(r.body instanceof URLSearchParams)a=r.body,o=ke(a);else if(null==r.body)a=new URLSearchParams,o=new FormData;else try{a=new URLSearchParams(r.body),o=ke(a)}catch(p){return l()}let c={formMethod:s,formAction:u,formEncType:r&&r.formEncType||"application/x-www-form-urlencoded",formData:o,json:void 0,text:void 0};if(Fe(c.formMethod))return{path:n,submission:c};let f=v(n);return t&&f.search&&We(f.search)&&a.append("index",""),f.search="?"+a,{path:m(f),submission:c}}function se(e,t,n){void 0===n&&(n=!1);let r=e.findIndex((e=>e.route.id===t));return r>=0?e.slice(0,n?r+1:r):e}function ue(e,t,n,r,a,o,l,i,s,c,d,f,p,h,m,v){let g=v?Ie(v[1])?v[1].error:v[1].data:void 0,y=e.createURL(t.location),b=e.createURL(a),w=n;o&&t.errors?w=se(n,Object.keys(t.errors)[0],!0):v&&Ie(v[1])&&(w=se(n,v[0]));let k=v?v[1].statusCode:void 0,S=l&&k&&k>=400,E=w.filter(((e,n)=>{let{route:a}=e;if(a.lazy)return!0;if(null==a.loader)return!1;if(o)return ce(a,t.loaderData,t.errors);if(function(e,t,n){let r=!t||n.route.id!==t.route.id,a=void 0===e[n.route.id];return r||a}(t.loaderData,t.matches[n],e)||s.some((t=>t===e.route.id)))return!0;let l=t.matches[n],c=e;return fe(e,u({currentUrl:y,currentParams:l.params,nextUrl:b,nextParams:c.params},r,{actionResult:g,actionStatus:k,defaultShouldRevalidate:!S&&(i||y.pathname+y.search===b.pathname+b.search||y.search!==b.search||de(l,c))}))})),C=[];return f.forEach(((e,a)=>{if(o||!n.some((t=>t.route.id===e.routeId))||d.has(a))return;let l=x(h,e.path,m);if(!l)return void C.push({key:a,routeId:e.routeId,path:e.path,matches:null,match:null,controller:null});let s=t.fetchers.get(a),f=He(l,e.path),v=!1;p.has(a)?v=!1:c.has(a)?(c.delete(a),v=!0):v=s&&"idle"!==s.state&&void 0===s.data?i:fe(f,u({currentUrl:y,currentParams:t.matches[t.matches.length-1].params,nextUrl:b,nextParams:n[n.length-1].params},r,{actionResult:g,actionStatus:k,defaultShouldRevalidate:!S&&i})),v&&C.push({key:a,routeId:e.routeId,path:e.path,matches:l,match:f,controller:new AbortController})})),[E,C]}function ce(e,t,n){if(e.lazy)return!0;if(!e.loader)return!1;let r=null!=t&&void 0!==t[e.id],a=null!=n&&void 0!==n[e.id];return!(!r&&a)&&("function"===typeof e.loader&&!0===e.loader.hydrate||!r&&!a)}function de(e,t){let n=e.route.path;return e.pathname!==t.pathname||null!=n&&n.endsWith("*")&&e.params["*"]!==t.params["*"]}function fe(e,t){if(e.route.shouldRevalidate){let n=e.route.shouldRevalidate(t);if("boolean"===typeof n)return n}return t.defaultShouldRevalidate}function pe(e,t,n,r,a){var o;let l;if(e){let t=r[e];d(t,"No route found to patch children into: routeId = "+e),t.children||(t.children=[]),l=t.children}else l=n;let i=w(t.filter((e=>!l.some((t=>he(e,t))))),a,[e||"_","patch",String((null==(o=l)?void 0:o.length)||"0")],r);l.push(...i)}function he(e,t){return"id"in e&&"id"in t&&e.id===t.id||e.index===t.index&&e.path===t.path&&e.caseSensitive===t.caseSensitive&&(!(e.children&&0!==e.children.length||t.children&&0!==t.children.length)||e.children.every(((e,n)=>{var r;return null==(r=t.children)?void 0:r.some((t=>he(e,t)))})))}async function me(e){let{matches:t}=e,n=t.filter((e=>e.shouldLoad));return(await Promise.all(n.map((e=>e.resolve())))).reduce(((e,t,r)=>Object.assign(e,{[n[r].route.id]:t})),{})}async function ve(e,t,n,r,a,o,l,i,s,c){let p=o.map((e=>e.route.lazy?async function(e,t,n){if(!e.lazy)return;let r=await e.lazy();if(!e.lazy)return;let a=n[e.id];d(a,"No route found in manifest");let o={};for(let l in r){let e=void 0!==a[l]&&"hasErrorBoundary"!==l;f(!e,'Route "'+a.id+'" has a static property "'+l+'" defined but its lazy function is also returning a value for this property. The lazy route property "'+l+'" will be ignored.'),e||b.has(l)||(o[l]=r[l])}Object.assign(a,o),Object.assign(a,u({},t(a),{lazy:void 0}))}(e.route,s,i):void 0)),h=o.map(((e,n)=>{let o=p[n],l=a.some((t=>t.route.id===e.route.id));return u({},e,{shouldLoad:l,resolve:async n=>(n&&"GET"===r.method&&(e.route.lazy||e.route.loader)&&(l=!0),l?async function(e,t,n,r,a,o){let l,i,s=r=>{let l,s=new Promise(((e,t)=>l=t));i=()=>l(),t.signal.addEventListener("abort",i);let u=a=>"function"!==typeof r?Promise.reject(new Error('You cannot call the handler for a route which defines a boolean "'+e+'" [routeId: '+n.route.id+"]")):r({request:t,params:n.params,context:o},...void 0!==a?[a]:[]),c=(async()=>{try{return{type:"data",result:await(a?a((e=>u(e))):u())}}catch(e){return{type:"error",result:e}}})();return Promise.race([c,s])};try{let a=n.route[e];if(r)if(a){let e,[t]=await Promise.all([s(a).catch((t=>{e=t})),r]);if(void 0!==e)throw e;l=t}else{if(await r,a=n.route[e],!a){if("action"===e){let e=new URL(t.url),r=e.pathname+e.search;throw De(405,{method:t.method,pathname:r,routeId:n.route.id})}return{type:y.data,result:void 0}}l=await s(a)}else{if(!a){let e=new URL(t.url);throw De(404,{pathname:e.pathname+e.search})}l=await s(a)}d(void 0!==l.result,"You defined "+("action"===e?"an action":"a loader")+' for route "'+n.route.id+"\" but didn't return anything from your `"+e+"` function. Please return a value or `null`.")}catch(u){return{type:y.error,result:u}}finally{i&&t.signal.removeEventListener("abort",i)}return l}(t,r,e,o,n,c):Promise.resolve({type:y.data,result:void 0}))})})),m=await e({matches:h,request:r,params:o[0].params,fetcherKey:l,context:c});try{await Promise.all(p)}catch(v){}return m}async function ge(e){let{result:t,type:n}=e;if(ze(t)){let e;try{let n=t.headers.get("Content-Type");e=n&&/\bapplication\/json\b/.test(n)?null==t.body?null:await t.json():await t.text()}catch(d){return{type:y.error,error:d}}return n===y.error?{type:y.error,error:new V(t.status,t.statusText,e),statusCode:t.status,headers:t.headers}:{type:y.data,data:e,statusCode:t.status,headers:t.headers}}var r,a,o,l,i,s,u,c;return n===y.error?Ae(t)?t.data instanceof Error?{type:y.error,error:t.data,statusCode:null==(o=t.init)?void 0:o.status,headers:null!=(l=t.init)&&l.headers?new Headers(t.init.headers):void 0}:{type:y.error,error:new V((null==(r=t.init)?void 0:r.status)||500,void 0,t.data),statusCode:Q(t)?t.status:void 0,headers:null!=(a=t.init)&&a.headers?new Headers(t.init.headers):void 0}:{type:y.error,error:t,statusCode:Q(t)?t.status:void 0}:function(e){let t=e;return t&&"object"===typeof t&&"object"===typeof t.data&&"function"===typeof t.subscribe&&"function"===typeof t.cancel&&"function"===typeof t.resolveData}(t)?{type:y.deferred,deferredData:t,statusCode:null==(i=t.init)?void 0:i.status,headers:(null==(s=t.init)?void 0:s.headers)&&new Headers(t.init.headers)}:Ae(t)?{type:y.data,data:t.data,statusCode:null==(u=t.init)?void 0:u.status,headers:null!=(c=t.init)&&c.headers?new Headers(t.init.headers):void 0}:{type:y.data,data:t}}function ye(e,t,n,r,a,o){let l=e.headers.get("Location");if(d(l,"Redirects returned/thrown from loaders/actions must have a Location header"),!ne.test(l)){let i=r.slice(0,r.findIndex((e=>e.route.id===n))+1);l=le(new URL(t.url),i,a,!0,l,o),e.headers.set("Location",l)}return e}function be(e,t,n){if(ne.test(e)){let r=e,a=r.startsWith("//")?new URL(t.protocol+r):new URL(r),o=null!=A(a.pathname,n);if(a.origin===t.origin&&o)return a.pathname+a.search+a.hash}return e}function we(e,t,n,r){let a=e.createURL(Re(t)).toString(),o={signal:n};if(r&&Fe(r.formMethod)){let{formMethod:e,formEncType:t}=r;o.method=e.toUpperCase(),"application/json"===t?(o.headers=new Headers({"Content-Type":t}),o.body=JSON.stringify(r.json)):"text/plain"===t?o.body=r.text:"application/x-www-form-urlencoded"===t&&r.formData?o.body=xe(r.formData):o.body=r.formData}return new Request(a,o)}function xe(e){let t=new URLSearchParams;for(let[n,r]of e.entries())t.append(n,"string"===typeof r?r:r.name);return t}function ke(e){let t=new FormData;for(let[n,r]of e.entries())t.append(n,r);return t}function Se(e,t,n,r,a){let o,l={},i=null,s=!1,u={},c=n&&Ie(n[1])?n[1].error:void 0;return e.forEach((n=>{if(!(n.route.id in t))return;let f=n.route.id,p=t[f];if(d(!Oe(p),"Cannot handle redirect results in processLoaderData"),Ie(p)){let t=p.error;if(void 0!==c&&(t=c,c=void 0),i=i||{},a)i[f]=t;else{let n=je(e,f);null==i[n.route.id]&&(i[n.route.id]=t)}l[f]=void 0,s||(s=!0,o=Q(p.error)?p.error.status:500),p.headers&&(u[f]=p.headers)}else Le(p)?(r.set(f,p.deferredData),l[f]=p.deferredData.data,null==p.statusCode||200===p.statusCode||s||(o=p.statusCode),p.headers&&(u[f]=p.headers)):(l[f]=p.data,p.statusCode&&200!==p.statusCode&&!s&&(o=p.statusCode),p.headers&&(u[f]=p.headers))})),void 0!==c&&n&&(i={[n[0]]:c},l[n[0]]=void 0),{loaderData:l,errors:i,statusCode:o||200,loaderHeaders:u}}function Ee(e,t,n,r,a,o,l){let{loaderData:i,errors:s}=Se(t,n,r,l,!1);return a.forEach((t=>{let{key:n,match:r,controller:a}=t,l=o[n];if(d(l,"Did not find corresponding fetcher result"),!a||!a.signal.aborted)if(Ie(l)){let t=je(e.matches,null==r?void 0:r.route.id);s&&s[t.route.id]||(s=u({},s,{[t.route.id]:l.error})),e.fetchers.delete(n)}else if(Oe(l))d(!1,"Unhandled fetcher revalidation redirect");else if(Le(l))d(!1,"Unhandled fetcher deferred data");else{let t=qe(l.data);e.fetchers.set(n,t)}})),{loaderData:i,errors:s}}function Ce(e,t,n,r){let a=u({},t);for(let o of n){let n=o.route.id;if(t.hasOwnProperty(n)?void 0!==t[n]&&(a[n]=t[n]):void 0!==e[n]&&o.route.loader&&(a[n]=e[n]),r&&r.hasOwnProperty(n))break}return a}function _e(e){return e?Ie(e[1])?{actionData:{}}:{actionData:{[e[0]]:e[1].data}}:{}}function je(e,t){return(t?e.slice(0,e.findIndex((e=>e.route.id===t))+1):[...e]).reverse().find((e=>!0===e.route.hasErrorBoundary))||e[0]}function Pe(e){let t=1===e.length?e[0]:e.find((e=>e.index||!e.path||"/"===e.path))||{id:"__shim-error-route__"};return{matches:[{params:{},pathname:"",pathnameBase:"",route:t}],route:t}}function De(e,t){let{pathname:n,routeId:r,method:a,type:o,message:l}=void 0===t?{}:t,i="Unknown Server Error",s="Unknown @remix-run/router error";return 400===e?(i="Bad Request",a&&n&&r?s="You made a "+a+' request to "'+n+'" but did not provide a `loader` for route "'+r+'", so there is no way to handle the request.':"defer-action"===o?s="defer() is not supported in actions":"invalid-body"===o&&(s="Unable to encode submission body")):403===e?(i="Forbidden",s='Route "'+r+'" does not match URL "'+n+'"'):404===e?(i="Not Found",s='No route matches URL "'+n+'"'):405===e&&(i="Method Not Allowed",a&&n&&r?s="You made a "+a.toUpperCase()+' request to "'+n+'" but did not provide an `action` for route "'+r+'", so there is no way to handle the request.':a&&(s='Invalid request method "'+a.toUpperCase()+'"')),new V(e||500,i,new Error(s),!0)}function Ne(e){let t=Object.entries(e);for(let n=t.length-1;n>=0;n--){let[e,r]=t[n];if(Oe(r))return{key:e,result:r}}}function Re(e){return m(u({},"string"===typeof e?v(e):e,{hash:""}))}function Te(e){return ze(e.result)&&X.has(e.result.status)}function Le(e){return e.type===y.deferred}function Ie(e){return e.type===y.error}function Oe(e){return(e&&e.type)===y.redirect}function Ae(e){return"object"===typeof e&&null!=e&&"type"in e&&"data"in e&&"init"in e&&"DataWithResponseInit"===e.type}function ze(e){return null!=e&&"number"===typeof e.status&&"string"===typeof e.statusText&&"object"===typeof e.headers&&"undefined"!==typeof e.body}function Me(e){return J.has(e.toLowerCase())}function Fe(e){return q.has(e.toLowerCase())}async function Ue(e,t,n,r,a){let o=Object.entries(t);for(let l=0;l<o.length;l++){let[i,s]=o[l],u=e.find((e=>(null==e?void 0:e.route.id)===i));if(!u)continue;let c=r.find((e=>e.route.id===u.route.id)),d=null!=c&&!de(c,u)&&void 0!==(a&&a[u.route.id]);Le(s)&&d&&await $e(s,n,!1).then((e=>{e&&(t[i]=e)}))}}async function Be(e,t,n){for(let r=0;r<n.length;r++){let{key:a,routeId:o,controller:l}=n[r],i=t[a];e.find((e=>(null==e?void 0:e.route.id)===o))&&(Le(i)&&(d(l,"Expected an AbortController for revalidating fetcher deferred result"),await $e(i,l.signal,!0).then((e=>{e&&(t[a]=e)}))))}}async function $e(e,t,n){if(void 0===n&&(n=!1),!await e.deferredData.resolveData(t)){if(n)try{return{type:y.data,data:e.deferredData.unwrappedData}}catch(r){return{type:y.error,error:r}}return{type:y.data,data:e.deferredData.data}}}function We(e){return new URLSearchParams(e).getAll("index").some((e=>""===e))}function He(e,t){let n="string"===typeof t?v(t).search:t.search;if(e[e.length-1].route.index&&We(n||""))return e[e.length-1];let r=M(e);return r[r.length-1]}function Ve(e){let{formMethod:t,formAction:n,formEncType:r,text:a,formData:o,json:l}=e;if(t&&n&&r)return null!=a?{formMethod:t,formAction:n,formEncType:r,formData:void 0,json:void 0,text:a}:null!=o?{formMethod:t,formAction:n,formEncType:r,formData:o,json:void 0,text:void 0}:void 0!==l?{formMethod:t,formAction:n,formEncType:r,formData:void 0,json:l,text:void 0}:void 0}function Qe(e,t){if(t){return{state:"loading",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}}return{state:"loading",location:e,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0}}function Ke(e,t){if(e){return{state:"loading",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t}}return{state:"loading",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:t}}function qe(e){return{state:"idle",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:e}}function Ye(){return Ye=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Ye.apply(this,arguments)}const Je=a.createContext(null);const Xe=a.createContext(null);const Ge=a.createContext(null);const Ze=a.createContext(null);const et=a.createContext({outlet:null,matches:[],isDataRoute:!1});const tt=a.createContext(null);function nt(){return null!=a.useContext(Ze)}function rt(){return nt()||d(!1),a.useContext(Ze).location}function at(e){a.useContext(Ge).static||a.useLayoutEffect(e)}function ot(){let{isDataRoute:e}=a.useContext(et);return e?function(){let{router:e}=vt(ht.UseNavigateStable),t=yt(mt.UseNavigateStable),n=a.useRef(!1);at((()=>{n.current=!0}));let r=a.useCallback((function(r,a){void 0===a&&(a={}),n.current&&("number"===typeof r?e.navigate(r):e.navigate(r,Ye({fromRouteId:t},a)))}),[e,t]);return r}():function(){nt()||d(!1);let e=a.useContext(Je),{basename:t,future:n,navigator:r}=a.useContext(Ge),{matches:o}=a.useContext(et),{pathname:l}=rt(),i=JSON.stringify(F(o,n.v7_relativeSplatPath)),s=a.useRef(!1);return at((()=>{s.current=!0})),a.useCallback((function(n,a){if(void 0===a&&(a={}),!s.current)return;if("number"===typeof n)return void r.go(n);let o=U(n,JSON.parse(i),l,"path"===a.relative);null==e&&"/"!==t&&(o.pathname="/"===o.pathname?t:B([t,o.pathname])),(a.replace?r.replace:r.push)(o,a.state,a)}),[t,r,i,l,e])}()}const lt=a.createContext(null);function it(e,t){let{relative:n}=void 0===t?{}:t,{future:r}=a.useContext(Ge),{matches:o}=a.useContext(et),{pathname:l}=rt(),i=JSON.stringify(F(o,r.v7_relativeSplatPath));return a.useMemo((()=>U(e,JSON.parse(i),l,"path"===n)),[e,i,l,n])}function st(e,t,n,o){nt()||d(!1);let{navigator:l,static:i}=a.useContext(Ge),{matches:s}=a.useContext(et),u=s[s.length-1],c=u?u.params:{},f=(u&&u.pathname,u?u.pathnameBase:"/");u&&u.route;let p,h=rt();if(t){var m;let e="string"===typeof t?v(t):t;"/"===f||(null==(m=e.pathname)?void 0:m.startsWith(f))||d(!1),p=e}else p=h;let g=p.pathname||"/",y=g;if("/"!==f){let e=f.replace(/^\//,"").split("/");y="/"+g.replace(/^\//,"").split("/").slice(e.length).join("/")}let b=!i&&n&&n.matches&&n.matches.length>0?n.matches:x(e,{pathname:y});let w=pt(b&&b.map((e=>Object.assign({},e,{params:Object.assign({},c,e.params),pathname:B([f,l.encodeLocation?l.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?f:B([f,l.encodeLocation?l.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])}))),s,n,o);return t&&w?a.createElement(Ze.Provider,{value:{location:Ye({pathname:"/",search:"",hash:"",state:null,key:"default"},p),navigationType:r.Pop}},w):w}function ut(){let e=function(){var e;let t=a.useContext(tt),n=gt(mt.UseRouteError),r=yt(mt.UseRouteError);if(void 0!==t)return t;return null==(e=n.errors)?void 0:e[r]}(),t=Q(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,r="rgba(200,200,200, 0.5)",o={padding:"0.5rem",backgroundColor:r};return a.createElement(a.Fragment,null,a.createElement("h2",null,"Unexpected Application Error!"),a.createElement("h3",{style:{fontStyle:"italic"}},t),n?a.createElement("pre",{style:o},n):null,null)}const ct=a.createElement(ut,null);class dt extends a.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?a.createElement(et.Provider,{value:this.props.routeContext},a.createElement(tt.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function ft(e){let{routeContext:t,match:n,children:r}=e,o=a.useContext(Je);return o&&o.static&&o.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=n.route.id),a.createElement(et.Provider,{value:t},r)}function pt(e,t,n,r){var o;if(void 0===t&&(t=[]),void 0===n&&(n=null),void 0===r&&(r=null),null==e){var l;if(!n)return null;if(n.errors)e=n.matches;else{if(!(null!=(l=r)&&l.v7_partialHydration&&0===t.length&&!n.initialized&&n.matches.length>0))return null;e=n.matches}}let i=e,s=null==(o=n)?void 0:o.errors;if(null!=s){let e=i.findIndex((e=>e.route.id&&void 0!==(null==s?void 0:s[e.route.id])));e>=0||d(!1),i=i.slice(0,Math.min(i.length,e+1))}let u=!1,c=-1;if(n&&r&&r.v7_partialHydration)for(let a=0;a<i.length;a++){let e=i[a];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(c=a),e.route.id){let{loaderData:t,errors:r}=n,a=e.route.loader&&void 0===t[e.route.id]&&(!r||void 0===r[e.route.id]);if(e.route.lazy||a){u=!0,i=c>=0?i.slice(0,c+1):[i[0]];break}}}return i.reduceRight(((e,r,o)=>{let l,d=!1,f=null,p=null;var h;n&&(l=s&&r.route.id?s[r.route.id]:void 0,f=r.route.errorElement||ct,u&&(c<0&&0===o?(h="route-fallback",!1||bt[h]||(bt[h]=!0),d=!0,p=null):c===o&&(d=!0,p=r.route.hydrateFallbackElement||null)));let m=t.concat(i.slice(0,o+1)),v=()=>{let t;return t=l?f:d?p:r.route.Component?a.createElement(r.route.Component,null):r.route.element?r.route.element:e,a.createElement(ft,{match:r,routeContext:{outlet:e,matches:m,isDataRoute:null!=n},children:t})};return n&&(r.route.ErrorBoundary||r.route.errorElement||0===o)?a.createElement(dt,{location:n.location,revalidation:n.revalidation,component:f,error:l,children:v(),routeContext:{outlet:null,matches:m,isDataRoute:!0}}):v()}),null)}var ht=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(ht||{}),mt=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(mt||{});function vt(e){let t=a.useContext(Je);return t||d(!1),t}function gt(e){let t=a.useContext(Xe);return t||d(!1),t}function yt(e){let t=function(){let e=a.useContext(et);return e||d(!1),e}(),n=t.matches[t.matches.length-1];return n.route.id||d(!1),n.route.id}const bt={};function wt(e,t){null==e||e.v7_startTransition,void 0===(null==e?void 0:e.v7_relativeSplatPath)&&(!t||t.v7_relativeSplatPath),t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation)}o.startTransition;function xt(e){return function(e){let t=a.useContext(et).outlet;return t?a.createElement(lt.Provider,{value:e},t):t}(e.context)}function kt(e){let{basename:t="/",children:n=null,location:o,navigationType:l=r.Pop,navigator:i,static:s=!1,future:u}=e;nt()&&d(!1);let c=t.replace(/^\/*/,"/"),f=a.useMemo((()=>({basename:c,navigator:i,static:s,future:Ye({v7_relativeSplatPath:!1},u)})),[c,u,i,s]);"string"===typeof o&&(o=v(o));let{pathname:p="/",search:h="",hash:m="",state:g=null,key:y="default"}=o,b=a.useMemo((()=>{let e=A(p,c);return null==e?null:{location:{pathname:e,search:h,hash:m,state:g,key:y},navigationType:l}}),[c,p,h,m,g,y,l]);return null==b?null:a.createElement(Ge.Provider,{value:f},a.createElement(Ze.Provider,{children:n,value:b}))}new Promise((()=>{}));a.Component;function St(e){let t={hasErrorBoundary:null!=e.ErrorBoundary||null!=e.errorElement};return e.Component&&Object.assign(t,{element:a.createElement(e.Component),Component:void 0}),e.HydrateFallback&&Object.assign(t,{hydrateFallbackElement:a.createElement(e.HydrateFallback),HydrateFallback:void 0}),e.ErrorBoundary&&Object.assign(t,{errorElement:a.createElement(e.ErrorBoundary),ErrorBoundary:void 0}),t}function Et(){return Et=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Et.apply(this,arguments)}function Ct(e,t){if(null==e)return{};var n,r,a={},o=Object.keys(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);const _t=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],jt=["aria-current","caseSensitive","className","end","style","to","viewTransition","children"];try{window.__reactRouterVersion="6"}catch(xr){}function Pt(){var e;let t=null==(e=window)?void 0:e.__staticRouterHydrationData;return t&&t.errors&&(t=Et({},t,{errors:Dt(t.errors)})),t}function Dt(e){if(!e)return null;let t=Object.entries(e),n={};for(let[r,a]of t)if(a&&"RouteErrorResponse"===a.__type)n[r]=new V(a.status,a.statusText,a.data,!0===a.internal);else if(a&&"Error"===a.__type){if(a.__subType){let e=window[a.__subType];if("function"===typeof e)try{let t=new e(a.message);t.stack="",n[r]=t}catch(xr){}}if(null==n[r]){let e=new Error(a.message);e.stack="",n[r]=e}}else n[r]=a;return n}const Nt=a.createContext({isTransitioning:!1});const Rt=a.createContext(new Map);const Tt=o.startTransition,Lt=s.flushSync;o.useId;function It(e){Lt?Lt(e):e()}class Ot{constructor(){this.status="pending",this.promise=new Promise(((e,t)=>{this.resolve=t=>{"pending"===this.status&&(this.status="resolved",e(t))},this.reject=e=>{"pending"===this.status&&(this.status="rejected",t(e))}}))}}function At(e){let{fallbackElement:t,router:n,future:r}=e,[o,l]=a.useState(n.state),[i,s]=a.useState(),[u,c]=a.useState({isTransitioning:!1}),[d,f]=a.useState(),[p,h]=a.useState(),[m,v]=a.useState(),g=a.useRef(new Map),{v7_startTransition:y}=r||{},b=a.useCallback((e=>{y?function(e){Tt?Tt(e):e()}(e):e()}),[y]),w=a.useCallback(((e,t)=>{let{deletedFetchers:r,flushSync:a,viewTransitionOpts:o}=t;e.fetchers.forEach(((e,t)=>{void 0!==e.data&&g.current.set(t,e.data)})),r.forEach((e=>g.current.delete(e)));let i=null==n.window||null==n.window.document||"function"!==typeof n.window.document.startViewTransition;if(o&&!i){if(a){It((()=>{p&&(d&&d.resolve(),p.skipTransition()),c({isTransitioning:!0,flushSync:!0,currentLocation:o.currentLocation,nextLocation:o.nextLocation})}));let t=n.window.document.startViewTransition((()=>{It((()=>l(e)))}));return t.finished.finally((()=>{It((()=>{f(void 0),h(void 0),s(void 0),c({isTransitioning:!1})}))})),void It((()=>h(t)))}p?(d&&d.resolve(),p.skipTransition(),v({state:e,currentLocation:o.currentLocation,nextLocation:o.nextLocation})):(s(e),c({isTransitioning:!0,flushSync:!1,currentLocation:o.currentLocation,nextLocation:o.nextLocation}))}else a?It((()=>l(e))):b((()=>l(e)))}),[n.window,p,d,g,b]);a.useLayoutEffect((()=>n.subscribe(w)),[n,w]),a.useEffect((()=>{u.isTransitioning&&!u.flushSync&&f(new Ot)}),[u]),a.useEffect((()=>{if(d&&i&&n.window){let e=i,t=d.promise,r=n.window.document.startViewTransition((async()=>{b((()=>l(e))),await t}));r.finished.finally((()=>{f(void 0),h(void 0),s(void 0),c({isTransitioning:!1})})),h(r)}}),[b,i,d,n.window]),a.useEffect((()=>{d&&i&&o.location.key===i.location.key&&d.resolve()}),[d,p,o.location,i]),a.useEffect((()=>{!u.isTransitioning&&m&&(s(m.state),c({isTransitioning:!0,flushSync:!1,currentLocation:m.currentLocation,nextLocation:m.nextLocation}),v(void 0))}),[u.isTransitioning,m]),a.useEffect((()=>{}),[]);let x=a.useMemo((()=>({createHref:n.createHref,encodeLocation:n.encodeLocation,go:e=>n.navigate(e),push:(e,t,r)=>n.navigate(e,{state:t,preventScrollReset:null==r?void 0:r.preventScrollReset}),replace:(e,t,r)=>n.navigate(e,{replace:!0,state:t,preventScrollReset:null==r?void 0:r.preventScrollReset})})),[n]),k=n.basename||"/",S=a.useMemo((()=>({router:n,navigator:x,static:!1,basename:k})),[n,x,k]),E=a.useMemo((()=>({v7_relativeSplatPath:n.future.v7_relativeSplatPath})),[n.future.v7_relativeSplatPath]);return a.useEffect((()=>wt(r,n.future)),[r,n.future]),a.createElement(a.Fragment,null,a.createElement(Je.Provider,{value:S},a.createElement(Xe.Provider,{value:o},a.createElement(Rt.Provider,{value:g.current},a.createElement(Nt.Provider,{value:u},a.createElement(kt,{basename:k,location:o.location,navigationType:o.historyAction,navigator:x,future:E},o.initialized||n.future.v7_partialHydration?a.createElement(zt,{routes:n.routes,future:n.future,state:o}):t))))),null)}const zt=a.memo(Mt);function Mt(e){let{routes:t,future:n,state:r}=e;return st(t,void 0,r,n)}const Ft="undefined"!==typeof window&&"undefined"!==typeof window.document&&"undefined"!==typeof window.document.createElement,Ut=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Bt=a.forwardRef((function(e,t){let n,{onClick:r,relative:o,reloadDocument:l,replace:i,state:s,target:u,to:c,preventScrollReset:f,viewTransition:p}=e,h=Ct(e,_t),{basename:v}=a.useContext(Ge),g=!1;if("string"===typeof c&&Ut.test(c)&&(n=c,Ft))try{let e=new URL(window.location.href),t=c.startsWith("//")?new URL(e.protocol+c):new URL(c),n=A(t.pathname,v);t.origin===e.origin&&null!=n?c=n+t.search+t.hash:g=!0}catch(xr){}let y=function(e,t){let{relative:n}=void 0===t?{}:t;nt()||d(!1);let{basename:r,navigator:o}=a.useContext(Ge),{hash:l,pathname:i,search:s}=it(e,{relative:n}),u=i;return"/"!==r&&(u="/"===i?r:B([r,i])),o.createHref({pathname:u,search:s,hash:l})}(c,{relative:o}),b=function(e,t){let{target:n,replace:r,state:o,preventScrollReset:l,relative:i,viewTransition:s}=void 0===t?{}:t,u=ot(),c=rt(),d=it(e,{relative:i});return a.useCallback((t=>{if(function(e,t){return 0===e.button&&(!t||"_self"===t)&&!function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e)}(t,n)){t.preventDefault();let n=void 0!==r?r:m(c)===m(d);u(e,{replace:n,state:o,preventScrollReset:l,relative:i,viewTransition:s})}}),[c,u,d,r,o,n,e,l,i,s])}(c,{replace:i,state:s,target:u,preventScrollReset:f,relative:o,viewTransition:p});return a.createElement("a",Et({},h,{href:n||y,onClick:g||l?r:function(e){r&&r(e),e.defaultPrevented||b(e)},ref:t,target:u}))}));const $t=a.forwardRef((function(e,t){let{"aria-current":n="page",caseSensitive:r=!1,className:o="",end:l=!1,style:i,to:s,viewTransition:u,children:c}=e,f=Ct(e,jt),p=it(s,{relative:f.relative}),h=rt(),m=a.useContext(Xe),{navigator:v,basename:g}=a.useContext(Ge),y=null!=m&&function(e,t){void 0===t&&(t={});let n=a.useContext(Nt);null==n&&d(!1);let{basename:r}=Vt(Wt.useViewTransitionState),o=it(e,{relative:t.relative});if(!n.isTransitioning)return!1;let l=A(n.currentLocation.pathname,r)||n.currentLocation.pathname,i=A(n.nextLocation.pathname,r)||n.nextLocation.pathname;return null!=I(o.pathname,i)||null!=I(o.pathname,l)}(p)&&!0===u,b=v.encodeLocation?v.encodeLocation(p).pathname:p.pathname,w=h.pathname,x=m&&m.navigation&&m.navigation.location?m.navigation.location.pathname:null;r||(w=w.toLowerCase(),x=x?x.toLowerCase():null,b=b.toLowerCase()),x&&g&&(x=A(x,g)||x);const k="/"!==b&&b.endsWith("/")?b.length-1:b.length;let S,E=w===b||!l&&w.startsWith(b)&&"/"===w.charAt(k),C=null!=x&&(x===b||!l&&x.startsWith(b)&&"/"===x.charAt(b.length)),_={isActive:E,isPending:C,isTransitioning:y},j=E?n:void 0;S="function"===typeof o?o(_):[o,E?"active":null,C?"pending":null,y?"transitioning":null].filter(Boolean).join(" ");let P="function"===typeof i?i(_):i;return a.createElement(Bt,Et({},f,{"aria-current":j,className:S,ref:t,style:P,to:s,viewTransition:u}),"function"===typeof c?c(_):c)}));var Wt,Ht;function Vt(e){let t=a.useContext(Je);return t||d(!1),t}(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Wt||(Wt={})),function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"}(Ht||(Ht={}));const Qt=e=>{e&&e instanceof Function&&n.e(453).then(n.bind(n,453)).then((t=>{let{getCLS:n,getFID:r,getFCP:a,getLCP:o,getTTFB:l}=t;n(e),r(e),a(e),o(e),l(e)}))},Kt=`${{NODE_ENV:"production",PUBLIC_URL:"/plugin-server/public/ui",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_SERVER_URL:"http://localhost:3100"}.WEB_SOCKET_URL}/plugin-server/healthcheck`;let qt="";const Yt=new Map;function Jt(e,t){let n=Yt.get(e);return n||(n=new Set,Yt.set(e,n)),n.add(t),()=>{n&&n.delete(t)}}var Xt=n(579);const Gt=function(){const e=(0,a.useRef)(null),t=(0,a.useCallback)((t=>{e.current&&(e.current.innerText=t,e.current.scrollTop=e.current.scrollHeight)}),[e]);return(0,a.useEffect)((()=>Jt("onLog",t)),[t]),(0,Xt.jsx)("div",{children:(0,Xt.jsx)("pre",{ref:e,style:{fontSize:10,height:"calc(100vh - 28px)",overflow:"auto"}})})},Zt=function(){const e=e=>{let{isActive:t}=e;return t?"text-indigo-500 border rounded border-indigo-500 border-2 w-full":"text-stone-500 w-full"},t=[{path:"/workspaces",label:"Workspaces"},{path:"/currentApp",label:"CurrentApp"},{path:"/socketclients",label:"Clients"},{path:"webSdk",label:"WebSDK"}].map((t=>(0,Xt.jsx)($t,{to:t.path,className:e,children:(0,Xt.jsx)("div",{className:"h-10 flex items-center justify-center",children:(0,Xt.jsx)("span",{style:{fontSize:8},children:t.label})})},t.path)));return(0,Xt.jsxs)("div",{className:"grid grid-cols-[50px,1fr,1fr]",children:[(0,Xt.jsx)("div",{className:"flex flex-col items-start",children:t}),(0,Xt.jsx)("div",{className:"flex flex-col items-start",children:(0,Xt.jsx)(xt,{})}),(0,Xt.jsx)(Gt,{})]})};var en=Symbol.for("immer-nothing"),tn=Symbol.for("immer-draftable"),nn=Symbol.for("immer-state");function rn(e){throw new Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var an=Object.getPrototypeOf;function on(e){return!!e&&!!e[nn]}function ln(e){return!!e&&(un(e)||Array.isArray(e)||!!e[tn]||!!e.constructor?.[tn]||hn(e)||mn(e))}var sn=Object.prototype.constructor.toString();function un(e){if(!e||"object"!==typeof e)return!1;const t=an(e);if(null===t)return!0;const n=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return n===Object||"function"==typeof n&&Function.toString.call(n)===sn}function cn(e,t){0===dn(e)?Reflect.ownKeys(e).forEach((n=>{t(n,e[n],e)})):e.forEach(((n,r)=>t(r,n,e)))}function dn(e){const t=e[nn];return t?t.type_:Array.isArray(e)?1:hn(e)?2:mn(e)?3:0}function fn(e,t){return 2===dn(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function pn(e,t,n){const r=dn(e);2===r?e.set(t,n):3===r?e.add(n):e[t]=n}function hn(e){return e instanceof Map}function mn(e){return e instanceof Set}function vn(e){return e.copy_||e.base_}function gn(e,t){if(hn(e))return new Map(e);if(mn(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);const n=un(e);if(!0===t||"class_only"===t&&!n){const t=Object.getOwnPropertyDescriptors(e);delete t[nn];let n=Reflect.ownKeys(t);for(let r=0;r<n.length;r++){const a=n[r],o=t[a];!1===o.writable&&(o.writable=!0,o.configurable=!0),(o.get||o.set)&&(t[a]={configurable:!0,writable:!0,enumerable:o.enumerable,value:e[a]})}return Object.create(an(e),t)}{const t=an(e);if(null!==t&&n)return{...e};const r=Object.create(t);return Object.assign(r,e)}}function yn(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return wn(e)||on(e)||!ln(e)||(dn(e)>1&&(e.set=e.add=e.clear=e.delete=bn),Object.freeze(e),t&&Object.entries(e).forEach((e=>{let[t,n]=e;return yn(n,!0)}))),e}function bn(){rn(2)}function wn(e){return Object.isFrozen(e)}var xn,kn={};function Sn(e){const t=kn[e];return t||rn(0),t}function En(){return xn}function Cn(e,t){t&&(Sn("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function _n(e){jn(e),e.drafts_.forEach(Dn),e.drafts_=null}function jn(e){e===xn&&(xn=e.parent_)}function Pn(e){return xn={drafts_:[],parent_:xn,immer_:e,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function Dn(e){const t=e[nn];0===t.type_||1===t.type_?t.revoke_():t.revoked_=!0}function Nn(e,t){t.unfinalizedDrafts_=t.drafts_.length;const n=t.drafts_[0];return void 0!==e&&e!==n?(n[nn].modified_&&(_n(t),rn(4)),ln(e)&&(e=Rn(t,e),t.parent_||Ln(t,e)),t.patches_&&Sn("Patches").generateReplacementPatches_(n[nn].base_,e,t.patches_,t.inversePatches_)):e=Rn(t,n,[]),_n(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==en?e:void 0}function Rn(e,t,n){if(wn(t))return t;const r=t[nn];if(!r)return cn(t,((a,o)=>Tn(e,r,t,a,o,n))),t;if(r.scope_!==e)return t;if(!r.modified_)return Ln(e,r.base_,!0),r.base_;if(!r.finalized_){r.finalized_=!0,r.scope_.unfinalizedDrafts_--;const t=r.copy_;let a=t,o=!1;3===r.type_&&(a=new Set(t),t.clear(),o=!0),cn(a,((a,l)=>Tn(e,r,t,a,l,n,o))),Ln(e,t,!1),n&&e.patches_&&Sn("Patches").generatePatches_(r,n,e.patches_,e.inversePatches_)}return r.copy_}function Tn(e,t,n,r,a,o,l){if(on(a)){const l=Rn(e,a,o&&t&&3!==t.type_&&!fn(t.assigned_,r)?o.concat(r):void 0);if(pn(n,r,l),!on(l))return;e.canAutoFreeze_=!1}else l&&n.add(a);if(ln(a)&&!wn(a)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;Rn(e,a),t&&t.scope_.parent_||"symbol"===typeof r||!Object.prototype.propertyIsEnumerable.call(n,r)||Ln(e,a)}}function Ln(e,t){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&yn(t,n)}var In={get(e,t){if(t===nn)return e;const n=vn(e);if(!fn(n,t))return function(e,t,n){const r=zn(t,n);return r?"value"in r?r.value:r.get?.call(e.draft_):void 0}(e,n,t);const r=n[t];return e.finalized_||!ln(r)?r:r===An(e.base_,t)?(Fn(e),e.copy_[t]=Un(r,e)):r},has:(e,t)=>t in vn(e),ownKeys:e=>Reflect.ownKeys(vn(e)),set(e,t,n){const r=zn(vn(e),t);if(r?.set)return r.set.call(e.draft_,n),!0;if(!e.modified_){const r=An(vn(e),t),l=r?.[nn];if(l&&l.base_===n)return e.copy_[t]=n,e.assigned_[t]=!1,!0;if(((a=n)===(o=r)?0!==a||1/a===1/o:a!==a&&o!==o)&&(void 0!==n||fn(e.base_,t)))return!0;Fn(e),Mn(e)}var a,o;return e.copy_[t]===n&&(void 0!==n||t in e.copy_)||Number.isNaN(n)&&Number.isNaN(e.copy_[t])||(e.copy_[t]=n,e.assigned_[t]=!0),!0},deleteProperty:(e,t)=>(void 0!==An(e.base_,t)||t in e.base_?(e.assigned_[t]=!1,Fn(e),Mn(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0),getOwnPropertyDescriptor(e,t){const n=vn(e),r=Reflect.getOwnPropertyDescriptor(n,t);return r?{writable:!0,configurable:1!==e.type_||"length"!==t,enumerable:r.enumerable,value:n[t]}:r},defineProperty(){rn(11)},getPrototypeOf:e=>an(e.base_),setPrototypeOf(){rn(12)}},On={};function An(e,t){const n=e[nn];return(n?vn(n):e)[t]}function zn(e,t){if(!(t in e))return;let n=an(e);for(;n;){const e=Object.getOwnPropertyDescriptor(n,t);if(e)return e;n=an(n)}}function Mn(e){e.modified_||(e.modified_=!0,e.parent_&&Mn(e.parent_))}function Fn(e){e.copy_||(e.copy_=gn(e.base_,e.scope_.immer_.useStrictShallowCopy_))}cn(In,((e,t)=>{On[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}})),On.deleteProperty=function(e,t){return On.set.call(this,e,t,void 0)},On.set=function(e,t,n){return In.set.call(this,e[0],t,n,e[0])};function Un(e,t){const n=hn(e)?Sn("MapSet").proxyMap_(e,t):mn(e)?Sn("MapSet").proxySet_(e,t):function(e,t){const n=Array.isArray(e),r={type_:n?1:0,scope_:t?t.scope_:En(),modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1};let a=r,o=In;n&&(a=[r],o=On);const{revoke:l,proxy:i}=Proxy.revocable(a,o);return r.draft_=i,r.revoke_=l,i}(e,t);return(t?t.scope_:En()).drafts_.push(n),n}function Bn(e){if(!ln(e)||wn(e))return e;const t=e[nn];let n;if(t){if(!t.modified_)return t.base_;t.finalized_=!0,n=gn(e,t.scope_.immer_.useStrictShallowCopy_)}else n=gn(e,!0);return cn(n,((e,t)=>{pn(n,e,Bn(t))})),t&&(t.finalized_=!1),n}var $n=new class{constructor(e){var t=this;this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(e,t,n)=>{if("function"===typeof e&&"function"!==typeof t){const n=t;t=e;const r=this;return function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:n;for(var a=arguments.length,o=new Array(a>1?a-1:0),l=1;l<a;l++)o[l-1]=arguments[l];return r.produce(e,(e=>t.call(this,e,...o)))}}let r;if("function"!==typeof t&&rn(6),void 0!==n&&"function"!==typeof n&&rn(7),ln(e)){const a=Pn(this),o=Un(e,void 0);let l=!0;try{r=t(o),l=!1}finally{l?_n(a):jn(a)}return Cn(a,n),Nn(r,a)}if(!e||"object"!==typeof e){if(r=t(e),void 0===r&&(r=e),r===en&&(r=void 0),this.autoFreeze_&&yn(r,!0),n){const t=[],a=[];Sn("Patches").generateReplacementPatches_(e,r,t,a),n(t,a)}return r}rn(1)},this.produceWithPatches=(e,n)=>{if("function"===typeof e)return function(n){for(var r=arguments.length,a=new Array(r>1?r-1:0),o=1;o<r;o++)a[o-1]=arguments[o];return t.produceWithPatches(n,(t=>e(t,...a)))};let r,a;return[this.produce(e,n,((e,t)=>{r=e,a=t})),r,a]},"boolean"===typeof e?.autoFreeze&&this.setAutoFreeze(e.autoFreeze),"boolean"===typeof e?.useStrictShallowCopy&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){ln(e)||rn(8),on(e)&&(e=function(e){on(e)||rn(10);return Bn(e)}(e));const t=Pn(this),n=Un(e,void 0);return n[nn].isManual_=!0,jn(t),n}finishDraft(e,t){const n=e&&e[nn];n&&n.isManual_||rn(9);const{scope_:r}=n;return Cn(r,t),Nn(void 0,r)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let n;for(n=t.length-1;n>=0;n--){const r=t[n];if(0===r.path.length&&"replace"===r.op){e=r.value;break}}n>-1&&(t=t.slice(n+1));const r=Sn("Patches").applyPatches_;return on(e)?r(e,t):this.produce(e,(e=>r(e,t)))}},Wn=$n.produce;$n.produceWithPatches.bind($n),$n.setAutoFreeze.bind($n),$n.setUseStrictShallowCopy.bind($n),$n.applyPatches.bind($n),$n.createDraft.bind($n),$n.finishDraft.bind($n);let Hn,Vn;function Qn(e){return"number"===typeof e.id&&["plugins","navigators","ios-jsbundle","android-jsbundle"].includes(e.type)&&"string"===typeof e.cdnlink&&"string"===typeof e.tag}function Kn(e){let t=!0;for(let n=0;n<e.length;++n){const r=e[n];if(t=t&&Qn(r),!t)break}return t}!function(e){function t(e){let t=e;try{return!(!t||"number"!==typeof t.id||"string"!==typeof t.location)}catch(n){return console.error("Failed to narrow workspace",n),!1}}e.narrowWorkspace=t,e.narrowWorkspaceArray=function(e){try{let n=!0;if(!Array.isArray(e))return!1;for(let r of e)if(n=n&&t(r),!n)break;return n}catch(n){return console.error("Failed to narrow workspace array",n),!1}}}(Hn||(Hn={})),function(e){function t(e){try{return"string"===typeof e.id&&"string"===typeof e.name&&"string"===typeof e.type}catch(t){return console.error("Failed to validate mobile device",t),!1}}function n(e){let t="string"===typeof e.name&&"string"===typeof e.fullPath&&("string"===typeof e.gitRepo||null===e.gitRepo)&&"string"===typeof e.appId&&"string"===typeof e.apptileServer&&"string"===typeof e.appconfigServer&&"boolean"===typeof e.isOpen;for(let n=0;n<e.codePushBundles.ios.length;++n){const r=e.codePushBundles.ios[n];if(t=t&&"string"===typeof r.fullPath&&"number"===typeof r.timestamp,!t)break}for(let n=0;n<e.codePushBundles.android.length;++n){const r=e.codePushBundles.android[n];if(t=t&&"string"===typeof r.fullPath&&"number"===typeof r.timestamp,!t)break}return t}function r(e){try{let t=!0;for(let r=0;r<e.length&&(t=t&&n(e[r]),t);++r);return t||console.error("Failed to narrow repo data"),t}catch(t){return console.error("Validation error: ",t),!1}}function a(e){try{return"number"===typeof e.id&&"string"===typeof e.sdkHash&&"string"===typeof e.location&&r(e.repos)}catch(t){return console.error("Validation Error: ",t),!1}}e.narrowMobileDevice=t,e.narrowMobileDeviceArray=function(e){try{let n=!0;for(let r=0;r<e.length&&(n=n&&t(e[r]),n);++r);return n}catch(n){return console.error("Failed to validate mobile device",n),!1}},e.narrowRepo=n,e.narrowRepoArray=r,e.narrowWorkspaceData=a,e.narrowWorkspaceDataArray=function(e){try{let t=!0;for(let n=0;n<e.length&&(t=t&&a(e[n]),t);++n);return t}catch(t){return console.error("Validation Error: ",t),!1}}}(Vn||(Vn={}));const qn={appData:{loadingInfo:{status:"notstarted",errorMessage:"",error:null},payload:{manifest:{name:"",uuid:"",gitRepo:null,iosBundleId:null,navigatorsBundleId:null,pluginsBundleId:null,androidBundleId:null,codeArtefacts:[],forks:[]},appIntegrations:[],apptileConfig:{apptileServer:"",appconfigServer:""},bundles:[]}},workspaceData:{loadingInfo:{status:"notstarted",errorMessage:"",error:null},payload:[]},devicesData:{loadingInfo:{status:"notstarted",errorMessage:"",error:null},payload:{ios:[],android:[]}},currentAppRepoOffset:-1};function Yn(e){if("success"===e.appData.loadingInfo.status&&"success"===e.workspaceData.loadingInfo.status){let t=-1;e:for(let n=0;n<e.workspaceData.payload.length;++n){const r=e.workspaceData.payload[n];for(let n=0;n<r.repos.length;++n){const a=r.repos[n];if(a.appId===e.appData.payload.manifest.uuid&&a.isOpen){t=n;break e}}}return t}return console.error("Setting repo offset to -1. Check that appId is correct in the apptile.config.json of the cloned app"),-1}const Jn=Wn(((e,t)=>{switch(t.type){case"APPDATA_CLEAR":e.appData.loadingInfo.status="notstarted",e.currentAppRepoOffset=-1;break;case"APPDATA_SET_LOADING":e.appData.loadingInfo.status="inprogress",e.currentAppRepoOffset=-1;break;case"APPDATA_SET_ERROR":e.appData.loadingInfo.status="error",e.appData.loadingInfo.errorMessage=t.payload.message,e.appData.loadingInfo.error=t.payload.err,e.currentAppRepoOffset=-1;break;case"APPDATA_SET_LOADED":e.appData.loadingInfo.status="success",e.appData.payload=t.payload,e.currentAppRepoOffset=Yn(e);break;case"WORKSPACE_CLEAR":e.workspaceData.loadingInfo.status="notstarted",e.currentAppRepoOffset=-1;break;case"WORKSPACE_SET_LOADING":e.workspaceData.loadingInfo.status="inprogress",e.currentAppRepoOffset=-1;break;case"WORKSPACE_SET_ERROR":e.workspaceData.loadingInfo.status="error",e.workspaceData.loadingInfo.errorMessage=t.payload.message,e.workspaceData.loadingInfo.error=t.payload.err,e.currentAppRepoOffset=-1;break;case"WORKSPACE_SET_LOADED":e.workspaceData.loadingInfo.status="success",e.workspaceData.payload=t.payload,e.currentAppRepoOffset=Yn(e);break;case"DEVICES_CLEAR":e.devicesData.loadingInfo.status="notstarted";break;case"DEVICES_SET_LOADING":e.devicesData.loadingInfo.status="inprogress";break;case"DEVICES_SET_ERROR":e.devicesData.loadingInfo.status="error",e.devicesData.loadingInfo.errorMessage=t.payload.message,e.devicesData.loadingInfo.error=t.payload.err;break;case"DEVICES_SET_LOADED":e.devicesData.loadingInfo.status="success",e.devicesData.payload=t.payload;break;default:throw new Error("Unhandled action")}}));let Xn=(0,a.createContext)(null),Gn=(0,a.createContext)((function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return console.log("Cannot process: ",t)}));function Zn(e,t,n){t&&e({type:"WORKSPACE_SET_LOADING"}),fetch("http://localhost:3100/plugin-server/cli/workspaces/list").then((e=>e.json())).then((t=>{Vn.narrowWorkspaceDataArray(t)?e({type:"WORKSPACE_SET_LOADED",payload:t}):(console.error("Failed to validate workspace data"),n&&e({type:"WORKSPACE_SET_ERROR",payload:{message:"Could not validate workspace data",err:new Error("Could not validate workspace data")}}))})).catch((t=>{console.error("Failed to fetch app details: ",t),n&&e({type:"WORKSPACE_SET_ERROR",payload:{message:"Invalid workspace app data "+t.message,err:t}})}))}const er=function(){const[e,t]=(0,a.useReducer)(Jn,qn);return(0,a.useEffect)((()=>{function e(e){if("appData"===e.data.type)if(function(e){try{let r=!0;r=r&&"string"===typeof e.manifest.name,r=r&&"string"===typeof e.manifest.uuid,r=r&&("string"===typeof e.manifest.gitRepo||null===e.manifest.gitRepo),r=r&&("number"===typeof e.manifest.iosBundleId||null===e.manifest.iosBundleId),r=r&&("number"===typeof e.manifest.navigatorsBundleId||null===e.manifest.navigatorsBundleId),r=r&&("number"===typeof e.manifest.pluginsBundleId||null===e.manifest.pluginsBundleId),r=r&&Kn(e.manifest.codeArtefacts);for(let a=0;a<(null===e||void 0===e||null===(t=e.manifest)||void 0===t?void 0:t.forks.length);++a){var t,n;const o=null===e||void 0===e||null===(n=e.manifest)||void 0===n?void 0:n.forks[a];if(r=r&&"number"===typeof o.id&&"string"===typeof o.title&&("number"===typeof o.publishedCommitId||null===o.publishedCommitId),!r)break}for(let t=0;t<e.appIntegrations.length;++t){const n=e.appIntegrations[t];r=r&&"string"===typeof n.id&&"string"===typeof n.integrationCode&&(null===n.packageLocation||"string"===typeof n.packageLocation)&&"string"===typeof n.title}return r=r&&"string"===typeof e.apptileConfig.apptileServer&&"string"===typeof e.apptileConfig.appconfigServer,r=r&&Kn(e.bundles),r||console.error("failure in validating AppDataPayload"),r}catch(r){return console.error("Failed to validate AppDataPayload",r),!1}}(e.data.payload))t({type:"APPDATA_SET_LOADED",payload:{manifest:e.data.payload.manifest,appIntegrations:e.data.payload.appIntegrations,apptileConfig:e.data.payload.apptileConfig,bundles:e.data.payload.bundles}});else{const n="Invalid appData received from host "+JSON.stringify(e.data.payload);t({type:"APPDATA_SET_ERROR",payload:{err:new Error(n),message:n}})}else console.warn("Ignoring event: ",e)}return console.log("Starting load of appData"),window.parent.postMessage({type:"requestAppData"},"*"),t({type:"APPDATA_SET_LOADING"}),window.addEventListener("message",e),()=>{window.removeEventListener("message",e)}}),[t]),(0,Xt.jsx)(Xn.Provider,{value:e,children:(0,Xt.jsx)(Gn.Provider,{value:t,children:(0,Xt.jsx)(Zt,{})})})},tr=function(e){let{onClick:t,label:r,showLoader:a,disabled:o,className:l}=e,i=null;return a&&(i=(0,Xt.jsx)("img",{alt:"loading",src:n(833),style:{height:15,marginRight:4}})),(0,Xt.jsxs)("button",{disabled:o,className:`flex flex-row p-1 rounded text-blue-500 border border-blue-500 m-1 disabled:opacity-50 ${l}`,onClick:t,children:[r,i]})},nr=function(e){var t,n;let{workspaceId:r}=e;const o=(0,a.useRef)(null),l=(0,a.useContext)(Xn),i=()=>{o.current&&o.current.close()};let s=null;if(l)switch(l.appData.loadingInfo.status){case"notstarted":s=(0,Xt.jsx)("div",{children:"Data loading has not started. Wait"});break;case"inprogress":s=(0,Xt.jsx)("div",{children:"Loading..."});break;case"error":s=(0,Xt.jsxs)("div",{children:["Error happened ",l.appData.loadingInfo.errorMessage,(0,Xt.jsx)(tr,{label:"Retry",onClick:()=>{}})]});break;case"success":const{manifest:e,appIntegrations:t,apptileConfig:n}=l.appData.payload;s=(0,Xt.jsxs)("div",{children:[(0,Xt.jsxs)("div",{children:["name: ",e.name]}),(0,Xt.jsxs)("div",{children:["appId: ",e.uuid]}),(0,Xt.jsxs)("div",{children:["repo: ",e.gitRepo]}),(0,Xt.jsx)("div",{children:"Integrations"}),t.map((e=>(0,Xt.jsxs)("div",{children:[e.integrationCode,":",e.packageLocation]},e.integrationCode))),(0,Xt.jsxs)("div",{children:["apptileServer: ",n.apptileServer]}),(0,Xt.jsxs)("div",{children:["appconfigServer: ",n.appconfigServer]})]});break;default:s=(0,Xt.jsx)("div",{children:"Invalid loading state!"})}else s=(0,Xt.jsx)("div",{children:"Store is not initialized yet! Wait"});return(0,Xt.jsxs)(Xt.Fragment,{children:[(0,Xt.jsxs)("dialog",{ref:o,children:[s,(0,Xt.jsx)(tr,{label:"Clone",showLoader:"success"!==(null===l||void 0===l||null===(t=l.appData)||void 0===t||null===(n=t.loadingInfo)||void 0===n?void 0:n.status),onClick:async()=>{l?(await fetch(`http://localhost:3100/plugin-server/cli/workspaces/${r}/cloneApp`,{method:"POST",body:JSON.stringify({appIntegrations:l.appData.payload.appIntegrations,manifest:l.appData.payload.manifest,apptileConfig:l.appData.payload.apptileConfig}),headers:{"Content-Type":"application/json"}}),i()):console.error("Store is not defined. Cannot perform clone")}}),(0,Xt.jsx)("button",{className:"bg-blue-500 p-2 rounded text-white",onClick:i,children:"Cancel"})]}),(0,Xt.jsx)("button",{className:"border border-solid border-green-500 rounded w-14 h-14 mt-1",onClick:async()=>{o.current&&o.current.showModal()},children:"+"})]})};function rr(e,t){"code"===t?fetch(`http://localhost:3100/plugin-server/cli/operation/opencode/${encodeURIComponent(e)}`,{method:"POST"}):fetch(`http://localhost:3100/plugin-server/cli/operation/openfinder/${encodeURIComponent(e)}`,{method:"POST"})}const ar=function(){const e=(0,a.useContext)(Gn),t=(0,a.useContext)(Xn);(0,a.useEffect)((()=>{Zn(e,!0,!0)}),[e]);let r=null;return r=t&&"success"===t.workspaceData.loadingInfo.status?t.workspaceData.payload.map((t=>{const r=t.repos.map((n=>(0,Xt.jsxs)("div",{className:"border border-solid border-green-500 rounded p-1",children:[(0,Xt.jsx)("div",{className:"self-center text-xs p-1",children:n.name}),(0,Xt.jsxs)("div",{className:"self-center p-1",style:{fontSize:10},children:[n.apptileServer," "]}),(0,Xt.jsx)(tr,{label:n.isOpen?"Close":"Open",onClick:()=>(async(t,n)=>{t.isOpen?await fetch(`http://localhost:3100/plugin-server/cli/workspaces/${n.toFixed(0)}/closeApp`,{method:"DELETE",body:JSON.stringify(t),headers:{"Content-Type":"application/json"}}):await fetch(`http://localhost:3100/plugin-server/cli/workspaces/${n}/openApp/${t.appId}`,{method:"POST"}),Zn(e,!1,!1)})(n,t.id)})]},n.name)));return(0,Xt.jsxs)("div",{className:"flex flex-col items-start border border-solid p-1",children:[(0,Xt.jsxs)("div",{className:"flex flex-row gap-1 w-full",children:[(0,Xt.jsx)("button",{onClick:()=>rr(t.location,"code"),children:(0,Xt.jsx)("img",{alt:"open in vscode",src:n(67),className:"w-10"})}),(0,Xt.jsx)("button",{onClick:()=>rr(t.location,"finder"),children:(0,Xt.jsx)("img",{alt:"open in finder",src:n(192),className:"w-10"})}),(0,Xt.jsxs)("div",{className:"flex flex-row items-center justify-end w-full",children:[(0,Xt.jsx)("span",{style:{fontSize:10},children:t.sdkHash}),(0,Xt.jsx)("img",{alt:"git repo status",src:n(449),className:"w-8"})]})]}),(0,Xt.jsx)("div",{className:"flex flex-wrap",children:r}),(0,Xt.jsx)(nr,{workspaceId:t.id})]})})):t&&"error"===t.workspaceData.loadingInfo.status?(0,Xt.jsxs)("div",{children:[t.workspaceData.loadingInfo.errorMessage,(0,Xt.jsx)("br",{}),t.workspaceData.loadingInfo.error.stack]}):t&&"inprogress"===t.workspaceData.loadingInfo.status?(0,Xt.jsx)("div",{children:"Loading..."}):(0,Xt.jsx)("div",{children:"Uh oh!"}),(0,Xt.jsxs)(Xt.Fragment,{children:[r,(0,Xt.jsx)(tr,{label:"new workspace",onClick:()=>{}})]})},or=function(){return(0,Xt.jsx)(xt,{})},lr=Intl.DateTimeFormat("en-US",{dateStyle:"long",timeStyle:"short"}),ir=function(){var e,t;const n=function(e){nt()||d(!1);let{pathname:t}=rt();return a.useMemo((()=>I(e,O(t))),[t,e])}("workspaces/:workspaceId/app/:appId"),r=null===n||void 0===n||null===(e=n.params)||void 0===e?void 0:e.appId,o=null===n||void 0===n||null===(t=n.params)||void 0===t?void 0:t.workspaceId,l=(0,a.useContext)(Xn);let i=null;if(l&&"success"===l.workspaceData.loadingInfo.status&&o){const e=parseInt(o);let t=l.workspaceData.payload.find((t=>t.id===e));t&&(i=t.repos.find((e=>e.appId===r)))}const s=()=>{fetch(`http://localhost:3100/plugin-server/home/<USER>/startMetro`,{method:"POST"})},u=()=>{fetch("http://localhost:3100/plugin-server/home/<USER>",{method:"DELETE"})},c=()=>{fetch(`http://localhost:3100/plugin-server/home/<USER>/build/ios/debug`,{method:"POST"})},f=()=>{fetch(`http://localhost:3100/plugin-server/home/<USER>/build/ios/release`,{method:"POST"})},p=()=>{fetch(`http://localhost:3100/plugin-server/home/<USER>/bundleJsIOS`,{method:"POST"})},h=(e,t)=>{fetch(`http://localhost:3100/plugin-server/home/<USER>/uploadMobileBundle/${e}/${t}`,{method:"POST"})},m=()=>{fetch(`http://localhost:3100/plugin-server/home/<USER>/runAndroid`,{method:"POST"})},v=()=>{fetch(`http://localhost:3100/plugin-server/home/<USER>/build/android/release`,{method:"POST"})},g=()=>{fetch(`http://localhost:3100/plugin-server/home/<USER>/bundleJSAndroid`,{method:"POST"})};let y=(0,Xt.jsx)("div",{children:"Invalid appId or workspaceId"}),b=null,w=null;if(l){switch(l.appData.loadingInfo.status){case"notstarted":b=(0,Xt.jsx)("div",{children:"Store fetch has not started yet! Wait"}),w=(0,Xt.jsx)("div",{children:"Store fetch has not started yet! Wait"});break;case"inprogress":b=(0,Xt.jsx)("div",{children:"Loading..."}),w=(0,Xt.jsx)("div",{children:"Loading..."});break;case"error":b=(0,Xt.jsxs)("div",{children:["Error ocurred: ",l.appData.loadingInfo.errorMessage]});break;case"success":b=l.appData.payload.manifest.codeArtefacts.map((e=>(0,Xt.jsxs)("div",{children:[e.type,":",e.id]},e.cdnlink))),w=(0,Xt.jsxs)("div",{children:[(0,Xt.jsx)("span",{children:"Bundles on s3"}),l.appData.payload.bundles.map(((e,t)=>(0,Xt.jsxs)("div",{children:[(0,Xt.jsxs)("span",{children:[e.id,":",e.type]}),(0,Xt.jsx)(tr,{label:"Publish",onClick:()=>{return t=e.id,void fetch(`http://localhost:3100/plugin-server/home/<USER>/bundles/${t}`,{method:"POST"});var t}})]},t)))]});break;default:b=(0,Xt.jsx)("div",{children:"Undefined state in store"})}y=r&&o?"inprogress"===l.workspaceData.loadingInfo.status?(0,Xt.jsx)("div",{children:"Loading..."}):"error"===l.workspaceData.loadingInfo.status?(0,Xt.jsxs)("div",{children:["Error happend: ",l.workspaceData.loadingInfo.errorMessage]}):"notstarted"===l.workspaceData.loadingInfo.status?(0,Xt.jsx)("div",{children:"Waiting for api call"}):"success"===l.workspaceData.loadingInfo.status?i?(0,Xt.jsxs)("div",{children:[(0,Xt.jsxs)("div",{children:["appid: ",i.appId]}),(0,Xt.jsxs)("div",{children:["approot: ",i.fullPath]}),(0,Xt.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,Xt.jsx)(tr,{label:"Kill Metro",onClick:u}),(0,Xt.jsx)(tr,{label:"Start Metro",onClick:s}),(0,Xt.jsx)(tr,{label:"Bundle js(ios)",onClick:p}),(0,Xt.jsx)(tr,{label:"Debug Build(ios)",onClick:c}),(0,Xt.jsx)(tr,{label:"Release Build(ios)",onClick:f}),(0,Xt.jsx)(tr,{label:"Bundle js(android)",onClick:g}),(0,Xt.jsx)(tr,{label:"Debug Android",onClick:m}),(0,Xt.jsx)(tr,{label:"Build Android",onClick:v}),(0,Xt.jsx)(tr,{label:"Distributable Build",onClick:()=>{}}),(0,Xt.jsx)(tr,{label:"Install App",onClick:()=>{}})]}),b,w,(0,Xt.jsxs)("div",{children:[(0,Xt.jsx)("span",{children:"ios Bundles"}),i.codePushBundles.ios.map((e=>(0,Xt.jsxs)("div",{children:[(0,Xt.jsx)("span",{children:lr.format(e.timestamp)}),(0,Xt.jsx)(tr,{label:"Upload",onClick:()=>h(e.timestamp,"ios")})]},e.fullPath)))]}),(0,Xt.jsxs)("div",{children:[(0,Xt.jsx)("span",{children:"android Bundles"}),i.codePushBundles.android.map((e=>(0,Xt.jsxs)("div",{children:[(0,Xt.jsx)("span",{children:lr.format(e.timestamp)}),(0,Xt.jsx)(tr,{label:"Upload",onClick:()=>h(e.timestamp,"android")})]},e.fullPath)))]})]}):(0,Xt.jsx)("div",{children:"No repo exists for this appId"}):(0,Xt.jsx)("div",{children:"Invalid state for workspace app"}):(0,Xt.jsx)("div",{children:"App home"})}else b=(0,Xt.jsx)("div",{children:"Store is not initialized"});return y};function sr(e){try{return["mobile","browser","compiler","cliui"].includes(e.type)&&"string"===typeof e.ip&&"boolean"===typeof e.isAlive}catch(t){return console.error("Failed to narrow client",t),!1}}const ur=function(){const[e,t]=(0,a.useState)([]);(0,a.useEffect)((()=>Jt("status",(e=>{e&&Array.isArray(e.clients)&&t(Wn((t=>{for(let n=0;n<e.clients.length;++n){const r=e.clients[n];if(sr(r)){const e=t.findIndex((e=>e.ip===r.ip&&e.type===r.type));e>=0?t[e]=r:t.push(r)}}})))}))),[t]);const n=e.map((e=>(0,Xt.jsxs)("div",{className:e.isAlive?"border rounded border-green-500":"border rounded border-red-500",children:[e.type,e.appId?(0,Xt.jsx)("div",{children:e.appId}):null]},e.ip+"_"+e.type)));return(0,Xt.jsxs)("div",{children:["Socket clients",n]})},cr=function(e){let{onClick:t,label:r}=e;const[o,l]=(0,a.useState)(!1);let i=null;o&&(i=(0,Xt.jsx)("img",{alt:"loading",src:n(833),style:{height:15,marginRight:4}}));return(0,Xt.jsxs)("button",{className:"flex flex-row p-1 rounded text-blue-500 border border-blue-500",onClick:async()=>{l(!0),await t(),l(!1)},children:[i,r]})},dr=function(e){let{repoData:t,appData:n,devicesData:r}=e;const o=(0,a.useContext)(Gn);(0,a.useEffect)((()=>{!function(e,t,n){t&&e({type:"DEVICES_SET_LOADING"}),fetch("http://localhost:3100/plugin-server/home/<USER>").then((e=>e.json())).then((t=>{t.ios&&t.android&&Vn.narrowMobileDeviceArray(t.ios)&&Vn.narrowMobileDeviceArray(t.android)?e({type:"DEVICES_SET_LOADED",payload:t}):(console.error("Failed to validate workspace data"),n&&e({type:"DEVICES_SET_ERROR",payload:{message:"Could not validate devices data",err:new Error("Could not validate devices data")}}))})).catch((t=>{console.error("Failed to fetch devices: ",t),n&&e({type:"DEVICES_SET_ERROR",payload:{message:"Invalid workspace app data "+t.message,err:t}})}))}(o,!1,!1)}),[o]);const l=(0,a.useRef)(null),i=(0,a.useRef)(null),s=t.appId;return(0,Xt.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,Xt.jsxs)("div",{className:"border p-1 rounded",children:[(0,Xt.jsx)("div",{children:"Repo management"}),(0,Xt.jsxs)("div",{className:"flex flex-row flex-wrap w-full p-2 gap-1",children:[(0,Xt.jsx)(cr,{label:"Codegen Integrations",onClick:async()=>{"success"===n.loadingInfo.status?await fetch(`http://localhost:3100/plugin-server/home/<USER>/refreshIntegrations`,{method:"POST",body:JSON.stringify({integrations:n.payload.appIntegrations}),headers:{"Content-Type":"application/json"}}):console.error("appData has not loaded properly yet")}}),(0,Xt.jsx)(cr,{label:"NPM Install",onClick:async()=>{await fetch(`http://localhost:3100/plugin-server/cli/app/${s}/npmInstall`,{method:"POST"})}}),(0,Xt.jsx)(cr,{label:"Pod install",onClick:async()=>{await fetch(`http://localhost:3100/plugin-server/cli/app/${s}/podInstall`,{method:"POST"})}}),(0,Xt.jsx)(cr,{label:"Regen configurations",onClick:async()=>{await fetch(`http://localhost:3100/plugin-server/cli/app/${s}/regenerateAppConfig`,{method:"POST"})}}),(0,Xt.jsx)(tr,{label:"Kill Metro",onClick:()=>{fetch("http://localhost:3100/plugin-server/home/<USER>",{method:"DELETE"})}})]})]}),(0,Xt.jsxs)("div",{className:"border p-1 rounded",children:[(0,Xt.jsx)("div",{children:"Android"}),(0,Xt.jsx)("div",{className:"p-2",children:(0,Xt.jsx)("select",{ref:l,className:"text-xs w-full",children:r.payload.android.map((e=>(0,Xt.jsx)("option",{value:e.id,children:e.name},e.id)))})}),(0,Xt.jsxs)("div",{className:"flex flex-row gap-1 text-sm p-2",children:[(0,Xt.jsx)(tr,{label:"Debug",onClick:()=>{const e=l.current;e?fetch(`http://localhost:3100/plugin-server/home/<USER>/runAndroid/${e.value||"none"}`,{method:"POST"}):console.error("No android device select item found")}}),(0,Xt.jsx)(tr,{label:"Build",onClick:()=>{const e=l.current;let t="";e?t=`?deviceId=${e.value}`:console.error("No android device select item found"),fetch(`http://localhost:3100/plugin-server/home/<USER>/build/android/release${t}`,{method:"POST"})}}),(0,Xt.jsx)(cr,{label:"Bundle Codepush",onClick:async()=>(await fetch(`http://localhost:3100/plugin-server/home/<USER>/bundleJSAndroid`,{method:"POST"}),Zn(o,!1,!1))})]})]}),(0,Xt.jsxs)("div",{className:"border p-1 rounded",children:[(0,Xt.jsx)("div",{children:"iOS"}),(0,Xt.jsx)("div",{className:"p-2",children:(0,Xt.jsx)("select",{ref:i,className:"text-xs w-full",children:r.payload.ios.map((e=>(0,Xt.jsx)("option",{value:e.id,children:e.name},e.id)))})}),(0,Xt.jsxs)("div",{className:"flex flex-row gap-1 text-sm p-2",children:[(0,Xt.jsx)(tr,{label:"Debug",onClick:()=>{const e=i.current;e?fetch(`http://localhost:3100/plugin-server/home/<USER>/runIOS/${e.value||"none"}`,{method:"POST"}):console.error("No ios device select item found")}}),(0,Xt.jsx)(tr,{label:"Build",onClick:()=>{const e=i.current;if(e){let t=`?deviceId=${e.value}`;fetch(`http://localhost:3100/plugin-server/home/<USER>/build/ios/release${t}`,{method:"POST"})}else console.error("No ios device select item found")}}),(0,Xt.jsx)(cr,{label:"Bundle Codepush",onClick:async()=>(await fetch(`http://localhost:3100/plugin-server/home/<USER>/bundleJsIOS`,{method:"POST"}),Zn(o,!1,!1))})]})]}),(0,Xt.jsxs)("div",{className:"border p-1 rounded",children:[(0,Xt.jsx)("div",{children:"Web"}),(0,Xt.jsx)(cr,{label:"Rebuild All",onClick:async()=>{await fetch(`http://localhost:3100/plugin-server/plugins/${s}/compileall`,{method:"POST"})}})]})]})},fr=Intl.DateTimeFormat("en-US",{dateStyle:"long",timeStyle:"short"}),pr=function(e){let{repoData:t,appData:r}=e;const[o,l]=(0,a.useState)("plugins"),[i,s]=(0,a.useState)({iosBundleId:"-1",androidBundleId:"-1",pluginsBundleId:"-1",navigatorsBundleId:"-1",publishedCommitId:"-1",comment:"",show:!1}),u=t.appId;(0,a.useEffect)((()=>{"success"===r.loadingInfo.status&&r.payload.manifest.forks.length>0&&s((e=>({...e,publishedCommitId:r.payload.manifest.forks[0].publishedCommitId.toString()})))}),[r,s]);let c=null,d=null;if(["inprogress","notstarted"].includes(r.loadingInfo.status))c=(0,Xt.jsx)("div",{children:"Loading..."}),d=(0,Xt.jsx)("div",{children:"Loading..."});else if("error"===r.loadingInfo.status)c=(0,Xt.jsxs)("div",{children:["Error happened: ",r.loadingInfo.errorMessage]}),d=(0,Xt.jsxs)("div",{children:["Error happened: ",r.loadingInfo.errorMessage]});else if("success"===r.loadingInfo.status){let e=r.payload.manifest.codeArtefacts;const t=["plugins","navigators","ios-jsbundle","android-jsbundle"],n=[];for(let r of t){let t=e.find((e=>e.type===r));t?n.push(t.id):n.push("NULL")}c=t.map(((e,t)=>{let r="";return e===o&&(r="border-orange-500"),(0,Xt.jsxs)("div",{className:`flex flex-col flex-1 border border-1 m-1 rounded justify-between ${r} cursor-pointer`,onClick:()=>l(e),children:[(0,Xt.jsx)("div",{className:"w-full flex justify-center text-center",style:{fontSize:8},children:e}),(0,Xt.jsx)("div",{className:"w-full flex justify-center",children:n[t]})]},e)})),c=(0,Xt.jsx)("div",{className:"flex flex-row",children:c}),d=r.payload.bundles.filter((e=>e.type===o)).map(((e,t)=>(0,Xt.jsxs)("div",{className:"flex flex-row justify-between w-full items-center px-2",children:[(0,Xt.jsx)("span",{children:e.id}),(0,Xt.jsxs)("div",{className:"flex flex-row gap-1",children:[(0,Xt.jsx)(tr,{label:"Log",onClick:()=>(e=>{if("success"===r.loadingInfo.status){const t=r.payload.bundles.find((t=>t.id===e));t&&"ios-jsbundle"===t.type?s((e=>({...e,show:!0,iosBundleId:t.id.toFixed()}))):t&&"android-jsbundle"===t.type?s((e=>({...e,show:!0,androidBundleId:t.id.toFixed()}))):t&&"plugins"===t.type?s((e=>({...e,show:!0,pluginsBundleId:t.id.toFixed()}))):t&&"navigators"===t.type&&s((e=>({...e,show:!0,navigatorsBundleId:t.id.toFixed()})))}})(e.id)}),(0,Xt.jsx)(cr,{label:"Publish",onClick:()=>(async e=>{await fetch(`http://localhost:3100/plugin-server/home/<USER>/bundles/${e}`,{method:"POST"}),window.parent.postMessage({type:"requestAppData"},"*")})(e.id)})]})]},t)))}const f=(e,r,a)=>{let o=[(0,Xt.jsx)("div",{className:"p-2",children:"no bundles yet"})];e.length>0&&(o=e.map((e=>(0,Xt.jsxs)("div",{className:"flex flex-row items-center justify-between px-2",children:[(0,Xt.jsx)("span",{children:fr.format(e.timestamp)}),(0,Xt.jsx)(cr,{label:"Upload",onClick:()=>(async(e,t)=>{await fetch(`http://localhost:3100/plugin-server/home/<USER>/uploadMobileBundle/${e}/${t}`,{method:"POST"}),window.parent.postMessage({type:"requestAppData"},"*")})(e.timestamp,r)})]},e.fullPath))));let l=null;return e.length>0&&(l=(0,Xt.jsx)("button",{className:"w-5",onClick:()=>rr(t.fullPath+`/remoteCode/generated/bundles/${r}`,"finder"),children:(0,Xt.jsx)("img",{alt:"open in finder",src:n(192),className:"w-full"})})),(0,Xt.jsxs)(Xt.Fragment,{children:[(0,Xt.jsxs)("div",{className:"flex flex-row items-center justify-between w-full pr-1",children:[a,l]}),(0,Xt.jsx)("div",{className:"max-h-24 overflow-y-auto border text-xs mb-2",children:o})]})},p=async()=>fetch(`http://localhost:3100/plugin-server/home/<USER>/pushLogs`,{method:"POST",headers:{"Content-type":"application/json"},body:JSON.stringify(i)}).then((e=>e.text())),h=()=>{s((e=>({...e,show:!1})))};let m=null;if(i.show){const e=(e,t)=>{s((n=>({...n,[e]:t})))};m=(0,Xt.jsxs)("div",{className:"flex flex-col",children:[(0,Xt.jsxs)("div",{className:"flex flex-row flex-wrap gap-1",children:[(0,Xt.jsxs)("div",{className:"flex flex-col border justify-between items-center",children:[(0,Xt.jsx)("span",{className:"w-full flex justify-center text-center",style:{fontSize:8},children:"ios"}),(0,Xt.jsx)("input",{className:"w-8",value:i.iosBundleId,onChange:t=>e("iosBundleId",t.target.value)})]}),(0,Xt.jsxs)("div",{className:"flex flex-col border justify-between items-center",children:[(0,Xt.jsx)("span",{className:"w-full flex justify-center text-center",style:{fontSize:8},children:"android"}),(0,Xt.jsx)("input",{className:"w-8",value:i.androidBundleId,onChange:t=>e("androidBundleId",t.target.value)})]}),(0,Xt.jsxs)("div",{className:"flex flex-col border justify-between items-center",children:[(0,Xt.jsx)("span",{className:"w-full flex justify-center text-center",style:{fontSize:8},children:"plugins"}),(0,Xt.jsx)("input",{className:"w-8",value:i.pluginsBundleId,onChange:t=>e("pluginsBundleId",t.target.value)})]}),(0,Xt.jsxs)("div",{className:"flex flex-col border justify-between items-center",children:[(0,Xt.jsx)("span",{className:"w-full flex justify-center text-center",style:{fontSize:8},children:"navigators"}),(0,Xt.jsx)("input",{className:"w-8",value:i.navigatorsBundleId,onChange:t=>e("navigatorsBundleId",t.target.value)})]}),(0,Xt.jsxs)("div",{className:"flex flex-col border justify-between items-center",children:[(0,Xt.jsx)("span",{className:"w-full flex justify-center text-center",style:{fontSize:8},children:"ota"}),(0,Xt.jsx)("input",{className:"w-8",value:i.publishedCommitId,onChange:t=>e("publishedCommitId",t.target.value)})]})]}),(0,Xt.jsxs)("div",{className:"flex flex-row gap-1",children:[(0,Xt.jsxs)("div",{className:"flex flex-1 flex-col border justify-between items-start",children:[(0,Xt.jsx)("span",{className:"w-full flex justify-center text-center",style:{fontSize:8},children:"comment"}),(0,Xt.jsx)("input",{className:"w-full",value:i.comment,onChange:t=>e("comment",t.target.value)})]}),(0,Xt.jsx)(cr,{label:"Send",onClick:p}),(0,Xt.jsx)(tr,{label:"Discard",onClick:h})]})]})}return(0,Xt.jsxs)("div",{className:"border p-1 rounded",children:[m,(0,Xt.jsx)("div",{children:"Published"}),c,(0,Xt.jsx)("div",{className:"max-h-24 overflow-y-auto border text-xs mb-2",children:d}),f(t.codePushBundles.ios,"ios","Local ios-jsbundles"),f(t.codePushBundles.android,"android","Local android-jsbundles"),(0,Xt.jsx)("div",{className:"flex flex-row items-center justify-between w-full pr-1",children:"Local web bundles"}),(0,Xt.jsx)("div",{className:"max-h-24 overflow-y-auto text-xs mb-2",children:(0,Xt.jsx)("div",{className:"max-h-24 overflow-y-auto border text-xs mb-2 p-2",children:(0,Xt.jsx)(cr,{label:"Upload Current WebBundles",onClick:async()=>{await fetch(`http://localhost:3100/plugin-server/home/<USER>/uploadWebBundle`,{method:"POST"}),window.parent.postMessage({type:"requestAppData"},"*")}})})})]})};const hr=Intl.DateTimeFormat("en-US",{dateStyle:"long",timeStyle:"short"});function mr(e){var t;const n=(0,a.useRef)(null),{commits:r}=e;let o=null;"notstarted"===r.loadingInfo.status?o=(0,Xt.jsx)("div",{children:"Not started yet! Stale data displayed"}):"inprogress"===r.loadingInfo.status?o=(0,Xt.jsx)("div",{children:"Loading... "}):"error"===r.loadingInfo.status&&(o=(0,Xt.jsxs)("div",{children:["Error: ",r.loadingInfo.errorMessage]}));let l=[(0,Xt.jsx)("div",{children:"No data!"})];return(null===(t=r.payload)||void 0===t?void 0:t.length)>0&&(l=r.payload.map((e=>(0,Xt.jsxs)("div",{className:"m-2 border",children:[(0,Xt.jsx)("div",{className:"text-sm",children:hr.format(new Date(e.createdAt))}),(0,Xt.jsxs)("div",{className:"text-sm",children:["(",e.id,")\xa0",e.remark]}),(0,Xt.jsxs)("div",{className:"flex flex-row justify-between",children:[e.isPublished?(0,Xt.jsx)("div",{className:"text-sm border",children:"Published"}):null,(0,Xt.jsx)("div",{className:"text-sm border",children:e.publishStatus})]})]},e.id)))),(0,Xt.jsxs)("div",{className:"border",children:[(0,Xt.jsxs)("div",{className:"flex flex-row",children:[(0,Xt.jsx)("input",{ref:n,placeholder:"message"}),(0,Xt.jsx)(cr,{label:"request appsave",onClick:()=>new Promise(((e,t)=>{if(n.current){const t=n.current.value;window.parent.postMessage({type:"requestAppConfigSave",message:t},"*"),setTimeout((()=>{e({})}),1e4)}else alert("whelp!! I give up!"),t()}))})]}),(0,Xt.jsx)(cr,{label:"refresh",onClick:e.onRefresh}),o,l]})}const vr=function(e){let{appId:t}=e;const[n,r]=(0,a.useState)({loadingInfo:{status:"notstarted",errorMessage:"",error:null},payload:[]}),o=async e=>(r((e=>({loadingInfo:{status:"inprogress",errorMessage:"",error:null},payload:e.payload}))),async function(e){return fetch(`http://localhost:3100/plugin-server/home/<USER>/commits`).then((e=>e.json()))}(e).then((e=>r({loadingInfo:{status:"success",errorMessage:"",error:null},payload:e}))).catch((e=>{r((t=>({loadingInfo:{status:"error",errorMessage:e.message,error:e},payload:t.payload})))})));return(0,a.useEffect)((()=>{o(t)}),[t]),(0,Xt.jsx)(mr,{commits:n,onRefresh:()=>o(t)})},gr=function(e){let{active:t,onClick:n,label:r}=e;const a=t?"border border-t-blue-500 border-b-none text-sm text-stone-800":"text-sm";return(0,Xt.jsx)("button",{className:a,onClick:n,children:r})},yr=function(){const[e,t]=(0,a.useState)("devactions"),r=(0,a.useContext)(Xn);let o=null;if(r&&r.currentAppRepoOffset>-1){const e=r.workspaceData.payload[0];o=null===e||void 0===e?void 0:e.repos[r.currentAppRepoOffset]}let l=null;l=r?-1===r.currentAppRepoOffset?(0,Xt.jsx)("div",{children:'repoOffset is -1. This probably means your apptile.config.json has the wrong "APP_ID"'}):o&&"devactions"===e?(0,Xt.jsx)(dr,{repoData:o,appData:r.appData,devicesData:r.devicesData}):o||"devactions"!==e?o&&"publishing"===e?(0,Xt.jsx)(pr,{repoData:o,appData:r.appData}):o||"publishing"!==e?"commits"===e?"success"===r.appData.loadingInfo.status?(0,Xt.jsx)(vr,{appId:r.appData.payload.manifest.uuid}):(0,Xt.jsx)("div",{className:"border",children:"Waiting for appData to load"}):(0,Xt.jsx)("div",{children:"This is unreachable codepath"}):(0,Xt.jsx)("div",{children:"There is no current app open. Try opening one in the workspace!"}):(0,Xt.jsx)("div",{children:"There is no current app open. Try opening one in the workspace!"}):(0,Xt.jsx)("div",{children:"Store is not initialized! Retry"});let i=null;return o&&(i=(0,Xt.jsxs)("div",{className:"flex flex-row justify-between items-center w-full p-1",children:[(0,Xt.jsx)("div",{className:"text-orange-500",children:o.name}),(0,Xt.jsx)("button",{onClick:()=>o&&rr(o.fullPath,"code"),children:(0,Xt.jsx)("img",{alt:"open in vscode",src:n(67),className:"w-8"})})]})),(0,Xt.jsxs)("div",{className:"w-80",children:[i,(0,Xt.jsxs)("div",{className:"flex flex-row gap-2",children:[(0,Xt.jsx)(gr,{label:"Dev Actions",active:"devactions"===e,onClick:()=>t("devactions")}),(0,Xt.jsx)(gr,{label:"Publishing",active:"publishing"===e,onClick:()=>t("publishing")}),(0,Xt.jsx)(gr,{label:"Commits",active:"commits"===e,onClick:()=>t("commits")})]}),l]})},br=()=>{var e;const[t,n]=a.useState(""),[r,o]=a.useState([]),[l,i]=a.useState(""),s=(0,a.useContext)(Xn),[u,c]=a.useState([]);let d=null;if(s&&s.currentAppRepoOffset>-1){const e=s.workspaceData.payload[0];d=null===e||void 0===e?void 0:e.repos[s.currentAppRepoOffset]}const f=`http://localhost:3100/plugin-server/webSDK/${null===(e=d)||void 0===e?void 0:e.appId}`,[p,h]=a.useState(null),[m,v]=a.useState(!1),[g,y]=a.useState(null),[b,w]=a.useState("newBundle");const x=async()=>{try{const e=await fetch(`${f}/bundles/${l}`),t=await e.json();c(t)}catch(e){console.error("Error during bundle fetching: ",e)}},k=async()=>{try{const e=await fetch(`${f}/live/${l}`),t=await e.json();h(t)}catch(e){console.error("Error during bundle fetching: ",e)}},S=e=>{const t=new Date(e);return`${String(t.getDate()).padStart(2,"0")}/${String(t.getMonth()+1).padStart(2,"0")}/${t.getFullYear()}, ${String(t.getHours()).padStart(2,"0")}:${String(t.getMinutes()).padStart(2,"0")}`};return(0,a.useEffect)((()=>{x(),k(),(async()=>{try{const e=await fetch(`${f}/partners`),t=await e.json();o(t)}catch(e){console.error("Error during bundle fetching: ",e)}})()}),[l]),(0,Xt.jsxs)("div",{className:"flex flex-col items-left justify-center border rounded border-gray-500 p-4 ml-10 ",style:{width:400},children:[(0,Xt.jsx)("h1",{className:"mb-2",children:"Web bundle manager"}),(0,Xt.jsxs)("div",{className:"mb-2",children:[(0,Xt.jsx)("label",{className:"mb-2 text-xs",children:"Select Partner:"}),(0,Xt.jsxs)("select",{className:" text-xs shadow appearance-none border rounded w-1/2 py-1 px-2 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",defaultValue:l,onChange:e=>{i(e.target.value),h(null),c([])},children:[(0,Xt.jsx)("option",{value:"",children:"Select a partner"}),r.map(((e,t)=>(0,Xt.jsx)("option",{value:e,children:e},t)))]})]}),p&&(0,Xt.jsxs)("div",{className:"mb-2",children:[(0,Xt.jsx)("p",{className:"text-green-50 text-xs",children:"Current live bundle"}),(0,Xt.jsx)("p",{className:"text-xs",children:JSON.stringify({id:p.artifactId,partner:p.partner})})]}),(0,Xt.jsxs)("div",{className:"flex",children:[(0,Xt.jsx)(tr,{onClick:()=>{w("newBundle"),v(!1)},className:"newBundle"===b?"bg-blue-500 text-white text-xs":"text-xs",label:"New Bundle"}),(0,Xt.jsx)(tr,{onClick:()=>{w("liveBundle"),x(),k(),y(null)},className:"liveBundle"===b?"bg-blue-500 text-white text-xs":"text-xs",label:"Bundle manager"})]}),(0,Xt.jsxs)("div",{className:"mt-4 overflow-y-auto",style:{height:400},children:["newBundle"===b&&(0,Xt.jsxs)("div",{children:[(0,Xt.jsxs)("div",{className:"mb-2",children:[(0,Xt.jsx)("label",{className:"mb-2 text-xs",children:"Entry file path:"}),(0,Xt.jsx)("input",{type:"text",className:" text-xs shadow appearance-none border rounded w-full py-1 px-2 text-gray-700 leading-tight focus:outline-none focus:shadow-outline",onChange:async function(e){n(e.target.value)}})]}),(0,Xt.jsx)(tr,{className:"text-xs",onClick:async()=>{try{await fetch(`http://localhost:3100/plugin-server/webSDK//compile?sourceFolder=${t}`),v(!0)}catch(e){console.error("Error during compilation: ",e)}},label:"Compile UMD bundle"}),(0,Xt.jsx)(tr,{disabled:!m||!l,className:"text-xs",onClick:async()=>{try{const e=await fetch(`${f}/upload/${l}`);y(await e.json()),v(!1)}catch(e){console.error("Error during upload: ",e)}},label:"Upload to S3"}),g&&(0,Xt.jsxs)("p",{className:"text-green-500",children:["Upload success! with ID: ",null===g||void 0===g?void 0:g.id]})]}),"liveBundle"===b&&(0,Xt.jsxs)("div",{className:"flex flex-col ",children:[(0,Xt.jsx)("h2",{className:"mb-2",children:"Available bundles"}),(0,Xt.jsx)("ul",{className:"mb-2",children:u.length>0?u.map(((e,t)=>(0,Xt.jsxs)("li",{className:"mb-2 border p-2 text-xs",children:[JSON.stringify({id:e.id,createdAt:S(e.createdAt),updatedAt:S(e.updatedAt),comment:e.comment,partner:e.partner}),(0,Xt.jsx)(tr,{onClick:()=>{(async e=>{try{window.parent.postMessage({type:"setWebSdkArtifactId",artifactId:e},"*")}catch(t){console.error("Error during making bundle local live: ",t)}})(e.id.toString())},label:"Local live"}),(0,Xt.jsx)(tr,{onClick:()=>{(async e=>{try{await fetch(`${f}/live/${e}`,{method:"POST"}),k()}catch(t){console.error("Error during making bundle live: ",t)}})(e.id.toString())},className:(null===p||void 0===p?void 0:p.artifactId)===e.id?"bg-green-500 text-white":"",label:(null===p||void 0===p?void 0:p.artifactId)===e.id?"Live":"Global live"})]},t))):(0,Xt.jsx)("li",{children:"No bundles available"})})]})]})]})},wr=function(e,t){return oe({basename:null==t?void 0:t.basename,future:Et({},null==t?void 0:t.future,{v7_prependBasename:!0}),history:(n={window:null==t?void 0:t.window},void 0===n&&(n={}),g((function(e,t){let{pathname:n,search:r,hash:a}=e.location;return h("",{pathname:n,search:r,hash:a},t.state&&t.state.usr||null,t.state&&t.state.key||"default")}),(function(e,t){return"string"===typeof t?t:m(t)}),null,n)),hydrationData:(null==t?void 0:t.hydrationData)||Pt(),routes:e,mapRouteProperties:St,dataStrategy:null==t?void 0:t.dataStrategy,patchRoutesOnNavigation:null==t?void 0:t.patchRoutesOnNavigation,window:null==t?void 0:t.window}).initialize();var n}([{path:"/",element:(0,Xt.jsx)(er,{}),children:[{path:"workspaces",element:(0,Xt.jsx)(or,{}),children:[{path:"",element:(0,Xt.jsx)(ar,{})},{path:":workspaceId/app/:appId",element:(0,Xt.jsx)(ir,{})}]},{path:"currentApp",element:(0,Xt.jsx)(yr,{}),children:[{path:"",element:(0,Xt.jsx)("div",{children:"publishing"})},{path:"actions",element:(0,Xt.jsx)("div",{children:"development"})}]},{path:"socketClients",element:(0,Xt.jsx)(ur,{})},{path:"webSdk",element:(0,Xt.jsx)(br,{})}]}],{basename:"/plugin-server/public/ui"});l.createRoot(document.getElementById("root")).render((0,Xt.jsx)(a.StrictMode,{children:(0,Xt.jsx)(At,{router:wr})})),Qt(),async function(){const e=[500,1e3,2e3,3e3,5e3,1e4,1e4,...new Array(100).fill(6e5)];let t=0,n=null,r=!1,a=null;(function o(l){if(l)if(null===a){const n=e[t];t=(t+1)%e.length,console.log("Queueing connection request with delay",n),a=setTimeout((()=>{a=null,o(!1)}),n)}else console.log("Not queuing a connection request because one is already in queue");else r?console.log("Ignoring connection request as one is already in progress"):(console.log("Trying to connect to: ",Kt),n&&(n.close(),n=null),n=new WebSocket(Kt),r=!0,n.onerror=e=>{var t;r=!1,console.error("Socket error",e),null===(t=n)||void 0===t||t.close()},n.onclose=()=>{console.log("Connection was closed! Will retry"),o(!0)},n.onopen=()=>{var e;r=!1,console.log("Socket is open"),null===(e=n)||void 0===e||e.send('{"type": "register", "kind": "cliui"}')},n.onmessage=async e=>{const t=e.data;let n;try{n=JSON.parse(t.toString())}catch(r){console.log("Unparsable message: ",r)}if("status"===n.type){const e=Yt.get("status");e&&e.forEach((e=>e(n)))}else if("log"===n.type){qt=qt+"\n"+n.message,qt.length>1e5&&(qt=qt.slice(qt.length-1e5));const e=Yt.get("onLog");e&&e.forEach((e=>e(qt)))}else console.log("socket message: ",t)})})(!1)}()})();
//# sourceMappingURL=main.ad1a955c.js.map