import { connectWidget } from 'apptile-core';
import {ReactComponent, WidgetConfig, WidgetEditors, PropertySettings} from './component'

const pluginListing = {
  labelPrefix: '__LABEL_PREFIX__',
  type: 'widget',
  name: '__LISTING_NAME__',
  description: '__DISPLAY_DESCRIPTION__',
  layout: {
    width: 50,
    height: 30,
  },
  section: 'SDK',
  icon: '__LISTING_ICON__',
  manifest: {
    directoryName: '__PLUGIN_REGISTRY_NAME__',
  }
};

export default connectWidget('__PLUGIN_REGISTRY_NAME__', 
  ReactComponent, 
  WidgetConfig, 
  null, 
  WidgetEditors, 
  {
    propertySettings: PropertySettings,
    widgetStyleConfig: [],
    pluginListing,
    docs: {},
  }
);
