import React, {<PERSON>} from 'react';
import {Outlet, NavLink} from 'react-router-dom';
import SocketLogs from './components/SocketLogs';

const MainLayout:FC = function MainLayout() {
  const navLinkClass = ({isActive}: {isActive: boolean}) => {
    if (isActive) {
      return "text-indigo-500 border rounded border-indigo-500 border-2 w-full";
    } else {
      return "text-stone-500 w-full";
    }
  }

  const links = [
    { path: '/workspaces', label: 'Workspaces' },
    { path: '/currentApp', label: 'CurrentApp' },
    { path: '/socketclients', label: 'Clients'},
    {path: 'webSdk', label: 'WebSDK'}
  ].map(it => {
    return (<NavLink 
      key={it.path}
      to={it.path}
      className={navLinkClass}
    >
      <div className="h-10 flex items-center justify-center">
        <span style={{fontSize: 8}}>{it.label}</span>
      </div>
    </NavLink>);
  });

  return (<div className="grid grid-cols-[50px,1fr,1fr]">
      <div className="flex flex-col items-start">
        {/* <h2 className="text-2xl font-default text-orange-500">apptile-cli</h2> */}
        {links}
      </div>
      <div className="flex flex-col items-start">
        <Outlet />
      </div>
      <SocketLogs />
    </div> 
  );
}

export default MainLayout;
