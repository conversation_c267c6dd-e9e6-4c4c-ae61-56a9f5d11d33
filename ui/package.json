{"name": "ui", "version": "0.1.0", "private": true, "homepage": "/plugin-server/public/ui", "dependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.119", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "immer": "^10.1.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.28.0", "react-scripts": "5.0.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "export $(cat .env.production | xargs) && react-scripts build && cp -r build ../public/ui && cp -r build ../dist/public/ui", "build-local": "export $(cat .env.local | xargs) && react-scripts build && cp -r build ../public/ui && cp -r build ../dist/public/ui", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"tailwindcss": "^3.4.15"}}