// import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
// import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
// import { z } from "zod";
// import dotenv from "dotenv";
// import { getOpenApp } from "../../../../database/cliconfig";
// import { apptileContextZodSchema, makeHeadersWithCookie, getMidUrlIntegrationsValue } from "../../../../utils";
// import {getAppConfigsFromFS } from "../../../workspace";
// import {Logger} from '../../../../utils/logger';

// dotenv.config();

// type Creds = {
//   SUPABASE_ACCESS_TOKEN: string;
//   SUPABASE_REFRESH_TOKEN: string;
//   ACCESS_TOKEN_EXPIRES_IN: number;
//   SUPABASE_PROJECT_REF: string;
//   SUPABASE_ANON_KEY?: string;
//   APPTILE_SUPABASE_CLIENT_ID?: string;
//   APPTILE_SUPABASE_CLIENT_SECRET?: string;
// }

// const logger = new Logger({filePath: __dirname + "/logs/supabase-mcp-server-logs.txt"});
// let supabaseUrl = process.env.SUPABASE_URL;
// let supabaseAnonKey = process.env.SUPABASE_SERVICE_ANON_KEY;
// let supabaseProjectRef = process.env.SUPABASE_PROJECT_REF;
// let supabaseAuthToken = process.env.SUPABASE_ACCESS_TOKEN;
// let supabaseRefreshToken = process.env.SUPABASE_REFRESH_TOKEN;
// let apptileSupabaseClientId: string;
// let apptileSupabaseClientSecret: string;

// // const useFetchCredsFunction = process.env.USE_FETCH_CREDS_FUNCTION === "true";
// const useFetchCredsFunction = true;

// const fetchCredsFunction = async (appId: string | undefined) => {
//   if (!appId) {
//     throw new Error("App ID is required");
//   }
//   const openApp = await getOpenApp(appId);
//   if (!openApp) {
//     throw new Error("Open app not found");
//   }
//   const header = makeHeadersWithCookie(openApp.apptilecookie, {});
//   const apptileConfig = await getAppConfigsFromFS(appId);
//   process.env.NODE_TLS_REJECT_UNAUTHORIZED = "0";
//   try {
//     const response = await fetch(
//       `${apptileConfig.APPTILE_BACKEND_URL}/${getMidUrlIntegrationsValue()}/${appId}/appIntegrations/supabase/credentials`,
//       {
//         method: "GET",
//         headers: { ...header.headers },
//       }
//     );
//     const credsData = await response.json();
//     supabaseUrl = `https://${credsData.SUPABASE_PROJECT_REF}.supabase.co`;
//     supabaseAnonKey = credsData.SUPABASE_ANON_KEY;
//     supabaseProjectRef = credsData.SUPABASE_PROJECT_REF;
//     supabaseAuthToken = credsData.SUPABASE_ACCESS_TOKEN;
//     supabaseRefreshToken = credsData.SUPABASE_REFRESH_TOKEN;
//     apptileSupabaseClientId = credsData.APPTILE_SUPABASE_CLIENT_ID;
//     apptileSupabaseClientSecret = credsData.APPTILE_SUPABASE_CLIENT_SECRET;
//   } catch (error: any) {
//     console.error("Fetch failed with error: ", error);
//     throw new Error(error);
//   }
// };

// const updateApptileCreds = async (appId: string, creds: Creds) => {
//   const openApp = await getOpenApp(appId);
//   if (!openApp) {
//     throw new Error("Open app not found");
//   }
//   const header = makeHeadersWithCookie(openApp.apptilecookie, {});
//   const apptileConfig = await getAppConfigsFromFS(appId);
//   process.env.NODE_TLS_REJECT_UNAUTHORIZED = "0";
//   try {
//     await fetch(
//       `${apptileConfig.APPTILE_BACKEND_URL}/${getMidUrlIntegrationsValue()}/${appId}/appIntegrations/supabase/credentials?isPlatformType=true`,
//       {
//         method: "POST",
//         headers: { ...header.headers, "Content-Type": "application/json" },
//         body: JSON.stringify(creds),
//       }
//     );
//     await fetchCredsFunction(appId);
//     logger.info("Update creds successful");
//   } catch (error: any) {
//     console.error("Fetch failed with error: ", error);
//     throw new Error(error);
//   }
// };

// export const refreshAccessToken = async (appId: string) => {
//   if (!supabaseRefreshToken) {
//     console.error("No refresh token found");
//     return;
//   }
//   try {
//     const formData = new URLSearchParams();
//     formData.append("grant_type", "refresh_token");
//     formData.append("refresh_token", supabaseRefreshToken);

//     const response = await fetch("https://api.supabase.com/v1/oauth/token", {
//       method: "POST",
//       headers: {
//         "Content-Type": "application/x-www-form-urlencoded",
//         Authorization: `Basic ${btoa(
//           `${apptileSupabaseClientId}:${apptileSupabaseClientSecret}`
//         )}`,
//       },
//       body: formData.toString(),
//     });
//     const data = await response.json();

//     await updateApptileCreds(appId, {
//       APPTILE_SUPABASE_CLIENT_ID: apptileSupabaseClientId,
//       APPTILE_SUPABASE_CLIENT_SECRET: apptileSupabaseClientSecret,
//       SUPABASE_ACCESS_TOKEN: data.access_token,
//       SUPABASE_PROJECT_REF: supabaseProjectRef as string,
//       SUPABASE_REFRESH_TOKEN: data.refresh_token,
//       SUPABASE_ANON_KEY: supabaseAnonKey,
//       ACCESS_TOKEN_EXPIRES_IN: data.expires_in,
//     });
//   } catch (error) {
//     logger.error("Update creds unsuccessful: ", error);
//     return formatReturn("STOP Execution. Refresh token failed! Continue without Supabase DB.")
//   }
// };

// const supabaseSqlQueryRunner = async (query: string) => {
//   try {
//     const data = await fetch(
//       `https://api.supabase.com/v1/projects/${supabaseProjectRef}/database/query`,
//       {
//         method: "POST",
//         headers: {
//           "Content-Type": "application/json",
//           Authorization: `Bearer ${supabaseAuthToken}`,
//         },
//         body: JSON.stringify({
//           query,
//         }),
//       }
//     );
//     const response = await data.json();
//     if (response.message === "Unauthorized") {
//       console.error(
//         "Supabase access token expired. Refreshing token required"
//       ); 
//       throw new Error('Unauthorized, Access token expired');
//     }
//     if (response.error) {
//       throw new Error(response.error.message);
//     }
//     return response;
//   } catch (error) {
//     console.error("Error executing SQL query:", error);
//     throw error;
//   }
// };

// const checkIfCredsExistsAndValid = async (appId: string) => {
//   logger.info("Checking if creds exists");
//   logger.info(
//     `Found creds: 
//     1. supabaseUrl: ${supabaseUrl} 
//     2. supabaseAnonKey: ${supabaseAnonKey}
//     3. supabaseProjectRef: ${supabaseProjectRef}
//     4. SupabaseAuthToken: ${supabaseAuthToken}    
//     `
//   );
//   if (
//     !supabaseUrl ||
//     !supabaseAnonKey ||
//     !supabaseProjectRef ||
//     !supabaseAuthToken
//   ) {
//     console.error(
//       "Supabase URL, Anon Key, Project Ref, and Auth Token must be provided"
//     );
//     process.exit(1);
//   }
// try {
//   //test call to check if the creds a valid
//   logger.info("Doing a test call to check if valid");
//   await supabaseSqlQueryRunner(`SELECT schema_name FROM information_schema.schemata`);
// } catch (error) {
//   //Refreshing the token if invalid creds
//   logger.info('Supabase un-authorized. Refreshing token');
//   await refreshAccessToken(appId);
// }
  
// };

// const server = new McpServer(
//   {
//     name: "supabase-mcp-server",
//     version: "1.0.0",
//   },
//   {
//     capabilities: {
//       tools: {
//         database: "Perform structured database operations on Supabase",
//         authentication: "Handle user authentication via Supabase Auth",
//         storage: "Manage file storage in Supabase buckets",
//       },
//     },
//   }
// );

// type McpContent =
//   | { type: "text"; text: string }
//   | { type: "image"; data: string; mimeType: string }
//   | { type: "resource"; resource: { text: string; uri: string; mimeType?: string } };

// const formatReturn = <T>(result: T): { content: McpContent[] } => {
//   if (Array.isArray(result)) {
//     return {
//       content: result.map((item) => ({
//         type: "text",
//         text: JSON.stringify(item),
//       })),
//     };
//   }

//   return {
//     content: [{ type: "text", text: JSON.stringify(result) }],
//   };
// };

// server.tool(
//   "database_operations",
//   "Perform structured operations like fetching, inserting, updating, deleting records, creating tables, managing row level security, or retrieving schema information in Supabase.",
//   {
//     action: z.enum(["fetch", "insert", "update", "delete", "create_table", "get_schemas", "get_tables", "get_table_schema", "delete_table", "enable_rls", "disable_rls", "create_rls_policy"]),
//     table: z.string().describe("Target table name.").optional(),
//     apptile_context: apptileContextZodSchema,
//     table_schema: z.string().optional().describe("Table schema name. To fetch the tables from a specific schema."),
//     filters: z
//       .record(z.string(), z.any())
//       .optional()
//       .describe("Filter criteria for fetching, updating, or deleting records."),
//     data: z
//       .array(z.record(z.string(), z.any()))
//       .optional()
//       .describe("Data payload for insert or update operations."),
//     schema: z
//       .array(
//         z.object({
//           column: z.string(),
//           type: z.string(),
//           constraints: z.string().optional(),
//           primary_key: z.boolean().optional(),
//           auto_increment: z.boolean().optional(),
//           unique: z.boolean().optional(),
//         })
//       )
//       .optional()
//       .describe("Table schema definition for table creation."),
//     policy_details: z
//       .object({
//         policy_name: z.string().describe("A unique name for the RLS policy"),
//         policy_action: z.enum(["ALL", "SELECT", "INSERT", "UPDATE", "DELETE"]).optional().describe("The operation the policy applies to (defaults to ALL)"),
//         policy_definition: z.string().describe("The SQL condition that defines when the policy applies"),
//         policy_role: z.string().optional().describe("The database role the policy applies to (defaults to 'authenticated')")
//       })
//       .optional()
//       .describe("Details for creating Row Level Security policies"),
//   },
//   async (inputVars) => {
//     logger.info("Connected to database operations: ", inputVars);
//     const appId = inputVars.apptile_context!.app_id ;
//     if (useFetchCredsFunction && appId) await fetchCredsFunction(inputVars.apptile_context!.app_id);
//     await checkIfCredsExistsAndValid(appId);
//     const { action, table, filters, data, schema, table_schema } = inputVars;
//     try {
//       switch (action) {
//         case "fetch": {
//           try {
//             if (!table) {
//               throw new Error("Table name is required for fetch operation");
//             }
//             let query = `SELECT * FROM ${table}`;
//             if (filters) {
//               query += ` WHERE ${Object.entries(filters)
//                 .map(([key, value]) => `${key} = '${value}'`)
//                 .join(" AND ")}`;
//             }
//             const data = await supabaseSqlQueryRunner(query);
//             return formatReturn(data);
//           } catch (error: any) {
//             console.error("Error fetching data:", error);
//             return formatReturn({
//               error: "Error fetching data: " + error.message,
//             });
//           }
//         }
//         case "insert": {
//           try {
//             if (!table || !data) {
//               return formatReturn({
//                 error: "Table name and data are required for insert operation",
//               });
//             }
//             await supabaseSqlQueryRunner(
//               `INSERT INTO ${table} (${Object.keys(data[0]).join(
//                 ", "
//               )}) VALUES ${data
//                 .map(
//                   (row) =>
//                     `(${Object.values(row)
//                       .map((value) => `'${value}'`)
//                       .join(", ")})`
//                 )
//                 .join(", ")}`
//             );
//             return formatReturn("Insert Successful");
//           } catch (error: any) {
//             console.error("Error inserting data:", error);
//             return formatReturn({
//               error: "Error inserting data: " + error.message,
//             });
//           }
//         }
//         case "update": {
//           try {
//             if (!table || !data) {
//               return formatReturn({
//                 error: "Table name and data are required for update operation",
//               });
//             }
//             if (!filters) {
//               return formatReturn({
//                 error: "Filters are required for update operation",
//               });
//             }
//             await supabaseSqlQueryRunner(
//               `UPDATE ${table} SET ${Object.entries(data)
//                 .map(([key, value]) => `${key} = '${value}'`)
//                 .join(", ")} WHERE ${Object.entries(filters)
//                 .map(([key, value]) => `${key} = '${value}'`)
//                 .join(" AND ")}`
//             );
//             return formatReturn("update successful");
//           } catch (error: any) {
//             console.error("Error updating data:", error);
//             return formatReturn({
//               error: "Error updating data: " + error.message,
//             });
//           }
//         }
//         case "delete": {
//           if (!table) {
//             return formatReturn({
//               error: "Table name is required for delete operation",
//             });
//           }
//           if (!filters) {
//             return formatReturn({
//               error: "Filters are required for delete operation",
//             });
//           }
//           await supabaseSqlQueryRunner(
//             `DELETE FROM ${table} WHERE ${Object.entries(filters)
//               .map(([key, value]) => `${key} = '${value}'`)
//               .join(" AND ")}`
//           );
//           return formatReturn("Delete successful");
//         }
//         case "delete_table": {
//           if (!table) {
//             return formatReturn({
//               error: "Table name is required for delete_table operation",
//             });
//           }
//           try {
//             await supabaseSqlQueryRunner(`DROP TABLE IF EXISTS ${table}`);
//             return formatReturn("Table deleted successfully");
//           } catch (error: any) {
//             console.error("Error deleting table:", error);
//             return formatReturn({
//               error: "Error deleting table: " + error.message,
//             });
//           }
//         }
//         case "create_table": {
//           //USe the project query to create a new table
//           if (!table || !schema) {
//             return formatReturn({
//               error:
//                 "Table name and schema are required for create_table operation",
//             });
//           }
//           const createTableQuery = `CREATE TABLE ${table} (
//             ${schema
//               .map((col) => {
//                 // Handle PostgreSQL specific types
//                 let typeStr = col.type;

//                 // Replace AUTO_INCREMENT with SERIAL type
//                 if (col.auto_increment) {
//                   if (col.type.toLowerCase() === "integer") {
//                     typeStr = "SERIAL";
//                   } else if (col.type.toLowerCase() === "bigint") {
//                     typeStr = "BIGSERIAL";
//                   }
//                 }

//                 // Ensure varchar has length
//                 if (
//                   col.type.toLowerCase() === "varchar" &&
//                   !col.type.includes("(")
//                 ) {
//                   typeStr = "varchar(255)";
//                 }

//                 // Ensure enum has options
//                 if (col.type.toLowerCase() === "enum") {
//                   // You need to replace this with actual enum values
//                   typeStr = "varchar(50)"; // Consider replacing enum with varchar or text
//                 }

//                 return `${col.column} ${typeStr}${
//                   col.constraints ? ` ${col.constraints}` : ""
//                 }${col.primary_key ? " PRIMARY KEY" : ""}${
//                   col.unique ? " UNIQUE" : ""
//                 }`;
//               })
//               .join(", ")}
//           )`;
//           try {
//             await supabaseSqlQueryRunner(createTableQuery);
//             return formatReturn("Table created successfully");
//           } catch (error: any) {
//             console.error("Error creating table:", error);
//             return formatReturn({
//               error: "Error creating table: " + error.message,
//             });
//           }
//         }
//         case "get_schemas": {
//           try {
//             const data = await supabaseSqlQueryRunner(
//               "SELECT schema_name FROM information_schema.schemata"
//             );
//             return formatReturn(data.map((schema: any) => schema.schema_name));
//           } catch (error: any) {
//             console.error("Error fetching schemas:", error);
//             return formatReturn({
//               error: "Error fetching schemas: " + error.message,
//             });
//           }
//         }
//         case "get_tables": {
//           logger.info('From get table: ')
//           if (!table_schema) {
//             return formatReturn({
//               error: "Schema name is required for get_tables operation",
//             });
//           }
//           try {
//             const data = await supabaseSqlQueryRunner(
//               `SELECT table_name FROM information_schema.tables WHERE table_schema='${table_schema}'`
//             );
//             logger.info("Data is: ", data);
//             return formatReturn(data.map((table: any) => table.table_name));
//           } catch (error: any) {
//             console.error("Error fetching tables:", error);
//             return formatReturn({
//               error: "Error fetching tables: " + error.message,
//             });
//           }
//         }
//         case "get_table_schema": {
//           if (!table) {
//             return formatReturn({
//               error: "Table name is required for get_table_schema operation",
//             });
//           }
//           try {
//             const data = await supabaseSqlQueryRunner(
//               `SELECT column_name, data_type, is_nullable FROM information_schema.columns WHERE table_name='${table}'`
//             );
//             return formatReturn(data);
//           } catch (error: any) {
//             console.error("Error fetching table schema:", error);
//             return formatReturn({
//               error: "Error fetching table schema: " + error.message,
//             });
//           }
//         }
//         case "enable_rls": {
//           if (!table) {
//             return formatReturn({
//               error: "Table name is required for enable_rls operation",
//             });
//           }
//           try {
//             // Enable Row Level Security on the table
//             await supabaseSqlQueryRunner(
//               `ALTER TABLE ${table} ENABLE ROW LEVEL SECURITY;`
//             );
//             return formatReturn("Row Level Security enabled successfully for table: " + table);
//           } catch (error: any) {
//             console.error("Error enabling RLS:", error);
//             return formatReturn({
//               error: "Error enabling RLS: " + error.message,
//             });
//           }
//         }
//         case "disable_rls": {
//           if (!table) {
//             return formatReturn({
//               error: "Table name is required for disable_rls operation",
//             });
//           }
//           try {
//             // Disable Row Level Security on the table
//             await supabaseSqlQueryRunner(
//               `ALTER TABLE ${table} DISABLE ROW LEVEL SECURITY;`
//             );
//             return formatReturn("Row Level Security disabled successfully for table: " + table);
//           } catch (error: any) {
//             console.error("Error disabling RLS:", error);
//             return formatReturn({
//               error: "Error disabling RLS: " + error.message,
//             });
//           }
//         }
//         case "create_rls_policy": {
//           try {
//             if (!table) {
//               return formatReturn({
//                 error: "Table name is required for create_rls_policy operation",
//               });
//             }
            
//             // Check if policy details are provided either in filters or policy_details
//             let policyName: string | undefined;
//             let policyAction: string | undefined;
//             let policyDefinition: string | undefined;
//             let policyRole: string | undefined;
            
//             if (inputVars.policy_details) {
//               // Use policy_details if provided
//               policyName = inputVars.policy_details.policy_name;
//               policyAction = inputVars.policy_details.policy_action || 'ALL';
//               policyDefinition = inputVars.policy_details.policy_definition;
//               policyRole = inputVars.policy_details.policy_role || 'authenticated';
//             } else if (filters) {
//               // Fallback to filters for backward compatibility
//               policyName = filters.policy_name as string;
//               policyAction = filters.policy_action as string || 'ALL';
//               policyDefinition = filters.policy_definition as string;
//               policyRole = filters.policy_role as string || 'authenticated';
//             }
            
//             if (!policyName || !policyDefinition) {
//               return formatReturn({
//                 error: "Policy name and definition are required for create_rls_policy operation",
//               });
//             }
              
//             // Create a policy for the table
//             await supabaseSqlQueryRunner(
//               `CREATE POLICY "${policyName}" ON ${table} 
//                FOR ${policyAction} 
//                TO ${policyRole} 
//                USING (${policyDefinition});`
//             );
              
//             return formatReturn(`Policy "${policyName}" created successfully for table: ${table}`);
//           } catch (error: any) {
//             console.error("Error creating RLS policy:", error);
//             return formatReturn({
//               error: "Error creating RLS policy: " + error.message,
//             });
//           }
//         }
//         default:
//           return formatReturn("Invalid action");
//       }
//     } catch (error: any) {
//       return formatReturn({ error: error.message });
//     }
//   }
// );

// server.tool(
//   "authentication_api_generator",
//   "Receive authentication related api endpoints for signup, signin, signout, and get user.",
//   {
//     action: z.enum(["signup", "signin", "signout", "get_user"]),
//     apptile_context: apptileContextZodSchema
//   },
//   async (inputVars) => {
//     logger.info("Connected to auth api generator: ", inputVars);
//     if (useFetchCredsFunction && inputVars.apptile_context!.app_id) await fetchCredsFunction(inputVars.apptile_context!.app_id);
//     checkIfCredsExistsAndValid(inputVars.apptile_context!.app_id);
//     const { action } = inputVars;
//     try {
//       switch (action) {
//         case "signup": {
//           return formatReturn({
//             api_endpoint: `${supabaseUrl}/auth/v1/signup`,
//             method: "POST",
//             headers: {
//               apikey: supabaseAnonKey,
//               "Content-Type": "application/json",
//             },
//             body: JSON.stringify("auth_credentials"),
//           });
//         }
//         case "signin": {
//           return formatReturn({
//             api_endpoint: `${supabaseUrl}/auth/v1/token?grant_type=password`,
//             method: "POST",
//             headers: {
//               apikey: supabaseAnonKey,
//               "Content-Type": "application/json",
//             },
//             body: JSON.stringify("auth_credentials"),
//           });
//         }
//         case "signout": {
//           return formatReturn({
//             api_endpoint: `${supabaseUrl}/auth/v1/logout`,
//             method: "POST",
//             headers: {
//               apikey: supabaseAnonKey,
//               Authorization: `Bearer {User's JWT token}`,
//             },
//           });
//         }
//         case "get_user": {
//           return formatReturn({
//             api_endpoint: `${supabaseUrl}/auth/v1/user`,
//             method: "GET",
//             headers: {
//               apikey: supabaseAnonKey,
//               Authorization: `Bearer {User's JWT token}`,
//             },
//           });
//         }
//       }
//     } catch (error: any) {
//       return formatReturn({ error: error.message });
//     }
//   }
// );

// server.tool(
//   "database_api_generator",
//   "Generate API endpoints for fetching, inserting, updating, deleting records, or creating tables in Supabase.",
//   {
//     action: z.enum(["fetch", "insert", "update", "delete", "create_table"]),
//     table: z.string().describe("Target table name."),
//     apptile_context: apptileContextZodSchema,
//     filters: z
//       .record(z.string(), z.any())
//       .optional()
//       .describe("Filter criteria for fetching, updating, or deleting records."),
    
//   },
//   async (inputVars) => {
//     if (useFetchCredsFunction && inputVars.apptile_context!.app_id) await fetchCredsFunction(inputVars.apptile_context!.app_id);
//     checkIfCredsExistsAndValid(inputVars.apptile_context!.app_id);
//     const { action, table, filters } = inputVars;
//     if (!supabaseUrl || !supabaseAnonKey) {
//       return formatReturn("Supabase URL and Key must be provided");
//     }
//     let queryParams = filters ? new URLSearchParams(filters).toString() : "";
//     let apiUrl = `${supabaseUrl}/rest/v1/${table}`;
//     let method = "GET";
//     let body = null;

//     switch (action) {
//       case "fetch": {
//         apiUrl += `?${queryParams}`;
//         break;
//       }
//       case "insert": {
//         method = "POST";
//         break;
//       }
//       case "update": {
//         method = "PATCH";
//         apiUrl += `?${queryParams}`;
//         break;
//       }
//       case "delete": {
//         method = "DELETE";
//         apiUrl += `?${queryParams}`;
//         break;
//       }
//     }
//     return formatReturn({
//       api_endpoint: apiUrl,
//       method: method,
//       headers: {
//         apikey: supabaseAnonKey,
//         "Content-Type": "application/json",
//       },
//       body: body,
//     });
//   }
// );

// server.tool(
//   "supabase_auth_keys",
//   "Get supabase api key and base url for rest apis. While interacting on the client side", 
//   {
//     action: z.enum(["get_supabase_rest_api_key_and_base_url"]),
//   },
//   async (inputVars) => {
//     switch(inputVars.action){
//       case "get_supabase_rest_api_key_and_base_url": {
//         return formatReturn({
//           api_endpoint: `${supabaseUrl}/rest/v1`,
//           api_key: supabaseAnonKey,
//         })
//       }
//     }
//   }
// );

// async function main() {
//   const transport = new StdioServerTransport();
//   await server.connect(transport);
//   logger.error("Supabase MCP Server running on stdio");
// }

// main().catch((error) => {
//   logger.error("Fatal error in main():", error);
//   process.exit(1);
// });
