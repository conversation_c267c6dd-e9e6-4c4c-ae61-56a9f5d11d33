import React from 'react';
import { View, Text, Image } from 'react-native';
import { useApptileWindowDims } from 'apptile-core';

export function ReactComponent({ model }) {
  const id = model.get('id');
  const {width, height} = useApptileWindowDims();
  return (
    <View
      nativeID={'rootElement-' + id}
      style={{
        height: height - (2 * 20),
        flex: 'unset',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#00000010',
        margin: 20,
        borderRadius: 25,
        paddingLeft: 80,
        paddingRight: 80
      }}
    >
      <Text
        nativeID={'Text-__PLUGIN_REGISTRY_NAME__-welcometext'}
        style={{
          fontSize: 60,
          textAlign: 'center'
        }}
      >
       Working on the design
      </Text>
      <Image 
        nativeID={'Image-__PLUGIN_REGISTRY_NAME__-loadingindicator'}
        source={{uri: 'https://cdn-demo.apptile.io/621ca46b-031f-4e49-9d51-ca80fd746a36/9486e0c8-bcc0-413d-a556-cab0647a0f74/original.png'}}
        style={{width: 55, height: 55, transform: [{rotate: "45deg"}]}}
      />
    </View>
  );
}

export const WidgetConfig = {
};

export const WidgetEditors = {
  basic: [],
};

export const PropertySettings = {};

export const WrapperTileConfig = {
  name: 'Rating Summary Card',
  defaultProps: {
  },
};

