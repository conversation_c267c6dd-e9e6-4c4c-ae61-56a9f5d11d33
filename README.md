Create a server and serve a html page that will accept commands

Modes of operation
==================

Running(dev)
------------
- in ui folder run `npm start`
- in root folder run `npm run watch`
- Update Editor.tsx in ReactNativeTSProjeect to point to the localhost:3000 port for the cli iframe

Running(prod)
-------------
- `npx --package=@apptile/cli apptile-cli`
- or to run it locally in prod mode. 
- do a build `npm run build` in root
- then run `npm start`
- change Editor.tsx to point to `PLUGIN_SERVER_URL` route

Publishing
----------
just run `npm publish --access public`

Downloading schema using apollo-ios-cli
---------------------------------------
The binary can be installed by creating an ios project and adding apollo-ios in it. Use ~/apolloswifttest/apollo-ios-cli on local
command to download: `~/apolloswifttest/apollo-ios-cli fetch-schema --path apollo-shopify-codegen-config.json --verbose`

Setting up supabase
-------------------
Use `npx supabase start` to start the containers
Virtual env for mcp created with `python3 -m venv ./supabasenv` and `souce supabasenv/bin/activate`
Then installed using `pip3 install supabase-mcp-server`
Then added configurations within ~/.config/supabase-mcp/.env

How to deploy to cli.apptile.io
===============================
- Push to main branch and copy the build number from github
- Update the build number in the repository: apptile-cli-devenv
- Check on this url for deployment status: **************/webhook/running-image