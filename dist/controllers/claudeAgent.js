"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.logReplacer = logReplacer;
exports.simpleChat = simpleChat;
exports.prompt = prompt;
const path_1 = __importDefault(require("path"));
const promises_1 = require("node:fs/promises");
const fs_1 = require("fs");
const js_sha256_1 = require("js-sha256");
const cliconfig_1 = require("../database/cliconfig");
const compiler_1 = require("./compiler");
const messageparsing_1 = require("./messageparsing");
const sdk_1 = __importDefault(require("@anthropic-ai/sdk"));
const index_js_1 = require("@modelcontextprotocol/sdk/client/index.js");
const stdio_js_1 = require("@modelcontextprotocol/sdk/client/stdio.js");
const chalk_1 = __importDefault(require("chalk"));
let claude = null;
let mcp = {
    graphql: null,
    supabaseClientMCPServer: null
};
function initialize() {
    return __awaiter(this, void 0, void 0, function* () {
        if (!claude) {
            const rec = (0, cliconfig_1.getAPIKey)("claude");
            if (rec) {
                claude = new sdk_1.default({ apiKey: rec.apikey });
            }
        }
        if (!mcp.supabaseClientMCPServer) {
            mcp.supabaseClientMCPServer = new index_js_1.Client({ name: "supabase-client-mcp-client", version: "1.0.0" });
            debugger;
            yield connectToMCPServer(path_1.default.resolve(__dirname, "supabase-client-mcp-server.ts"), mcp.supabaseClientMCPServer);
        }
        if (!mcp.graphql) {
            mcp.graphql = new index_js_1.Client({ name: "graphql-mcp-client", version: "1.0.0" });
            yield connectToMCPServer(path_1.default.resolve(__dirname, "graphql-mcp-server.ts"), mcp.graphql);
        }
        return claude;
    });
}
const transports = new Map();
const tools = [];
const toolsMCPMapping = {};
function connectToMCPServer(serverScriptPath, mcp, env) {
    return __awaiter(this, void 0, void 0, function* () {
        try {
            const isJS = serverScriptPath.endsWith(".js");
            const isPy = serverScriptPath.endsWith(".py");
            const isTS = serverScriptPath.endsWith(".ts");
            // if (!isJS && !isPy && !isTS) {
            //   throw new Error("Server script must be .js, .py or .ts file");
            // }
            let command;
            if (isJS) {
                command = process.execPath;
            }
            else if (isPy) {
                command = "python3";
            }
            else if (isTS) {
                command = path_1.default.resolve(__dirname, "..", "node_modules", ".bin", "ts-node");
            }
            else {
                command = serverScriptPath;
            }
            let transport;
            if (isJS || isPy || isTS) {
                transport = new stdio_js_1.StdioClientTransport({
                    command,
                    args: [serverScriptPath],
                    env
                });
            }
            else {
                transport = new stdio_js_1.StdioClientTransport({
                    command,
                    env
                });
            }
            transports.set(serverScriptPath, transport);
            yield mcp.connect(transport);
            // TODO: Hack because supabase mcp takes time to start. @yashwanth take a look
            // await new Promise((resolve) => {
            //   setTimeout(() => {
            //     resolve({});
            //   }, 25000);
            // });
            const toolResult = yield mcp.listTools();
            for (let i = 0; i < toolResult.tools.length; ++i) {
                const tool = toolResult.tools[i];
                tools.push({
                    name: tool.name,
                    description: tool.description,
                    input_schema: tool.inputSchema
                });
                toolsMCPMapping[tool.name] = mcp;
            }
            console.log(chalk_1.default.green("Connected to mcp server with tools: " + tools.map(it => it.name).join(",")));
        }
        catch (err) {
            console.error(chalk_1.default.red("Failed to connect to mcp server: " + serverScriptPath), err);
        }
    });
}
function narrowFormat(format) {
    return ['image/jpeg', 'image/png', 'image/gif', 'image/webp'].includes(format);
}
function logReplacer(key, value) {
    if (key === "data" && typeof value === "string" && value.length > 20) {
        return value.slice(0, 20) + "...[truncated]";
    }
    return value;
}
function simpleChat(messages) {
    return __awaiter(this, void 0, void 0, function* () {
        const claude = yield initialize();
        if (!claude) {
            console.error("Failed to initialize claude");
            return null;
        }
        else {
            const responseStream = claude.messages.stream({
                system: `
Assume the role of a programmer who helps with constructing graphql queries. You will be provided with some tools to interact with a graphql schema for a shopify store. You will use these tools to look for the appropriate fields that would satisfy the request and then construct a gql query. The user may ask for things that don't exist in the schema or database. If you don't find something just report that you couldn't find those specific things and create the query for the things you did find.

  # Description of how to use the tools

  ## enumerate_fields_at_path
  This tool will enumerate all the subfields of the field whose path is supplied. It also supports fetching the descriptions and arguments of the fields. The returned value contains information about the target field as well as all the fields from the root to the target field. 

  Once you have determined the answer the final reply should be a graphql schema that I can paste inside a gql\`\` tag followed with a typescript type that I can use to type the response that I will get by running the query.
`,
                messages,
                model: "claude-3-7-sonnet-20250219",
                max_tokens: 8000,
                tool_choice: {
                    type: 'auto',
                    disable_parallel_tool_use: true
                },
                tools: [
                    /*
                    {
                      name: "execute_query",
                      description: "Execute a query to check if it was successful or not",
                      input_schema: {
                        type: "object",
                        properties: {
                          query: {
                            type: "string",
                            description: "The graphql query to run. This will be passed to a gql tag by the function so the value should be just the query in SDL as a string that can be embedded into a gql tag as gql`${query}`. The result will be 'successful' or the 'error: <error message if any>'"
                          },
                          variables: {
                            type: "object",
                            description: "An object containing the variables to be passed to the query"
                          }
                        },
                        required: ["query", "variables"]
                      }
                    },
                    */
                    {
                        name: "enumerate_fields_at_path",
                        description: "Enumerate the fields for an object in the graphql schema. Think of it as a tree explorer. You can incrementally update the path argument and the function will return the fields of the object reachable via that path. The `includeDescriptions` and `includeArgumentsInfo` parameters determine if the function will also return the descriptions and arguments for the objects reached by the specified path in the schema. The `includeArgumentsInfo` parameter is more expensive compared to the `includeDescriptions` argument in terms of how many tokens are used to send the response. So use the `includeArgumentsInfo` parameter when you are sure you want to use the arguments for the obejct to build the query. Use the `includeDescriptions` argument to get more information about the fields you are looking at in a cheap way.",
                        input_schema: {
                            type: "object",
                            properties: {
                                path: {
                                    type: "string",
                                    description: "A dot separated string specifying the field in the schema whose fields are to be enumerated. If a blank string is passed the top level of the schema will be enumerated."
                                },
                                includeDescriptions: {
                                    type: "boolean",
                                    description: "Include the descriptions for the fields in the result"
                                },
                                includeArgumentsInfo: {
                                    type: "boolean",
                                    description: "Include all available information about the arguments applicable to the fields being returned. Set this to true sparingly to save tokens."
                                }
                            },
                            required: ["path", "includeDescriptions", "includeArguments"]
                        }
                    }
                ],
            });
            // console.log("Waiting for claude");
            return (0, messageparsing_1.ingestClaudeMessages)(responseStream);
        }
    });
}
function prompt(type, appId, assetName, userPrompt, res, model) {
    return __awaiter(this, void 0, void 0, function* () {
        var _a;
        console.log("[ANTHROPIC AGENT] starting");
        const imgTagStart = userPrompt.indexOf('<img');
        let finalizedPrompt = [];
        if (imgTagStart >= 0) {
            console.log("[ANTHROPIC AGENT] found image tag in prompt");
            const format = userPrompt.match(/<img\ssrc="data:(.+);base64,/);
            if (format && format[1]) {
                console.log("[ANTHROPIC AGENT] matching on image tag successful");
                const originalMessage = userPrompt;
                const dataStart = userPrompt.indexOf(";base64,") + ";base64,".length;
                const dataEnd = userPrompt.indexOf("\"", dataStart);
                userPrompt = userPrompt.slice(0, imgTagStart) +
                    " removed_image_to_save_tokens " +
                    userPrompt.slice(userPrompt.indexOf(">", dataEnd));
                if (narrowFormat(format[1])) {
                    finalizedPrompt.push({
                        type: 'image',
                        source: {
                            type: 'base64',
                            media_type: format[1],
                            data: originalMessage.slice(dataStart, dataEnd)
                        }
                    });
                }
                else {
                    console.error("Unacceptable image format detected in prompt. Dropping image: ", format[1]);
                }
                finalizedPrompt.push({
                    type: 'text',
                    text: userPrompt
                });
                console.log("[ANTHROPIC AGENT] Finalized prompt: ", JSON.stringify(finalizedPrompt, logReplacer, 2));
            }
            else {
                finalizedPrompt.push({
                    type: 'text',
                    text: userPrompt
                });
                console.log("[ANTHROPIC AGENT] Finalized prompt is text only because image format was weird: ", format, JSON.stringify(finalizedPrompt, logReplacer, 2));
            }
        }
        else {
            finalizedPrompt.push({
                type: 'text',
                text: userPrompt
            });
            console.log("[ANTHROPIC AGENT] Finalized text only prompt", JSON.stringify(finalizedPrompt, logReplacer, 2));
        }
        const claude = yield initialize();
        let result = '';
        if (!claude || !mcp) {
            result = 'No anthropic client found!';
            console.log("[ANTHROMPIC AGENT] Failed because no agent was found");
            if (!mcp) {
                console.log("[ANTHROPIC AGENT] Failed to connect to mcp client");
            }
        }
        else {
            const openedApp = (0, cliconfig_1.getOpenApp)(appId);
            if (openedApp) {
                const filePath = path_1.default.resolve(openedApp.repoPath, 'remoteCode', 'plugins', assetName, 'source', 'component.jsx');
                const fileStream = (0, fs_1.createWriteStream)(filePath, { flags: 'w' });
                res.write(`\nresult will be written to ${filePath}\n`);
                let chat = (0, cliconfig_1.getChat)(filePath, "claude", model);
                if (!chat) {
                    chat = (0, cliconfig_1.createChat)(filePath, type, "claude", model);
                }
                if (!chat) {
                    res.write(`\nfailed to create chat for: ${filePath}`);
                }
                else {
                    (0, cliconfig_1.createChatMessageWithContentAndRole)(chat.id, "user", JSON.stringify(finalizedPrompt));
                }
                const initialPrompt = yield (0, promises_1.readFile)(path_1.default.resolve(__dirname, '../systemPrompts/latest.html'), { encoding: 'utf8' });
                const messages = [
                // {role: 'user', content: [{type: 'text', text: initialPrompt}]}
                ];
                if (chat) {
                    // We remove all tool calls from history for claude 
                    const history = (0, cliconfig_1.getChatMessages)(chat.id, 'ASC');
                    for (let i = 0; i < history.length; ++i) {
                        const msg = history[i];
                        let parsedMessage;
                        try {
                            parsedMessage = JSON.parse(msg.content) || '<lost_data/>';
                            if (msg.role === "user" || msg.role === "assistant") {
                                if (!Array.isArray(parsedMessage) ||
                                    !['tool_use', 'tool_result'].includes(parsedMessage[0].type)) {
                                    messages.push({
                                        role: msg.role,
                                        content: parsedMessage
                                    });
                                }
                            }
                            else {
                                console.error("[ANTHROPIC_AGENT] unrecognized role in chat history", msg);
                            }
                        }
                        catch (err) {
                            parsedMessage = msg.content || '<lost_data/>';
                            if (msg.role === "user" || msg.role === "assistant") {
                                messages.push({
                                    role: msg.role,
                                    content: parsedMessage
                                });
                            }
                        }
                    }
                }
                try {
                    // TODO(gaurav): if input token count is larger than limit remove history messages
                    let prevTokens;
                    do {
                        const tokenCount = yield claude.messages.countTokens({
                            model,
                            system: initialPrompt,
                            messages
                        });
                        prevTokens = tokenCount.input_tokens;
                        if (prevTokens > 40000) {
                            const droppedMessage = messages.shift();
                            console.log("[ANTHROPIC AGENT] Dropping message because of token limit: ", JSON.stringify(droppedMessage, logReplacer, 2));
                        }
                    } while (prevTokens > 40000 && messages.length > 0);
                    let toolRun = false;
                    do {
                        console.log("[ANTHROPIC AGENT] Triggering completion");
                        const responseStream = claude.messages.stream({
                            system: initialPrompt,
                            model,
                            messages,
                            max_tokens: 16000,
                            tool_choice: {
                                type: 'auto',
                                disable_parallel_tool_use: true
                            },
                            tools: tools, // [
                            // {
                            //   name: "execute_query",
                            //   description: "Execute a query to check if it was successful or not",
                            //   input_schema: {
                            //     type: "object",
                            //     properties: {
                            //       query: {
                            //         type: "string",
                            //         description: "The graphql query to run. This will be passed to a gql tag by the function so the value should be just the query in SDL as a string that can be embedded into a gql tag as gql`${query}`. The result will be 'successful' or the 'error: <error message if any>'"
                            //       },
                            //       variables: {
                            //         type: "object",
                            //         description: "An object containing the variables to be passed to the query"
                            //       }
                            //     },
                            //     required: ["query", "variables"]
                            //   }
                            // },
                            // {
                            //   name: "enumerate_fields_at_path",
                            //   description: "Enumerate the fields for an object in the graphql schema. Think of it as a tree explorer. You can incrementally update the path argument and the function will return the fields of the object reachable via that path. The `includeDescriptions` and `includeArgumentsInfo` parameters determine if the function will also return the descriptions and arguments for the objects reached by the specified path in the schema. The `includeArgumentsInfo` parameter is more expensive compared to the `includeDescriptions` argument in terms of how many tokens are used to send the response. So use the `includeArgumentsInfo` parameter when you are sure you want to use the arguments for the obejct to build the query. Use the `includeDescriptions` argument to get more information about the fields you are looking at in a cheap way.",
                            //   input_schema: {
                            //     type: "object",
                            //     properties: {
                            //       path: {
                            //         type: "string",
                            //         description: "A dot separated string specifying the field in the schema whose fields are to be enumerated. If a blank string is passed the top level of the schema will be enumerated."
                            //       },
                            //       includeDescriptions: {
                            //         type: "boolean",
                            //         description: "Include the descriptions for the fields in the result"
                            //       },
                            //       includeArgumentsInfo: {
                            //         type: "boolean",
                            //         description: "Include all available information about the arguments applicable to the fields being returned. Set this to true sparingly to save tokens."
                            //       }
                            //     },
                            //     required: ["path", "includeDescriptions", "includeArguments"]
                            //   }
                            // }
                            // ] 
                        });
                        console.log("[ANTHROPIC_AGENT] starting ingestion of response stream");
                        const ingestedResponse = yield (0, messageparsing_1.ingestClaudeResponseStream)(responseStream, res, fileStream);
                        if (ingestedResponse === null || ingestedResponse === void 0 ? void 0 : ingestedResponse.messageText) {
                            messages.push({
                                role: "assistant",
                                content: [{ type: "text", text: ingestedResponse.messageText }]
                            });
                        }
                        if (ingestedResponse.status === "MaxTokensUsed") {
                            console.log("Claude stopped in the middle of generation. Waiting a few seconds and asking to continue");
                            yield new Promise(resolve => {
                                setTimeout(() => {
                                    resolve({});
                                }, 10000);
                            });
                            messages.push({
                                role: "user",
                                content: [{
                                        type: "text",
                                        text: "you stopped in the middle of generation. Please continue."
                                    }]
                            });
                            continue;
                        }
                        toolRun = !!(ingestedResponse === null || ingestedResponse === void 0 ? void 0 : ingestedResponse.toolInput);
                        if (toolRun) {
                            let inputVars = undefined;
                            if (ingestedResponse.toolInput.partialInput) {
                                inputVars = JSON.parse(ingestedResponse.toolInput.partialInput);
                                console.log("[ANTHROPIC_AGENT] Starting tool run ", ingestedResponse.toolInput.tool, inputVars);
                            }
                            else {
                                inputVars = {};
                                console.log("[ANTHROPIC_AGENT] Starting tool run with no arguments for: ", ingestedResponse.toolInput.tool);
                            }
                            messages.push({
                                role: "assistant",
                                content: [{
                                        id: ingestedResponse.toolInput.id,
                                        input: inputVars,
                                        name: ingestedResponse.toolInput.tool,
                                        type: "tool_use"
                                    }]
                            });
                            if (chat) {
                                (0, cliconfig_1.createChatMessageWithContentAndRole)(chat.id, "assistant", JSON.stringify([{
                                        id: ingestedResponse.toolInput.id,
                                        input: inputVars,
                                        name: ingestedResponse.toolInput.tool,
                                        type: "tool_use"
                                    }]));
                            }
                            debugger;
                            let result;
                            if (toolsMCPMapping[ingestedResponse.toolInput.tool]) {
                                result = yield toolsMCPMapping[ingestedResponse.toolInput.tool].callTool({
                                    name: ingestedResponse.toolInput.tool,
                                    arguments: inputVars
                                });
                            }
                            else {
                                console.error(chalk_1.default.red("Could not run tool!"));
                                result = { content: ["Tool unavailable! Try again later!"] };
                            }
                            // switch (ingestedResponse.toolInput.tool) {
                            //   case "search_schema":
                            //     result = await searchSchema(inputVars.searchtext, {
                            //     startingPoint: inputVars.options.startingPoint,
                            //     maxDepth: 1,
                            //     includeArgs: false, // inputVars.options.includeArgs, 
                            //     includeDescriptions: inputVars.options.includeDescriptions
                            //   });
                            //   break;
                            //   case "describe_schema_upto_level":
                            //     result = await getQueriesUptoLevel(inputVars.maxDepth);
                            //   break;
                            //   case "get_schema_for_path":
                            //     result = await getSchemaForPath(inputVars.path, inputVars.maxDepth);
                            //   break;
                            //   case "enumerate_fields_at_path": 
                            //     result = await enumerateFields(inputVars.path, inputVars.includeDescriptions, inputVars.includeArgumentsInfo); 
                            //   break;
                            //   case "execute_query":
                            //     result = await executeQuery(inputVars.query, inputVars.variables);
                            //   break;
                            //   default:
                            //     console.log("Unknown tool!");
                            //   return;
                            // }
                            debugger;
                            let typedResult;
                            if (typeof result.content === "string") {
                                typedResult = result.content;
                            }
                            else if (Array.isArray(result.content)) {
                                typedResult = result.content;
                            }
                            else {
                                console.log(chalk_1.default.red("tool returned unexpected response: "), result);
                                typedResult = "tool failed!";
                            }
                            // if (stringifiedResult.length > 40000) {
                            //   stringifiedResult = stringifiedResult.slice(0, 40000) + "--------rest of the response was truncated due to token limit--------";
                            // }
                            messages.push({
                                role: "user",
                                content: [
                                    {
                                        type: "tool_result",
                                        tool_use_id: ingestedResponse.toolInput.id,
                                        content: typedResult
                                    }
                                ]
                            });
                            if (chat) {
                                (0, cliconfig_1.createChatMessageWithContentAndRole)(chat.id, "user", JSON.stringify([
                                    {
                                        type: "tool_result",
                                        tool_use_id: ingestedResponse.toolInput.id,
                                        content: typedResult
                                    }
                                ]));
                            }
                        }
                    } while (toolRun);
                }
                catch (err) {
                    console.error("[ANTHROPIC AGENT] Failed when communicating with anthropic", err);
                    res.write('\nerror happened\n');
                    res.write(((_a = err === null || err === void 0 ? void 0 : err.error) === null || _a === void 0 ? void 0 : _a.message) || '\nerror has no description\n');
                }
                // TODO(gaurav): No need to do all this when the chat completion step failed
                fileStream.end();
                res.write(`\npreparing to compile generated code\n`);
                let newCode = yield (0, promises_1.readFile)(filePath, { encoding: 'utf8' });
                if (chat) {
                    (0, cliconfig_1.createChatMessageWithContentAndRole)(chat.id, 'assistant', newCode);
                }
                else {
                    console.error("[ANTHROPIC AGENT] Chat session is active but no chat found!");
                }
                let startOfCodeMarker = newCode.indexOf('```javascript');
                if (startOfCodeMarker >= 0) {
                    startOfCodeMarker += '```javascript'.length;
                }
                else if (newCode.indexOf('```jsx') >= 0) {
                    startOfCodeMarker = newCode.indexOf('```jsx') + '```jsx'.length;
                }
                else {
                    startOfCodeMarker = 0;
                }
                let endOfCodeBlock = newCode.indexOf('```', startOfCodeMarker);
                if (endOfCodeBlock < 0) {
                    endOfCodeBlock = newCode.length;
                }
                newCode = newCode.slice(startOfCodeMarker, endOfCodeBlock);
                yield (0, promises_1.writeFile)(filePath, newCode);
                // compile it
                const entries = yield (0, promises_1.readdir)(path_1.default.resolve(openedApp.repoPath, 'remoteCode/plugins'), { withFileTypes: true });
                const plugins = entries.filter(it => it.isDirectory()).map(it => it.name);
                const remoteCode = path_1.default.resolve(openedApp.repoPath, 'remoteCode');
                res.write('compiling...\n');
                const compiledDirectoryName = (0, js_sha256_1.sha256)(plugins.join('_')).toString();
                yield (0, compiler_1.compileMultiplePlugins)(remoteCode, plugins, compiledDirectoryName);
                res.write('compilation finished\n');
                res.end();
            }
            else {
                result = 'App not found!';
                console.log("[ANTHROPIC AGENT] Failed because no app was found");
            }
        }
        return result;
    });
}
