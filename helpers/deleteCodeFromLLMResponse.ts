export function deleteCodeFromLLMResponse(response: string): string {
    let startOfCodeMarker = response.indexOf('```json');
    if (startOfCodeMarker >= 0) {
        startOfCodeMarker += '```json'.length
    } 

    let endOfCodeBlock = response.indexOf('```', startOfCodeMarker);
    if (endOfCodeBlock < 0) {
        endOfCodeBlock = response.length;
    }

    console.log(startOfCodeMarker,endOfCodeBlock)

    const responseWithoutCode = response.slice(0,startOfCodeMarker)+response.slice(endOfCodeBlock);
    return responseWithoutCode
}